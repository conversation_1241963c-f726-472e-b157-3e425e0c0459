import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UpdateTitleService {
  get title$() {
    return this.titleSubject.asObservable();
  }

  private titleSubject = new Subject<any>();

  constructor(private title: Title) {}

  /**
   * 修改页面title
   *
   * @param {ActivatedRoute} activeRoute 路由
   * @param {string} title
   */
  updateTitle(activeRoute: ActivatedRoute, title: string) {
    activeRoute.snapshot.data['title'] = title;
    this.title.setTitle(title);
    this.titleSubject.next();
  }
}
