import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-shell',
  templateUrl: './shell.component.html',
  styleUrls: ['./shell.component.scss']
})
export class ShellComponent implements OnInit {
  isDashBoardPage = false;
  isReportPage = false;

  constructor(private router: Router) {}

  ngOnInit() {
    this.listenRouterChange();
  }

  listenRouterChange() {
    this.isDashBoardPage = this.router.url == '/informationBoard';
    this.isReportPage = this.router.url == '/dataReport';
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const url = event.url.split('/');
        this.isDashBoardPage = this.router.url == '/informationBoard';
        this.isReportPage = this.router.url == '/dataReport';
      }
    });
  }
}
