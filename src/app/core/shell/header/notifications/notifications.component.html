<!-- <div
  class="notify"
  nz-button
  nzType="link"
  nz-popover
  nzPopoverPlacement="bottomRight"
  [nzPopoverContent]="notifyTpl"
  [(nzPopoverVisible)]="isShowNotify"
  nzPopoverTrigger="click"
  nzPopoverOverlayClassName="notify-popover-container"
  (click)="toggleNotify()"
>
  <nz-badge [nzDot]="unreadMessageCount > 0">
    <i class="iot iot-notify"></i>
  </nz-badge>
</div> -->

<!-- <ng-template #notifyTpl> -->
<div *ngIf="notifyList.length > 0">
  <div class="close-div">
    <img src="/assets/font/guanbitongzhi.png" />
    <span>{{ '关闭所有消息' | translate }}</span>
    <button type="button" class="close" aria-label="Close" (click)="closeAll()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div>
    <div class="notify-container" *ngFor="let item of notifyList; let index = index">
      <div class="notify-body">
        <div class="notify-item">
          <img src="/assets/font/jingbao.png" />
          <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ item.vehicleNumber | translate }}">
            {{ item.vehicleNumber | translate }}
          </span>
        </div>
        <div class="notify-item last">
          <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ item.alertTypeName | translate }}">
            {{ item.alertTypeName | translate }}
          </span>
          <span
            nz-tooltip
            nzTooltipPlacement="top"
            nzTooltipTitle="{{ item.alertTime | localDate : accountSetting.dateTimeHourMinuteFormat }}"
          >
            {{ item.alertTime | localDate : accountSetting.dateTimeHourMinuteFormat }}
          </span>
        </div>
        <img src="/assets/font/notification_read.png" (click)="readMess(index)" />
      </div>
    </div>
  </div>
</div>
<div class="hide">
  <audio #nofitySound src="/assets/media/app/audio/notify.mp3"></audio>
</div>
