import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
  HostListener
} from '@angular/core';
import { Subject, Subscription, timer } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import { environment } from '@env/environment';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { LoggerFactory } from '../../../logger-factory.service';
import { Logger } from '../../../../core/logger.service';

@Component({
  selector: 'app-notifications, [app-notifications]',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotificationsComponent implements OnInit, OnDestroy {
  log: Logger;
  accountSetting = accountSetting;

  unreadMessageCount = 0;
  unreadMessages: any[] = [];
  socket: any;
  interval: any;
  notifyList: any[] = [];

  isShowNotify = false;
  notifySubject = new Subject<boolean>();
  notifySubscription: Subscription;
  showNotifyTimer$: Subscription;
  closeTimer: any;

  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 初始重连延迟1秒
  private isReconnecting = false; // 防止重复重连
  private isConnecting = false; // 防止并发连接

  get iconClass(): string {
    const val = 'm-nav__link-icon';
    return this.unreadMessageCount === 0 ? val : val + ' m-animate-shake';
  }

  get badgeClass(): string {
    const val = 'm-nav__link-badge m-badge m-badge--dot m-badge--dot-small m-badge--danger';
    return this.unreadMessageCount === 0 ? val : val + ' m-animate-blink';
  }

  @ViewChild('nofitySound') nofitySound!: ElementRef;

  constructor(private cd: ChangeDetectorRef, private elementRef: ElementRef, private loggerFactory: LoggerFactory) {
    this.log = this.loggerFactory.getLogger();
    this.onNotify();
  }

  ngOnInit() {
    this.webSockek();
  }

  ngOnDestroy() {
    this.isShowNotify = false;
    this.clearIsShowNotify$();
    this.notifySubscription.unsubscribe();
    if (this.interval) {
      window.clearInterval(this.interval);
      this.interval = null;
    }
    if (this.socket) {
      this.socket.close();
    }
  }

  onNotify() {
    this.notifySubscription = this.notifySubject.pipe(debounceTime(300)).subscribe((isSound) => {
      this.showNotify();
      if (isSound) {
        this.nofitySound.nativeElement.play();
      }
      this.cd.markForCheck();
    });
  }

  webSockek() {
    if (typeof WebSocket === 'undefined') {
      this.log.error('您的浏览器不支持WebSocket');
      return;
    }

    this.connectWebSocket();
  }

  private connectWebSocket() {
    if (this.isConnecting) return; // 防止并发调用
    this.isConnecting = true;

    // 只有不是CLOSED的时候才关闭，避免重复触发onclose事件
    if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
      this.socket.close();
    }
    this.socket = null;

    const userIdKey = `oidc.user:${environment.odic.config.authority}:${environment.odic.config.client_id}`;
    let userId = sessionStorage.getItem(userIdKey);
    userId = JSON.parse(userId).profile.sub;
    const socketUrl = `${environment.api.websocket.baseUrl}${userId}/${new Date().getTime()}`;

    this.socket = new WebSocket(socketUrl);

    this.socket.onopen = (msg: any) => {
      this.isConnecting = false; // 连接完成后，重置状态
      this.isReconnecting = false;
      this.reconnectAttempts = 0; // 重置重连次数
      this.reconnectDelay = 1000; // 重置重连延迟

      // 保持连接定时器
      this.interval = setInterval(() => {
        if (this.socket.readyState === WebSocket.OPEN) {
          try {
            this.socket.send('w');
          } catch (error) {
            this.log.error('发送心跳包失败', error);
            this.reconnect();
          }
        }
      }, 5_000);
    };

    this.socket.onclose = (event: CloseEvent) => {
      // 清理心跳定时器
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
      this.isConnecting = false;

      this.log.warn('WebSocket连接关闭', event);
      this.reconnect();
    };

    this.socket.onerror = (error: Event) => {
      // 清理心跳定时器
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
      this.isConnecting = false;

      this.log.error('WebSocket错误', error);
      this.reconnect();
    };

    // 获得消息事件
    this.socket.onmessage = (msg: any) => {
      const param = JSON.parse(msg.data);
      console.log(param, 2222);
      if (param.code === '2001' && param.data) {
        const item = param.data;
        item.alertTime = new Date(item.alertTime);
        this.notifyList = [item, ...this.notifyList].slice(0, 20);
        console.log('🚀 ~ NotificationsComponent ~ webSockek ~ this.notifyList:', this.notifyList);

        // 定义递归函数
        const removeFirstMessage = () => {
          if (this.notifyList.length > 0) {
            this.notifyList.splice(0, 1);
            console.log('🚀 ~ NotificationsComponent ~ webSockek ~ this.notifyList:', this.notifyList);
            this.cd.markForCheck();
            // 递归调用，设置下一次执行
            this.closeTimer = setTimeout(removeFirstMessage, 10000);
          }
        };

        // 启动第一次执行
        this.closeTimer = setTimeout(removeFirstMessage, 10000);

        this.notifySubject.next(item.isSound);
        this.cd.markForCheck();
      }
    };
  }

  private reconnect() {
    if (this.isReconnecting) return; // 防止重复重连
    this.isReconnecting = true;

    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      // 使用指数退避算法增加重连延迟
      this.reconnectDelay = Math.min(30000, this.reconnectDelay * 2);

      this.log.info(
        `尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，延迟: ${this.reconnectDelay}ms`
      );

      setTimeout(() => {
        this.isReconnecting = false;
        this.connectWebSocket();
      }, this.reconnectDelay);
    } else {
      this.log.error('达到最大重连次数，停止重连');
      this.isReconnecting = false; // 重连失败后，重置状态,避免锁死
    }
  }

  showNotify() {
    this.isShowNotify = true;
    this.clearIsShowNotify$();
    this.showNotifyTimer$ = timer(60 * 1000).subscribe(() => {
      this.isShowNotify = false;
      this.clearIsShowNotify$();
      this.cd.markForCheck();
    });
  }

  hideNotify() {
    this.isShowNotify = false;
    this.clearIsShowNotify$();
  }

  toggleNotify() {
    // this.isShowNotify = !this.isShowNotify;
    this.clearIsShowNotify$();
  }

  clearIsShowNotify$() {
    if (this.showNotifyTimer$) {
      this.showNotifyTimer$.unsubscribe();
    }
  }
  // 信息已读
  readMess(index: number) {
    this.notifyList.splice(index, 1);
  }
  // 关闭所有消息
  closeAll() {
    this.notifyList = [];
  }
}
