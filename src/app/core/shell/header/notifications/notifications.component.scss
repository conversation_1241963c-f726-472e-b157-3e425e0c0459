:host {
  height: 100%;
}

.notify {
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 22px;
  cursor: pointer;

  .iot {
    font-size: 22px;
  }
}

.notify-container {
  width: 365px;
  margin-bottom: 10px;
  position: relative;
  margin-right: -50px;
  font-size: 12px;
  border-radius: 6px 6px 6px 6px;
  box-shadow: 0px 6px 30px 5px #e5e5e5, 0px 8px 10px -5px #e5e5e5 inset;
  &:nth-child(5n + 1):not(:first-child) {
    position: absolute;
    top: 55px;
  }
  &:nth-child(5n + 2):not(:nth-child(2)) {
    position: absolute;
    top: 145px;
  }
  &:nth-child(5n + 3):not(:nth-child(3)) {
    position: absolute;
    top: 235px;
  }
  &:nth-child(5n + 4):not(:nth-child(4)) {
    position: absolute;
    top: 326px;
  }
  &:nth-child(5n + 5):not(:nth-child(5)) {
    position: absolute;
    top: 416px;
  }
}

.notify-body {
  min-height: 72px;
  background-color: #fff;
  overflow-y: auto;
  position: relative;
  padding: 15px;
  > img {
    position: absolute;
    right: 16px;
    top: 24px;
    width: 24px;
    cursor: pointer;
  }

  .notify-item {
    img {
      width: 17px;
      margin-right: 10px;
      vertical-align: top;
      position: relative;
      top: 1px;
    }

    span {
      font-weight: bold;
      color: #191919;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 16px;
      font-family: PingFangSC-Semibold;
    }
    &.last span {
      color: #42474e;
      font-weight: 400;
      font-size: 13px;
      padding-left: 27px;
      padding-top: 4px;
      font-family: AlibabaPuHuiTi-Regular;
      display: inline-block;
      &:first-child {
        padding-left: 34px;
        position: relative;
        top: -2px;
      }
    }
  }
}

.notify-footer {
  border-top: 1px solid #cfd1d7;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  line-height: 25px;
  text-align: center;
  color: #838fae;
  background-color: #fff;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;

  img {
    width: 75px;
  }

  span {
    color: #333;
  }
}

::ng-deep {
  .notify-popover-container {
    .ant-popover-inner {
      background-color: transparent;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-popover-arrow {
      display: none;
    }
  }
}

.close-div {
  width: 365px;
  height: 42px;
  border-radius: 6px 6px 6px 6px;
  box-shadow: 0px 6px 30px 5px #e5e5e5, 0px 8px 10px -5px #e5e5e5 inset;
  background-image: linear-gradient(332deg, rgba(255, 255, 255, 1), rgba(235, 243, 255, 1));
  font-size: 16px;
  color: #191919;
  padding-left: 53px;
  line-height: 42px;
  margin-bottom: 10px;
  img {
    width: 15px;
    margin-right: 5px;
  }
  span {
    font-weight: 600;
    position: relative;
    top: 2px;
  }
  button {
    margin-right: 9px;
    margin-top: 7px;
    span {
      font-weight: 24px;
    }
  }
}
