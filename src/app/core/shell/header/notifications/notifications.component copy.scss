:host {
  height: 100%;
}

.notify {
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 22px;
  cursor: pointer;

  .iot {
    font-size: 22px;
  }
}

.notify-container {
  width: 365px;
  position: relative;
  margin-right: -50px;
  font-size: 12px;
}

.notify-container::after {
  content: "";
  position: absolute;
  top: -4px;
  right: 54px;
  width: 16px;
  height: 16px;
  background: #2f3747;
  transform: rotate(-45deg);
}

.notify-header {
  display: grid;
  grid-template-columns: 30% 35% 35%;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  background: #2f3747;

  span {
    padding: 6px 0 6px 12px;
    font-weight: bold;
    color: #d4e0ff;
  }
}

.notify-body {
  height: 90px;
  background-color: #fff;
  overflow-y: auto;

  .notify-item {
    display: grid;
    grid-template-columns: 30% 35% 35%;

    &:hover {
      background: #f2f2f2;
    }

    span {
      padding: 6px 0 6px 12px;
      font-weight: bold;
      color: #575e72;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.notify-footer {
  border-top: 1px solid #cfd1d7;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  line-height: 25px;
  text-align: center;
  color: #838fae;
  background-color: #fff;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;

  img {
    width: 75px;
  }

  span {
    color: #333;
  }
}

::ng-deep {
  .notify-popover-container {
    .ant-popover-inner {
      background-color: transparent;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-popover-arrow {
      display: none;
    }
  }
}
