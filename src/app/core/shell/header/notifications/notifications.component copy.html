<div
  class="notify"
  nz-button
  nzType="link"
  nz-popover
  nzPopoverPlacement="bottomRight"
  [nzPopoverContent]="notifyTpl"
  [(nzPopoverVisible)]="isShowNotify"
  nzPopoverTrigger="click"
  nzPopoverOverlayClassName="notify-popover-container"
  (click)="toggleNotify()"
>
  <nz-badge [nzDot]="unreadMessageCount > 0">
    <i class="iot iot-notify"></i>
  </nz-badge>
</div>

<ng-template #notifyTpl>
  <div class="notify-container">
    <div class="notify-header">
      <span translate>项</span>
      <span translate>类型</span>
      <span translate>日期</span>
    </div>

    <div class="notify-body">
      <div class="notify-item" *ngFor="let item of notifyList">
        <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ item.vehicleNumber | translate }}">
          {{ item.vehicleNumber | translate }}
        </span>
        <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ item.alertTypeName | translate }}">
          {{ item.alertTypeName | translate }}
        </span>
        <span
          nz-tooltip
          nzTooltipPlacement="top"
          nzTooltipTitle="{{ item.alertTime | localDate : accountSetting.dateTimeHourMinuteFormat }}"
        >
          {{ item.alertTime | localDate : accountSetting.dateTimeHourMinuteFormat }}
        </span>
      </div>

      <div class="no-data" *ngIf="!notifyList || notifyList.length === 0">
        <img src="/assets/font/noDate.png" />
        <span class="no-data-title">{{ '暂无数据' | translate }}</span>
      </div>
    </div>
    <div class="notify-footer" translate>注意！仅显示最新的 20 条通知。</div>
  </div>
</ng-template>

<div class="hide">
  <audio #nofitySound src="/assets/media/app/audio/notify.mp3"></audio>
</div>
