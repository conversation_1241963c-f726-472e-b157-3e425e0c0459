import { Component, OnInit, <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { fromEvent, of, timer } from 'rxjs';
import { buffer, catchError, filter, map, switchMap } from 'rxjs/operators';
import { BsModalService } from 'ngx-bootstrap/modal';

import { LoggerFactory } from '@app/core/logger-factory.service';
import { Logger } from '@app/core/logger.service';

import { AuthenticationService } from '@app/core/authentication/authentication.service';
import { ProfileService } from '@app/core/profile/profile.service';

import { Language } from '@app/account-settings/models/account';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { I18nService } from '@app/core/i18n.service';
import { LogoService } from '@app/shared/services/logo.service';
import { AccountService } from '@app/account-settings/services/account.service';
import { NoticeboardService } from '@app/noticeboard-management/services/noticeboard.service';

import { NoticeboardParams } from '@app/noticeboard-management/models/noticeboard';
import { NoticeboardDisplayComponent } from '@app/noticeboard-management/components/noticeboard-display/noticeboard-display.component';

import { Theme } from '@app/shared/models/theme';
import { ThemeService } from '@app/shared/services/theme.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, AfterViewInit, OnDestroy {
  log: Logger;

  Language = Language;
  language: Language = accountSetting.language;
  logo = accountSetting.logo;
  hasReminderMenu = false; // 是否有消息提醒弹窗
  isShow = false;

  get username(): string {
    const credentials = this.authenticationService.credentials;
    return credentials ? credentials.username : null;
  }

  constructor(
    private accountService: AccountService,
    private authenticationService: AuthenticationService,
    private el: ElementRef,
    private i18nService: I18nService,
    private loggerFactory: LoggerFactory,
    private logoService: LogoService,
    private modalService: BsModalService,
    private noticeboardService: NoticeboardService,
    private profileService: ProfileService,
    private router: Router,
    private themeService: ThemeService
  ) {
    this.log = this.loggerFactory.getLogger(``);
    this.logoService.logo$.subscribe((url) => (this.logo = url));
  }

  ngOnInit() {
    this.changeHasSpecialMenu();
    this.getNoticeBoardList();
  }

  ngAfterViewInit() {
    // this.headerLogoTripleClick();
    // this.changeLang();
    // this.changeTheme();
  }

  ngOnDestroy() {}

  logout() {
    this.authenticationService.logout().subscribe(() => this.router.navigate(['/login']));
  }

  changeLanguage(language: Language, sync: boolean = true) {
    this.language = language;
    this.i18nService.switchLanguage(language);
    if (sync) {
      accountSetting.language = language;
      this.accountService.setAccountSetting({ language }).subscribe();
    }
  }

  changeHasSpecialMenu() {
    const moduleTree = localStorage.getItem(`moduleTree`);
    const menus$ = moduleTree ? of(JSON.parse(moduleTree)) : this.profileService.getMenus();
    menus$.subscribe((menus) => {
      if (!Array.isArray(menus)) {
        return;
      }
      // 若moduleTree无值，则将menus中的visible为true的项存储到moduleTree中
      // if (!moduleTree) {
      //   const menuItems = menus.filter((item: any) => item.visible);
      //   localStorage.setItem('moduleTree', JSON.stringify(menuItems));
      // }
      menus
        .filter((item) => item.enabled)
        .forEach((item) => {
          switch (item.ngUrl) {
            case '/reminder':
              this.hasReminderMenu = true;
              break;
            default:
              break;
          }
        });
    });
  }

  /**
   * 获取当前用户未读公告
   */
  getNoticeBoardList() {
    const params: NoticeboardParams = {
      effectiveTime: new Date().getTime().toString(),
      isRead: 0,
      pageIndex: 1,
      pageSize: 1_000_000
    };
    this.noticeboardService
      .getPageList(params)
      .pipe(
        map((res) => {
          if (!res.success || !res.data || !Array.isArray(res.data.records)) {
            return [];
          }
          return res.data.records;
        }),
        catchError(() => of([]))
      )
      .subscribe((list) => {
        if (list.length < 1) {
          return;
        }
        this.modalService.show(NoticeboardDisplayComponent, {
          initialState: { list },
          class: 'modal-noticeboard',
          ignoreBackdropClick: true
        });
      });
  }

  // /**
  //  * 隐藏功能
  //  * 1秒内连续点击logo3次,切换语言,不同步用户设置的语言类型
  //  */
  // private headerLogoTripleClick() {
  //   const headerLogo = this.el.nativeElement.querySelector('.logo');
  //   const click$ = fromEvent(headerLogo, 'click');
  //   click$
  //     .pipe(
  //       buffer(click$.pipe(switchMap(() => timer(1000)))),
  //       map((list) => list.length),
  //       filter((len) => len === 3)
  //     )
  //     .subscribe(() => {
  //       let language: Language = Language['en-US'];
  //       // 切换语言枚举值
  //       switch (this.language) {
  //         // 若当前是英文，枚举改为中文枚举值
  //         case Language['en-US']:
  //           language = Language['zh-CN'];
  //           break;
  //         // 若当前是中文，枚举改为西班牙文枚举值
  //         case Language['zh-CN']:
  //           language = Language['es-MX'];
  //           break;
  //         // 若当前是西班牙文，枚举改为英文枚举值
  //         case Language['es-MX']:
  //           language = Language['en-US'];
  //           break;
  //         default:
  //           break;
  //       }
  //       this.changeLanguage(language, false);
  //     });
  // }

  /**
   * 点击图标，切换语言,不同步用户设置的语言类型
   */
  changeLang(language: number) {
    this.changeLanguage(language, false);
  }

  /**
   * 改变主题
   */
  private changeTheme() {
    const headerLogo = this.el.nativeElement.querySelector('.logo');
    const click$ = fromEvent(headerLogo, 'click');
    click$.subscribe(() => {
      switch (accountSetting.theme) {
        case Theme.Dark:
          accountSetting.theme = Theme.Light;
          break;
        case Theme.Light:
          accountSetting.theme = Theme.Dark;
          break;
        default:
          break;
      }
      this.themeService.changeTheme(accountSetting.theme);
    });
  }
}
