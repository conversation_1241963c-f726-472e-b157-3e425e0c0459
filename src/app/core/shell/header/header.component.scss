:host {
  width: 100%;
  height: 50px;
  position: fixed;
  top: 0;
  z-index: 101;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
}

.header-left {
  .logo {
    margin-right: 16px;
    overflow: hidden;

    img {
      max-height: 60px;
      object-fit: contain;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      margin-left: 10px;
    }
  }
}

.header-menu {
  width: 0;
  height: 100%;
  flex: 1;
  overflow-x: auto;

  &::-webkit-scrollbar {
    height: 5px !important;
  }
}

.header-right {
  display: flex;
  padding: 0 20px;
  height: 100%;

  > ul {
    height: 100%;
    display: flex;
    align-items: stretch;
    gap: 15px 20px;
  }
}

.change-btn {
  width: 24px;
  background: url(/assets/font/language-change.png) center no-repeat;
  background-size: 24px 24px;
  &:hover {
    cursor: pointer;
  }
}

.profile-container {
  width: 80px;
  position: relative;
}

.profile-container::before {
  content: "";
  position: absolute;
  top: -6px;
  right: 32px;
  width: 16px;
  height: 16px;
  background: #fff;
  transform: rotate(-45deg);
}

.profile-body {
  padding: 12px 10px;
  background-color: #fff;
}

.profile-content {
  li {
    display: flex;
    align-items: center;
    padding: 5px;
    font-size: 12px;
    cursor: pointer;
    color: #333333;
  }
  div {
    width: 100%;
    text-align: center;
  }
}

::ng-deep {
  .profile-popover-container {
    .ant-popover-inner {
      background-color: transparent;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-popover-arrow {
      display: none;
    }
  }
}
