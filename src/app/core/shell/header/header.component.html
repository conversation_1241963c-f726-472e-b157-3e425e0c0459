<!-- header-->
<header>
  <div class="header-left">
    <div class="logo">
      <img *ngIf="logo" [src]="logo" alt="" />
    </div>
  </div>
  <div class="header-menu">
    <app-top-menu></app-top-menu>
  </div>
  <div class="header-right">
    <div
      class="change-btn"
      nz-popover
      nzPopoverPlacement="bottom"
      [nzPopoverContent]="profileTpl"
      nzPopoverTrigger="click"
      nzPopoverOverlayClassName="profile-popover-container"
    ></div>

    <ng-template #profileTpl>
      <div class="profile-container">
        <div class="profile-body">
          <ul class="profile-content">
            <li (click)="changeLang(Language['zh-CN'])">
              <div>
                {{ '中文' | translate }}
              </div>
            </li>
            <li (click)="changeLang(Language['en-US'])">
              <div>
                {{ '英文' | translate }}
              </div>
            </li>
            <li (click)="changeLang(Language['es-MX'])">
              <div>
                {{ '西班牙文' | translate }}
              </div>
            </li>
          </ul>
        </div>
      </div>
    </ng-template>

    <ul>
      <li *ngIf="hasReminderMenu">
        <app-notifications></app-notifications>
      </li>
      <!-- TODO: 固定为英文,如需动态切换,取消下面注释 -->
      <!-- <li class="pointer">
        <img class="size30"  src="/assets/font/chinaese20_20.png" *ngIf="language === Language['zh-CN']" (click)="changeLanguage(Language['en-US'])"/>
        <img class="size30"  src="/assets/font/english20_20.png" *ngIf="language === Language['en-US']" (click)="changeLanguage(Language['zh-CN'])"/>
      </li> -->
      <li>
        <app-profile></app-profile>
      </li>
    </ul>
  </div>
</header>
