import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { ProfileService } from '../../../profile/profile.service';

@Component({
  selector: 'app-top-menu, [app-top-menu]',
  templateUrl: './top-menu.component.html',
  styleUrls: ['./top-menu.component.scss']
})
export class TopMenuComponent implements OnInit {
  menuItems: any;
  currentMenu: string = `/${window.location.pathname.split('/')[1]}`;

  constructor(private router: Router, private profileService: ProfileService) {}

  ngOnInit() {
    this.listenRouterChange();
    this.getMenus();
  }

  // 监听路由变化，更换选中状态
  listenRouterChange() {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const url = event.url.split('/');
        this.currentMenu = '/' + url[1];
      }
    });
  }

  private getMenus(): void {
    this.profileService.getMenus().subscribe((menuItems) => {
      this.menuItems = menuItems.filter((item: any) => item.visible);
      localStorage.setItem('moduleTree', JSON.stringify(menuItems));
    });
  }
}
