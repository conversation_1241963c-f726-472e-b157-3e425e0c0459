@mixin app-top-menu-theme($theme) {
  $is-dark-theme: map-get($theme, is-dark);
  $primary: map-get($theme, primary);
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);

  $top-menu-bg-color: if($is-dark-theme, #eaebf4, #f4f6fa);
  $top-menu-board-bg-color: if($is-dark-theme, #041127, #f4f6fa);
  $top-menu-color: if($is-dark-theme, #fff, #6a7797);
  $top-menu-icon-color: if($is-dark-theme, #6a7797, #6a7797);

  app-header {
    a {
      color: $top-menu-color;

      i {
        color: $top-menu-icon-color;
      }

      &:hover,
      &.active {
        color: mat-color($primary);

        i {
          color: mat-color($primary);
        }
      }

      &.active {
        background-color: $top-menu-bg-color;
      }

      &.active-board {
        background-color: $top-menu-board-bg-color;
      }
    }
  }
}
