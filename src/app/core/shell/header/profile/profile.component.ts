import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';

import { Subscription, of } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { BsModalService } from 'ngx-bootstrap/modal';

import { environment } from '@env/environment';
import { CreateSubscriptionService } from '@app/shared';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { Menu } from '@app/shared/models/type';

import { AuthenticationService } from '@core/authentication/authentication.service';
import { AuthenticationOAuth2Service } from '@core/authentication/authentication-oauth2.service';
import { ProfileService, Profile } from '@core/profile/profile.service';

import { ContactUsComponent } from '../contact-us/contact-us.component';
import { AccountSettingsModalComponent } from '@app/account-settings/components/account-settings-modal/account-settings-modal.component';
import { NoticeboardListComponent } from '@app/noticeboard-management/components/noticeboard-list/noticeboard-list.component';
import { NoticeboardSettingsListComponent } from '@app/noticeboard-management/components/noticeboard-settings-list/noticeboard-settings-list.component';
import { UserGuideComponent } from '@app/user-guide-management/user-guide/user-guide.component';
import { DownloadComponent } from '@app/shared/components/download/download.component';
import { MessageService } from '@app/core/message.service';

declare const $: any;

@Component({
  selector: 'app-profile, [app-profile]',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  log: Logger;
  idleHandle$: Subscription;
  messageHandel$: Subscription;
  messageReceive$: Subscription;
  signalRConnection$: Subscription;
  processing = true;
  isAuthenticated: boolean;
  profile: Profile = { displayName: '', username: '' };
  isShowProfile = false;

  hasAccountSettingsMenu = false; // 是否有账号设置菜单
  hasNoticeboardMenu = false; // 是否有公告栏菜单
  hasNoticeboardSettingsMenu = false; // 是否有公告栏管理菜单
  hasUserGuideMenu = false; // 是否有用户指引菜单
  hasDownloadMenu = false; // 是否有下载中心菜单

  @ViewChild(`warningAudio`) warningAudio: ElementRef;

  constructor(
    private router: Router,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private profileService: ProfileService,
    private authenticationService: AuthenticationService,
    private subscriptionService: CreateSubscriptionService,
    private messageService: MessageService,
    private authenticationOAuth2Service: AuthenticationOAuth2Service
  ) {
    this.log = this.loggerFactory.getLogger('Profile');
  }

  ngOnInit() {
    if (this.authenticationService.isUsing()) {
      this.isAuthenticated = this.authenticationService.isAuthenticated();
    }
    if (this.authenticationOAuth2Service.isUsing()) {
      this.isAuthenticated = this.authenticationOAuth2Service.isAuthenticated();
    }
    if (this.isAuthenticated) {
      this.getProfile();
    }
    this.idleHandle$ = this.subscriptionService.idle$.subscribe(() => this.logout());
    this.changeHasSpecialMenu();

    // 关闭提示音
    this.messageHandel$ = this.subscriptionService.messageHandel$.subscribe(() => {
      this.warningAudio.nativeElement.pause();
    });
    // 开启提示音
    this.messageReceive$ = this.subscriptionService.messageReceive$.subscribe(() => {
      if (this.warningAudio) {
        this.warningAudio.nativeElement.currentTime = 0;
        this.warningAudio.nativeElement.play();
      }
    });
    // signalR连接
    this.messageService.initSignalR();
    this.signalRConnection$ = this.subscriptionService.signalRConnection$.subscribe(() => {
      this.connectSignalR();
    });
  }

  ngOnDestroy() {
    this.idleHandle$.unsubscribe();
    if (this.messageReceive$) {
      this.messageReceive$.unsubscribe();
    }
    if (this.messageHandel$) {
      this.messageHandel$.unsubscribe();
    }
    if (this.signalRConnection$) {
      this.signalRConnection$.unsubscribe();
    }
  }
  connectSignalR() {
    if (this.messageService.hasPermission()) {
      this.messageService.addDataListener();
    }
  }
  logout() {
    // If usercenter authentication
    if (this.authenticationService.isUsing()) {
      this.authenticationService
        .logout()
        .pipe(
          finalize(() => {
            this.checkoutMenuActive().then(() => {
              localStorage.clear();
              sessionStorage.clear();
              this.router.navigate(['/login']);
            });
          })
        )
        .subscribe();
    }

    // If micro service authentication
    if (this.authenticationOAuth2Service.isUsing()) {
      if (environment.authentication.useServiceV1) {
        this.authenticationService.logout();
      }
      this.authenticationOAuth2Service.signout().then(() => {
        localStorage.clear();
        sessionStorage.clear();
        this.router.navigate(['/']);
      });
    }
  }

  checkoutMenuActive() {
    return new Promise((resolve) => {
      const menuOpen = $('.m-brand__toggler--active');
      sessionStorage.setItem('logout', 'logout');
      sessionStorage.removeItem('currentRouting');
      if (menuOpen.length > 0) {
        $('#m_aside_left_minimize_toggle').trigger('click');
      }
      resolve('');
    });
  }

  openAccountSettingModal() {
    this.modalService.show(AccountSettingsModalComponent, {
      class: 'modal-account-settings',
      ignoreBackdropClick: true
    });
  }

  openNoticeboardModal() {
    this.modalService.show(NoticeboardListComponent, { class: 'modal-noticeboard', ignoreBackdropClick: true });
  }

  openNoticeboardSettingsModal() {
    this.modalService.show(NoticeboardSettingsListComponent, { class: 'modal-noticeboard', ignoreBackdropClick: true });
  }

  openUserGuideModal() {
    this.modalService.show(UserGuideComponent, { class: 'modal-lg modal-user-guide', ignoreBackdropClick: true });
  }

  goToLabelPage() {
    this.router.navigateByUrl('/label');
  }

  openContackUsModal() {
    this.modalService.show(ContactUsComponent, {
      class: 'modal-mlg modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((value: any) => {
      if (value) {
      }
      onHidden.unsubscribe();
    });
  }

  changeHasSpecialMenu() {
    const moduleTree = localStorage.getItem(`moduleTree`);
    const menus$ = moduleTree ? of(JSON.parse(moduleTree)) : this.profileService.getMenus();
    menus$.subscribe((menus) => {
      if (!Array.isArray(menus)) {
        return;
      }
      // 若moduleTree无值，则将menus中的visible为true的项存储到moduleTree中
      // if (!moduleTree) {
      //   const menuItems = menus.filter((item: any) => item.visible);
      //   localStorage.setItem('moduleTree', JSON.stringify(menuItems));
      // }
      menus
        .filter((item) => item.enabled)
        .forEach((item: Menu) => {
          switch (item.ngUrl) {
            case '/accountSettings':
              this.hasAccountSettingsMenu = true;
              break;
            case '/noticeboard':
              this.hasNoticeboardMenu = true;
              break;
            case '/noticeboardSettings':
              this.hasNoticeboardSettingsMenu = true;
              break;
            case '/userGuide':
              this.hasUserGuideMenu = true;
              break;
            case '/downloadCenter':
              this.hasDownloadMenu = true;
              break;
            default:
              break;
          }
        });
    });
  }

  downloadModal() {
    this.modalService.show(DownloadComponent, {
      class: 'modal-account-settings',
      ignoreBackdropClick: true
    });
  }

  private getProfile(): void {
    this.profileService.getProfile().subscribe(
      (profile) => {
        this.profile = profile;
      },
      (error) => this.log.error(error)
    );
  }
}
