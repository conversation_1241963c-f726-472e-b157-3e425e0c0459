:host {
  height: 100%;
}

.profile {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 0 10px;
  cursor: pointer;

  .avatar {
    width: 26px;
  }
}

.profile-container {
  width: 260px;
  position: relative;
}

.profile-container::before {
  content: "";
  position: absolute;
  top: -6px;
  right: 45px;
  width: 16px;
  height: 16px;
  background: rgba(73, 138, 255, 1);
  transform: rotate(-45deg);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 0 15px;
  padding: 20px;
  border-radius: 6px 6px 0 0;
  background: url("/assets/font/personalCenter-background.png");
  background-size: cover;

  .avatar {
    width: 52px;
  }

  .profile-info {
    display: flex;
    flex-direction: column;

    h3 {
      color: #fff;
      font-size: 18px;
    }

    p {
      color: #d9d9d9;
    }
  }
}

.profile-body {
  background-color: #fff;
}

.profile-content {
  li {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    font-size: 12px;
    cursor: pointer;
    color: #333333;

    img {
      width: 20px;
      height: 20px;
    }

    span {
      margin-left: 12px;
    }
  }
}

::ng-deep {
  .profile-popover-container {
    .ant-popover-inner {
      background-color: transparent;
    }

    .ant-popover-inner-content {
      padding: 0;
    }

    .ant-popover-arrow {
      display: none;
    }
  }
}
