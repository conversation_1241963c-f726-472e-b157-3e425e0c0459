<audio controls="controls" #warningAudio hidden preload>
  <source src="/assets/font/audio/notify.mp3" type="audio/mp3" />
  <source src="/assets/font/audio/notify.wav" type="audio/wav" />
  Your browser does not support this audio format.
</audio>

<div
  class="profile"
  nz-popover
  nzPopoverPlacement="bottomRight"
  [nzPopoverContent]="profileTpl"
  [(nzPopoverVisible)]="isShowProfile"
  nzPopoverTrigger="click"
  nzPopoverOverlayClassName="profile-popover-container"
>
  <img src="/assets/font/userImg.png" class="avatar" />
  <span>{{ profile.displayName }}</span>
</div>

<ng-template #profileTpl>
  <div class="profile-container" (click)="isShowProfile = !isShowProfile">
    <div class="profile-header">
      <img src="/assets/font/userImgBig.png" class="avatar" />
      <div class="profile-info">
        <h3>
          {{ profile.displayName }}
        </h3>
        <p>
          {{ profile.username }}
        </p>
      </div>
    </div>
    <div class="profile-body">
      <ul class="profile-content">
        <li (click)="goToLabelPage()">
          <img src="/assets/font/labelManagement-icon.svg" />
          <span>
            {{ '标签管理' | translate }}
          </span>
        </li>
        <li *ngIf="hasAccountSettingsMenu" (click)="openAccountSettingModal()">
          <img src="/assets/font/settings.svg" />
          <span>
            {{ '账号设置' | translate }}
          </span>
        </li>
        <li (click)="openContackUsModal()">
          <img src="/assets/font/help-icon.svg" />
          <span>
            {{ '联系我们' | translate }}
          </span>
        </li>
        <li *ngIf="hasNoticeboardMenu" (click)="openNoticeboardModal()">
          <img src="/assets/media/app/img/icons/noticeboard.png" />
          <span>
            {{ '公告栏' | translate }}
          </span>
        </li>
        <li *ngIf="hasNoticeboardSettingsMenu" (click)="openNoticeboardSettingsModal()">
          <img src="/assets/media/app/img/icons/noticeboard-settings.png" />
          <span>
            {{ '公告栏管理' | translate }}
          </span>
        </li>
        <li *ngIf="hasUserGuideMenu" (click)="openUserGuideModal()">
          <img src="/assets/media/app/img/icons/user-guide.png" />
          <span>
            {{ '用户指引' | translate }}
          </span>
        </li>
        <li (click)="downloadModal()" *ngIf="hasDownloadMenu">
          <img src="/assets/media/app/img/icons/download.png" />
          <span translate>
            {{ '下载中心' | translate }}
          </span>
        </li>
        <li (click)="logout()">
          <img src="/assets/font/signOut-icon.svg" />
          <span translate>
            {{ '退出登录' | translate }}
          </span>
        </li>
      </ul>
    </div>
  </div>
</ng-template>
