import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core/route.service';

const routes: Routes = RouteExtensions.withShell([
  {
    path: 'informationBoard',
    loadChildren: () =>
      import('../dash-board-management/dash-board-management.module').then((m) => m.DashBoardManagementModule),
    data: { title: '数据看板' }
  },
  {
    path: 'positionManagement',
    loadChildren: () =>
      import('../position-management/position-management.module').then((m) => m.PositionManagementModule),
    data: { title: '定位监控' }
  },
  {
    path: 'fence',
    loadChildren: () => import('../fence-management/fence-management.module').then((m) => m.FenceManagementModule),
    data: { title: '围栏管理' }
  },
  {
    path: 'earlywarning',
    loadChildren: () => import('../events-management/events-management.module').then((m) => m.EventsManagementModule),
    data: { title: '事件管理' }
  },
  // // data-management
  {
    path: 'vehicleManagement',
    loadChildren: () =>
      import('../data-management/vehicle-management/vehicle-management.module').then((m) => m.VehicleManagementModule),
    data: { title: '车辆管理' }
  },
  {
    path: 'deviceManagement',
    loadChildren: () =>
      import('../data-management/device-management/device-management.module').then((m) => m.DeviceManagementModule),
    data: { title: '设备管理' }
  },
  {
    path: 'driverManagement',
    loadChildren: () =>
      import('../data-management/driver-management/driver-management.module').then((m) => m.DriverManagementModule),
    data: { title: '驾驶员管理' }
  },
  // data-management
  {
    path: 'dataReport',
    loadChildren: () =>
      import('../reports-management/reports-management.module').then((m) => m.ReportsManagementModule),
    data: { title: '数据报告' }
  },
  {
    path: 'notificationManagement',
    loadChildren: () =>
      import('../notification-management/notification-management.module').then((m) => m.NotificationManagementModule),
    data: { title: '配置管理' }
  },
  {
    path: 'commandsManagement',
    loadChildren: () =>
      import('../commands-management/commands-management.module').then((m) => m.CommandsManagementModule),
    data: { title: '指令发送' }
  },
  {
    path: 'video',
    loadChildren: () => import('../video-management/video-management.module').then((m) => m.VideoManagementModule),
    data: { title: '视频' }
  },
  {
    path: 'label',
    loadChildren: () => import('../label-management/label-management.module').then((m) => m.LabelManagementModule),
    data: { title: '标签管理' }
  },
  {
    path: 'customerBillingPlan',
    loadChildren: () =>
      import('../customer-billing-plan/customer-billing-plan.module').then((m) => m.CustomerBillingPlanModule),
    data: { title: '客户账户管理' }
  },
  {
    path: 'deviceBillingPlan',
    loadChildren: () =>
      import('../device-billing-plan/device-billing-plan.module').then((m) => m.DeviceBillingPlanModule),
    data: { title: '设备计费管理' }
  },
  {
    path: 'customerDeviceBilling',
    loadChildren: () =>
      import('../customer-device-billing/customer-device-billing.module').then((m) => m.CustomerDeviceBillingModule),
    data: { title: '客户设备计费管理' }
  },
  {
    path: 'accident',
    loadChildren: () =>
      import('../opportunity-business/opportunity-index/opportunity-index.module').then(
        (m) => m.OpportunityIndexModule
      ),
    data: { title: '事故管理' }
  },
  {
    path: 'rescue',
    loadChildren: () => import('../rescue/rescue.module').then((m) => m.RescueModule),
    data: { title: '救援管理' }
  }
]);

const commonPage: Routes = [
  {
    path: 'common-video',
    loadChildren: () => import('../common-video/common-video.module').then((m) => m.CommonVideoModule),
    data: { breadcrumb: '视频插件', title: '视频插件' }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes), RouterModule.forChild(commonPage)],
  exports: [RouterModule],
  providers: []
})
export class CoreRoutingModule {}
