export class ComplexStorage implements Storage {
  // NOT implement
  [name: string]: any;
  // NOT implement
  length: number;

  constructor(private _stores: Array<Storage> = new Array<Storage>()) {}

  clear(): void {
    this._stores.forEach((s) => s.clear());
  }

  getItem(key: string): string {
    for (let i = 0; i < this._stores.length; i++) {
      const value = this._stores[i].getItem(key);
      if (value) {
        return value;
      }
    }
    return undefined;
  }

  key(index: number): string {
    for (let i = 0; i < this._stores.length; i++) {
      const value = this._stores[i].key(index);
      if (value) {
        return value;
      }
    }
    return undefined;
  }

  removeItem(key: string): void {
    this._stores.forEach((s) => s.removeItem(key));
  }

  setItem(key: string, value: string): void {
    this._stores.forEach((s) => s.setItem(key, value));
  }
}
