import { Log, WebStorageStateStore } from 'oidc-client';
import { ComplexStorage } from './complex-storage';

export class WebStorageStateStoreEx extends WebStorageStateStore {
  // tslint:disable-next-line: typedef
  constructor({ prefix = 'oidc.', store = localStorage } = {}) {
    const stores = new Array<Storage>();
    stores.push(store);
    if (store !== sessionStorage) {
      stores.push(sessionStorage);
    }
    super({ prefix: prefix, store: new ComplexStorage(stores) });

    Log.debug('WebStorageStateStore.type', 'WebStorageStateStoreEx');
  }
}
