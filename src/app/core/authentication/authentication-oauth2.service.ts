import { Injectable } from '@angular/core';
import { UserManager, User, Log } from 'oidc-client';

import { environment } from '@env/environment';
import { WebStorageStateStoreEx } from './web-storage-state-store-ex';

@Injectable()
export class AuthenticationOAuth2Service {
  private _credentials: User;
  private _userManager: UserManager;

  constructor() {
    Log.logger = console;
    const config = environment.odic.config as any;
    config.stateStore = new WebStorageStateStoreEx();
    this._userManager = new UserManager(config);

    if (environment.odic.config[`silent_redirect_uri`]) {
      this._userManager.events.addAccessTokenExpiring(() => {
        this._userManager.signinSilent().then((user) => {
          this.resetUserProfile(user);
          this.setCredentials(user);
        });
      });
    }
  }
  // 刷新拿到一个新的token
  reNewToken(): Promise<User> {
    return this._userManager.signinSilent();
    // .then(
    //   (user) => {
    //     resolve(user.access_token);
    //   }
    // );
  }

  isUsing(): boolean {
    return environment.authentication.type === 'oauth2';
  }

  isNewUserCenter(): boolean {
    return environment.userCenterVersion.version === 'userCenterV2';
  }

  signin(): Promise<void> {
    return this._userManager.signinRedirect();
  }

  signinCallback(): Promise<void> {
    return this._userManager.signinRedirectCallback().then((user: any) => {
      this.resetUserProfile(user);
      this.setCredentials(user);
    });
  }

  signout(): Promise<void> {
    return this._userManager.signoutRedirect();
  }

  /**
   * Checks is the user is authenticated.
   * @return {boolean} True if the user is authenticated.
   */
  isAuthenticated(): boolean {
    return !!this.credentials;
  }

  get claims(): any {
    return this.credentials.profile;
  }

  /**
   * Gets the user credentials.
   * @return {Credentials} The user credentials or null if the user is not authenticated.
   */
  get credentials(): any {
    return this._credentials;
  }

  getAuthorizationHeaderValue(): string {
    return this._credentials ? `${this._credentials.token_type} ${this._credentials.access_token}` : null;
  }

  resetUserProfile(user: User) {
    if (!this._userManager.settings.loadUserInfo) {
      // 解析角色
      const token = this._userManager[`_joseUtil`].parseJwt(user.access_token);
      user.profile = token.payload;
    }
  }

  /**
   * Sets the user credentials.
   * The credentials may be persisted across sessions by setting the `remember` parameter to true.
   * Otherwise, the credentials are only persisted for the current session.
   * @param {Credentials=} credentials The user credentials.
   */
  private setCredentials(credentials?: User) {
    this._credentials = credentials || null;
  }
}
