import { Inject, Injectable, LOCALE_ID } from '@angular/core';

import { TranslateService, LangChangeEvent } from '@ngx-translate/core';
import i18next from 'i18next';

import { enUS, zhCN, es } from 'date-fns/locale';

import { environment } from '@env/environment';
import { Language } from '@app/account-settings/models/account';
import { en_US, NzI18nService, zh_CN, es_ES } from 'ng-zorro-antd/i18n';
import { LanguageMonitorService } from '@app/shared/services/language-monitor.service';

/**
 * Pass-through function to mark a string for translation extraction.
 * Running `npm translations:extract` will include the given string by using this.
 * @param s The string to extract for translation.
 * @return The same string.
 */
export function extract(s: string) {
  return s;
}

/**
 * 多语言切换
 * angular, antd, wangeditor富文本 等其他插件库多语言在该服务控制
 */

@Injectable({
  providedIn: 'root'
})
export class I18nService {
  wangeditor = {
    lang: 'en',
    i18next
  };

  defaultLanguage: string = environment.defaultLanguage;
  supportedLanguages: string[] = environment.supportedLanguages;

  language: string = this.defaultLanguage;

  constructor(
    private translateService: TranslateService,
    private nzI18nService: NzI18nService,
    private languageMonitorService: LanguageMonitorService,
    @Inject(LOCALE_ID) public localeId: string
  ) {
    console.log(this.localeId);
    this.localeId = 'en-US';
  }

  init() {
    this.switchLanguage(Language[this.language]);
    this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      console.log('onLangChange', event);
    });
  }

  switchLanguage(language: Language) {
    if (!this.isSupported(language)) {
      language = Language[this.defaultLanguage];
    }
    this.language = Language[language];
    switch (language) {
      case Language['zh-CN']:
        this.localeId = 'zh-CN';
        this.translateService.use('zh-CN');
        this.nzI18nService.setLocale(zh_CN);
        this.nzI18nService.setDateLocale(zhCN);
        this.wangeditor.lang = 'zh-CN';
        window.localStorage.setItem('lang', 'zh-CN');
        break;
      case Language['en-US']:
        this.localeId = 'en-US';
        this.translateService.use('en-US');
        this.nzI18nService.setLocale(en_US);
        this.nzI18nService.setDateLocale(enUS);
        this.wangeditor.lang = 'en';
        window.localStorage.setItem('lang', 'en-US');
        break;
      case Language['es-MX']:
        this.localeId = 'es-MX';
        this.translateService.use('es-MX');
        this.nzI18nService.setLocale(es_ES);
        this.nzI18nService.setDateLocale(es);
        this.wangeditor.lang = 'es';
        window.localStorage.setItem('lang', 'es-MX');
        break;
    }
    this.languageMonitorService.langChangeSubject.next(Language[language]);
  }

  isSupported(language: Language): boolean {
    return this.supportedLanguages.includes(Language[language]);
  }
}
