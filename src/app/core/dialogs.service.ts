import { Injectable } from '@angular/core';

import { Observable, Subscriber } from 'rxjs';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import swal, { SweetAlertIcon } from 'sweetalert2';

import { SweetAlert2Service } from '../shared/dialogs/sweetalert2.service';

@Injectable()
export class Dialogs {
  constructor(private toastr: ToastrService, private sweetAlert2: SweetAlert2Service) {}

  /** show successful toast */
  success(message: string, title?: string, optionsOverride?: any): ActiveToast<any> {
    return this.toastr.success(message, title, optionsOverride);
  }

  /** show info toast */
  info(message: string, title?: string, optionsOverride?: any): ActiveToast<any> {
    return this.toastr.info(message, title, optionsOverride);
  }

  /** show warning toast */
  warning(message: string, title?: string, optionsOverride?: any): ActiveToast<any> {
    return this.toastr.warning(message, title, optionsOverride);
  }

  /** show error toast */
  error(message: string, title?: string, optionsOverride?: any): ActiveToast<any> {
    return this.toastr.error(message, title, optionsOverride);
  }

  clear(toastId: any) {
    return this.toastr.clear(toastId);
  }

  /** show sweetalert2 as window.alert */
  alert(message: string, title?: string, icon?: SweetAlertIcon): Promise<any> {
    return this.sweetAlert2.show(icon || 'info', message, title, undefined, {
      showCancelButton: false
    });
  }

  /** show sweetalert2 as window.confirm */
  confirm(message: string, title?: string, icon?: SweetAlertIcon, config?: any): Observable<any> {
    let language = window.localStorage.getItem('lang');
    let confirmButtonText = '';
    let cancelButtonText = '';
    if (language === 'zh-CN') {
      confirmButtonText = '确定';
      cancelButtonText = '取消';
    }
    if (language === 'en-US') {
      confirmButtonText = 'OK';
      cancelButtonText = 'CANCEL';
    }
    if (language === 'es-MX') {
      confirmButtonText = 'OK';
      cancelButtonText = 'Cancelar';
    }

    return new Observable((subscriber: Subscriber<Response>) => {
      this.sweetAlert2
        .show(icon || 'question', message, title, undefined, {
          showConfirmButton: true,
          confirmButtonText: confirmButtonText,
          cancelButtonText: cancelButtonText,
          showCancelButton: true,
          ...(config ? config : {})
        })
        .then(function (result: any) {
          if (result.value) {
            subscriber.next(result);
          } else {
            subscriber.error(result);
          }
        });
    });
  }

  /** show sweetalert2 as a wait dialog */
  wait(param?: string | boolean): Promise<any> {
    if (param === undefined) {
      param = '处理中，请稍后...';
    }

    const html = `<p><i class="fa fa-spinner fa-pulse fa-4x fa-fw text-info"></i></p>
                  <p class="text-muted">${param}</p>`;

    if (typeof param === 'string') {
      return this.sweetAlert2.show(null, param, null, html, {
        showConfirmButton: false,
        showCancelButton: false
      });
    } else if (typeof param === 'boolean' && param === false) {
      // tslint:disable-next-line: deprecation
      swal.close();
    }
  }
}
