import { Inject, Injectable, InjectionToken, Injector, Optional } from '@angular/core';
import { HttpClient, HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { CacheInterceptor } from './cache.interceptor';
import { ApiPrefixInterceptor } from './api-prefix.interceptor';
import { ErrorHandlerInterceptor } from './error-handler.interceptor';

import { environment } from '@env/environment';
import { AuthenticationService, Credentials } from '@core/authentication/authentication.service';
import { toURLSearchParams } from './http-helper';
import { Auth2TokenInterceptor } from './auth2-token.interceptor';

// HttpClient is declared in a re-exported module, so we have to extend the original module to make it work properly
// (see https://github.com/Microsoft/TypeScript/issues/13897)
declare module '@angular/common/http/http' {
  // Augment HttpClient with the added configuration methods from HttpService, to allow in-place replacement of
  // HttpClient with HttpService using dependency injection
  export interface HttpClient {
    /**
     * Enables caching for this request.
     * @param {boolean} forceUpdate Forces request to be made and updates cache entry.
     * @return {HttpClient} The new instance.
     */
    cache(forceUpdate?: boolean): HttpClient;

    /**
     * Skips default error handler for this request.
     * @return {HttpClient} The new instance.
     */
    skipErrorHandler(): HttpClient;

    /**
     * Do not use API prefix for this request.
     * @return {HttpClient} The new instance.
     */
    disableApiPrefix(): HttpClient;

    skipAuth2TokenInterceptor(): HttpClient;
  }
}

// From @angular/common/http/src/interceptor: allows to chain interceptors
class HttpInterceptorHandler implements HttpHandler {
  constructor(private next: HttpHandler, private interceptor: HttpInterceptor) {}

  handle(request: HttpRequest<any>): Observable<HttpEvent<any>> {
    return this.interceptor.intercept(request, this.next);
  }
}

/**
 * Allows to override default dynamic interceptors that can be disabled with the HttpService extension.
 * Except for very specific needs, you should better configure these interceptors directly in the constructor below
 * for better readability.
 *
 * For static interceptors that should always be enabled (like ApiPrefixInterceptor), use the standard
 * HTTP_INTERCEPTORS token.
 */
export const HTTP_DYNAMIC_INTERCEPTORS = new InjectionToken<HttpInterceptor>('HTTP_DYNAMIC_INTERCEPTORS');

/**
 * Extends HttpClient with per request configuration using dynamic interceptors.
 */

declare const URI: any;

@Injectable()
export class HttpService extends HttpClient {
  private _apiSettings: any;

  constructor(
    private httpHandler: HttpHandler,
    private injector: Injector,
    @Optional()
    @Inject(HTTP_DYNAMIC_INTERCEPTORS)
    private interceptors: HttpInterceptor[] = []
  ) {
    super(httpHandler);
    this._apiSettings = environment.api[environment.api.default];

    if (!this.interceptors) {
      // Configure default interceptors that can be disabled here
      this.interceptors = [
        this.injector.get(ApiPrefixInterceptor),
        this.injector.get(Auth2TokenInterceptor),
        this.injector.get(ErrorHandlerInterceptor)
      ];
    }
  }

  cache(forceUpdate?: boolean): HttpClient {
    const cacheInterceptor = this.injector.get(CacheInterceptor).configure({ update: forceUpdate });
    return this.addInterceptor(cacheInterceptor);
  }

  skipErrorHandler(): HttpClient {
    return this.removeInterceptor(ErrorHandlerInterceptor);
  }

  disableApiPrefix(): HttpClient {
    return this.removeInterceptor(ApiPrefixInterceptor);
  }

  skipAuth2TokenInterceptor(): HttpClient {
    return this.removeInterceptor(Auth2TokenInterceptor);
  }

  // Override the original method to wire interceptors when triggering the request.
  request(method?: any, url?: any, options?: any): any {
    const handler = this.interceptors.reduceRight(
      (next, interceptor) => new HttpInterceptorHandler(next, interceptor),
      this.httpHandler
    );

    // url = this._apiSettings.baseUrl + url;
    const authenticationService: AuthenticationService = this.injector.get(AuthenticationService);
    const credentials: Credentials = authenticationService.isAuthenticated() ? authenticationService.credentials : null;
    const token: string = credentials == null ? 'null' : credentials.token;

    if (!options.headers) {
      // let language = '';
      const language = window.localStorage.getItem('lang');
      // if (lang === 'zh-CN') {
      //   language = 'zh-CN';
      // }
      // if (lang === 'en-US') {
      //   language = 'en-US';
      // }
      // if (lang === 'es-MX') {
      //   language = 'es-MX';
      // }
      options.headers = new HttpHeaders({ 'Accept-Language': language });
    }

    if (options && options != null && options.params && options.params != null) {
      if (!options.params['map']) {
        options.params = toURLSearchParams(options.params);
      }
    }
    const regex = new RegExp('^(http://|https://|//)');
    if (!regex.test(url) && !url.includes('assets/i18n')) {
      url = this._apiSettings.baseUrl + url;
      if (this._apiSettings.withHeaders) {
        // You can customize the 'headers' here.
        options.headers = options.headers.set('AppKey', this._apiSettings.appKey).set('AuthToken', token);
      } else {
        const urlParser: any = URI(url);
        if (!urlParser.hasQuery('AppKey')) {
          urlParser.addSearch('AppKey', this._apiSettings.appKey);
        }
        if (!urlParser.hasQuery('AuthToken')) {
          urlParser.addSearch('AuthToken', token);
        }
      }
    }
    // const regex = new RegExp('^(http://|https://|//)');
    // if (!regex.test(url)) {
    //   url = this._apiSettings.baseUrl + url;

    //   if (this._apiSettings.withHeaders) {
    //     // You can customize the 'headers' here.
    //     options.headers = options.headers.set('AppKey', this._apiSettings.appKey).set('AuthToken', token);
    //   } else {
    //     const urlParser: any = URI(url);
    //     if (!urlParser.hasQuery('AppKey')) {
    //       urlParser.addSearch('AppKey', this._apiSettings.appKey);
    //     }
    //     if (!urlParser.hasQuery('AuthToken')) {
    //       urlParser.addSearch('AuthToken', token);
    //     }
    //   }
    // }

    return new HttpClient(handler).request(method, url, options);
  }

  private removeInterceptor(interceptorType: Function): HttpService {
    return new HttpService(
      this.httpHandler,
      this.injector,
      this.interceptors.filter((i) => !(i instanceof interceptorType))
    );
  }

  private addInterceptor(interceptor: HttpInterceptor): HttpService {
    return new HttpService(this.httpHandler, this.injector, this.interceptors.concat([interceptor]));
  }
}
