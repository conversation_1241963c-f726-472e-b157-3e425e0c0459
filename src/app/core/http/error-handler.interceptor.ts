import { Injectable, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';

import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { AuthenticationService } from '@core/authentication/authentication.service';

/**
 * Adds a default error handler to all requests.
 */
@Injectable()
export class ErrorHandlerInterceptor implements HttpInterceptor {
  log: Logger;

  constructor(private router: Router, private injector: Injector, private loggerFactory: LoggerFactory) {
    this.log = this.loggerFactory.getLogger('Http Error');
  }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(catchError((error) => this.errorHandler(error)));
  }

  // Customize the default error handler here if needed
  private errorHandler(response: HttpEvent<any>): Observable<HttpEvent<any>> {
    const authenticationService: AuthenticationService = this.injector.get(AuthenticationService);
    const lang = window.localStorage.getItem('lang');

    if (!lang) {
      return;
    }

    if (
      response &&
      response['status'] &&
      response['status'] === 401 &&
      location.pathname.split('/')[1] !== 'sharelocation'
    ) {
      if (authenticationService.isUsing()) {
        let data = '';
        if (lang === 'zh-CN') {
          data = '未认证，跳转登录页...';
        }
        if (lang === 'en-US') {
          data = 'The system authentication timed out, please login again.';
        }
        if (lang === 'es-MX') {
          data = 'No autenticado, saltar a la página de inicio de sesión';
        }
        this.log.debug(data);
        this.router.navigate(['/']).then(() => {
          window.location.reload();
        });
      } else {
        window.location.reload();
      }
    }

    let message = '';

    if (response && response['status']) {
      switch (response['status']) {
        case 400:
          throw response;
        // case 401:
        //   message = 'The system authentication timed out, please login again.';
        //   break;
        // case 403:
        //   message = 'The system authentication failed, please login again.';
        //   break;
        case 404:
          if (lang === 'zh-CN') {
            message = '访问的数据（页面）不存在。';
          }
          if (lang === 'en-US') {
            message = 'Operation failed, please try again later.';
          }
          if (lang === 'es-MX') {
            message = 'Los datos accedidos (página) no existen.';
          }
          break;
        case 503:
          if (lang === 'zh-CN') {
            message = '服务器服务无效，请联系系统管理员。';
          }
          if (lang === 'en-US') {
            message = 'Operation failed, please try again later or contact the administrator.';
          }
          if (lang === 'es-MX') {
            message = 'Error del servidor, póngase en contacto con el administrador del sistema.';
          }
          break;
        case 500:
          if (lang === 'zh-CN') {
            message = '服务器错误，请联系系统管理员。';
          }
          if (lang === 'en-US') {
            message = 'Operation failed, please try again later or contact the administrator.';
          }
          if (lang === 'es-MX') {
            message = 'Error del servidor, póngase en contacto con el administrador del sistema.';
          }
          break;
      }
    }
    this.log.error(message);
    throw response;
  }
}
