import { Injectable, Injector } from '@angular/core';
import { HttpEvent, HttpInterceptor, Http<PERSON><PERSON>ler, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';

import { AuthenticationOAuth2Service } from '../authentication/authentication-oauth2.service';

@Injectable()
export class Auth2TokenInterceptor implements HttpInterceptor {
  constructor(private injector: Injector) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const update = this.attachRequestAuthInfo(request);
    request = request.clone(update);
    return next.handle(request);
  }

  attachRequestAuthInfo(request: HttpRequest<any>): { url: string; setHeaders: { [name: string]: string | string[] } } {
    const authenticationOAuth2Service: AuthenticationOAuth2Service = this.injector.get(AuthenticationOAuth2Service);
    const setHeaders: { [key: string]: string } = {};
    const authorization = authenticationOAuth2Service.getAuthorizationHeaderValue();
    if (authorization && !request.headers.has('Authorization')) {
      setHeaders['Authorization'] = authorization;
    }
    return { url: request.url, setHeaders };
  }
}
