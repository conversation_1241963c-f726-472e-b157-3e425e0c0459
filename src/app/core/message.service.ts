import { Injectable } from '@angular/core';

import * as signalR from '@microsoft/signalr';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { WebApiResultResponse } from './http/web-api-result-response';
import { LoggerFactory } from './logger-factory.service';
import { Logger } from './logger.service';

import { AuthenticationService, Credentials } from './authentication/authentication.service';
import { AuthenticationOAuth2Service } from './authentication/authentication-oauth2.service';
import { CreateSubscriptionService } from '@app/shared/services/create-subscription.service';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '@env/environment';

@Injectable()
export class MessageService extends WebApiResultResponse {
  log: Logger;

  tip: any = {
    clueCrash: '您有新的事故线索，请及时处理。',
    opportunityCrash: '您有新的事故商机，请及时处理。',
    opportunityRoadRescue: '您有新的道路救援请求，请及时处理。'
  };

  private _credentials: Credentials;
  private hubConnection: signalR.HubConnection;
  private hubDebounce = new Subject<string>(); // 重连防抖动

  constructor(
    private authenticationService: AuthenticationService,
    private authenticationOAuth2Service: AuthenticationOAuth2Service,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private subscriptionService: CreateSubscriptionService
  ) {
    super();
    this.log = this.loggerFactory.getLogger();

    if (this.authenticationService.isUsing()) {
      this._credentials = this.authenticationService.credentials;
    }

    if (this.authenticationOAuth2Service.isUsing()) {
      const claims = this.authenticationOAuth2Service.credentials;
      this._credentials = {
        username: claims.username,
        token: claims.access_token
      };
    }
  }
  isHasOpportunity: boolean;
  isHasClue: boolean;
  hasPermission() {
    const moduleTree = JSON.parse(localStorage.getItem('moduleTree'));
    const opportunity = ['/rescue', 'opportunity_cust'];
    const clue = ['clue'];

    moduleTree.forEach((ele: any) => {
      if (opportunity.includes(ele.ngUrl)) {
        this.isHasOpportunity = true;
      }
      if (clue.includes(ele.ngUrl)) {
        this.isHasClue = true;
      }
      (ele.widgets || []).forEach((el: any) => {
        if (opportunity.includes(el.id)) {
          this.isHasOpportunity = true;
        }
        if (clue.includes(el.id)) {
          this.isHasClue = true;
        }
      });
    });

    return this.isHasOpportunity || this.isHasClue;
  }
  initSignalR() {
    if (!this.hasPermission()) {
      return;
    }
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(
        environment.api.v2.baseUrl +
          'glcrm-clue-opportunity-api/hubs/clueopportunity?access_token=' +
          this._credentials.token
      )
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          // 断开连接
          this.hubConnection.off('NewClueNotify');
          this.hubConnection.off('NewOpportunityNotify');
          if (retryContext.elapsedMilliseconds < 60000 * 60 * 24) {
            // If we've been reconnecting for less than 60 seconds so far,
            // wait between 0 and 10 seconds before the next reconnect attempt.
            return Math.random() * 10000;
          } else {
            // If we've been reconnecting for more than 60 seconds so far, stop reconnecting.
            return null;
          }
        }
      })
      .build();
    this.hubConnection
      .start()
      .then(() => {
        this.subscriptionService.signalRConnection$.next();
        this.log.debug('Connection started');
      })
      .catch((err) => {
        this.log.debug('Error while starting connection: ' + err);
        setTimeout(() => {
          this.hubConnection.off('NewClueNotify');
          this.hubConnection.off('NewOpportunityNotify');
          this.initSignalR();
        }, 5000);
      });
  }
  public addDataListener = () => {
    if (this.isHasClue) {
      this.hubConnection.on('NewClueNotify', (message: any) => {
        // clueType: Crash / RoadRescue
        const msg = {
          type: 'clue' + message.clueType
        };
        this.log.info(this.translate.instant(this.tip[msg.type]));
        this.subscriptionService.messageReceive$.next(msg);
      });
    }
    if (this.isHasOpportunity) {
      this.hubConnection.on('NewOpportunityNotify', (message: any) => {
        // opportunityType: Crash / RoadRescue
        const msg = {
          type: 'opportunity' + message.opportunityType
        };
        this.log.info(this.translate.instant(this.tip[msg.type]));
        this.subscriptionService.messageReceive$.next(msg);
      });
    }
    // 重连成功;
    this.hubConnection.onreconnected(() => {
      this.log.debug(`signalR重连成功`);
      this.hubDebounce.next();
    });
    // 重连失败
    this.hubConnection.onreconnecting((error: any) => {
      this.log.debug(`signalR重连失败，${error}即将重新连接`);
    });
    // 防抖;
    this.hubDebounce
      .pipe(
        // 请求防抖 1000毫秒
        debounceTime(1000),
        distinctUntilChanged()
      )
      .subscribe(() => {
        this.subscriptionService.signalRConnection$.next();
      });
  };
}
