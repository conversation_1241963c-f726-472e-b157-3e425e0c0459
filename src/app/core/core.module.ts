import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClient, HttpClientModule, HttpClientJsonpModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { TranslateModule } from '@ngx-translate/core';

import { SsoModule } from '../sso/sso.module';
import { SharedModule } from '@app/shared/shared.module';
import { AccountSettingsModule } from '@app/account-settings/account-settings.module';
import { NoticeboardManagementModule } from '@app/noticeboard-management/noticeboard-management.module';
import { UserGuideManagementModule } from '@app/user-guide-management/user-guide-management.module';
import { CoreRoutingModule } from './core-routing.module';

import { ProfileService } from './profile/profile.service';
import { SsoServiceService } from '../sso/shared/sso-service.service';
import { SweetAlert2Service } from '@app/shared/dialogs/sweetalert2.service';

import {
  AuthenticationService,
  AuthenticationOAuth2Service,
  AuthenticationGuard,
  Dialogs,
  LoggerFactory,
  HttpService,
  HttpCacheService,
  ApiPrefixInterceptor,
  ErrorHandlerInterceptor,
  CacheInterceptor,
  Auth2TokenInterceptor,
  ShellComponent,
  HeaderComponent,
  NotificationsComponent,
  ProfileComponent,
  TopMenuComponent,
  MessageService
} from '@app/core';

import { SsoRegisterComponent } from '../sso/sso-register/sso-register.component';
import { ContactUsComponent } from './shell/header/contact-us/contact-us.component';

@NgModule({
  imports: [
    FormsModule,
    RouterModule,
    CommonModule,
    HttpClientModule,
    HttpClientJsonpModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    TranslateModule,
    SharedModule,
    SsoModule,
    AccountSettingsModule,
    NoticeboardManagementModule,
    UserGuideManagementModule,
    CoreRoutingModule
  ],
  declarations: [
    ShellComponent,
    HeaderComponent,
    ProfileComponent,
    TopMenuComponent,
    SsoRegisterComponent,
    NotificationsComponent,
    ContactUsComponent
  ],
  entryComponents: [ContactUsComponent],
  providers: [
    Dialogs,
    LoggerFactory,
    ProfileService,
    HttpCacheService,
    CacheInterceptor,
    Auth2TokenInterceptor,
    SsoServiceService,
    SweetAlert2Service,
    AuthenticationGuard,
    ApiPrefixInterceptor,
    AuthenticationService,
    ErrorHandlerInterceptor,
    AuthenticationOAuth2Service,
    MessageService,
    {
      provide: HttpClient,
      useClass: HttpService
    }
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    // Import guard
    if (parentModule) {
      throw new Error(`${parentModule} has already been loaded. Import Core module in the AppModule only.`);
    }
  }
}
