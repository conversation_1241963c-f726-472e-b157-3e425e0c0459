/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { LabelSelectModelComponent } from './label-select-model.component';

describe('LabelSelectModelComponent', () => {
  let component: LabelSelectModelComponent;
  let fixture: ComponentFixture<LabelSelectModelComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [LabelSelectModelComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LabelSelectModelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
