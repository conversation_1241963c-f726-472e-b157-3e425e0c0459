import { cloneDeep } from 'lodash';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { Dialogs, Logger, LoggerFactory } from '@app/core';
import { OppoCommonService } from '@app/opportunity-business/opportunity-components/service/oppo-commom.service';
import { finalize } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

declare const $: any;
@Component({
  selector: 'app-label-select-model',
  templateUrl: './label-select-model.component.html',
  styleUrls: ['./label-select-model.component.scss']
})
export class LabelSelectModelComponent implements OnInit, OnChanges {
  @Input() IsDisabled?: boolean = false; // 是否可编辑
  @Input() disabledSelect?: boolean = false; // 是否可编辑
  @Input() type: number = 1; //打标签-1，批量打标签-2
  @Input() showLabel: string = ''; //输入的标签
  @Input() showLabelList: Array<any> = []; // 选中的标签
  @Input() multiple: boolean = true; //是否多选
  @Input() orgId: any; //客户所属机构（不传则显示全部手动标签）
  @Output() showLabelListChange = new EventEmitter();

  log: Logger;
  loading: boolean = false;
  params: any = {};
  labelList: Array<any> = []; // 标签
  openPanel: boolean = true; // 展示下拉框
  labelListFilter: Array<any> = []; //标签搜索数据

  constructor(
    private loggerFactory: LoggerFactory,
    private oppoCommonService: OppoCommonService,
    private translate: TranslateService,
    private dialogs: Dialogs
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant(`标签列表`));
  }

  ngOnInit() {
    this.getLabelList();
  }

  ngOnChanges() {
    this.getLabelList();
  }

  // 获取标签列表
  getLabelList(id?: any) {
    this.oppoCommonService
      .queryLabelList({
        shareType: 1
      })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          this.labelListFilter = cloneDeep(response);
          const labelList = cloneDeep(response);
          // 已选中标签数据处理
          if (this.showLabelList.length) {
            labelList.forEach((item: any) => {
              if (id && id === item.id) this.showLabelList.push(item);
              this.showLabelList.forEach((element: any) => {
                if (item.id === element.id) item.checked = true;
              });
            });
          }
          this.labelList = labelList;
          this.showLabelListChange.emit(this.showLabelList);
        },
        (error) => {
          this.log.error(this.translate.instant('标签列表获取失败。'), this.translate.instant(error));
        }
      );
  }

  // 全部清空
  clearSelected() {
    if (this.disabledSelect) {
      return;
    }
    this.showLabelList = [];
    this.showLabel = '';
    this.labelList.forEach((item: any) => {
      item.checked = false;
    });
  }

  // 点击下拉框
  pannelClick() {
    this.openPanel = !this.openPanel;
    this.showLabel = '';
    const labelList = cloneDeep(this.labelListFilter);
    if (this.showLabelList.length) {
      labelList.forEach((item: any) => {
        this.showLabelList.forEach((element: any) => {
          if (item.id === element.id) item.checked = true;
        });
      });
    }
    this.labelList = labelList;
  }

  // 标签搜索名称
  filterCarName(array: Array<any>) {
    if (this.disabledSelect) {
      return;
    }
    this.openPanel = false;
    const filterTemporaryList = cloneDeep(
      array.filter((item: any) => {
        let searchString = item.labelName;
        return searchString.indexOf(this.showLabel) >= 0;
      })
    );
    filterTemporaryList.forEach((item: any) => {
      this.showLabelList.forEach((element: any) => {
        if (item.id === element.id) item.checked = true;
      });
    });
    return filterTemporaryList;
  }

  // 标签选中事件
  getLabelChecked(item: any) {
    if (this.disabledSelect) {
      return;
    }
    // this.showLabel = '';
    item.checked = !item.checked;
    if (item.checked) {
      this.showLabelList.push(item);
    } else {
      this.showLabelList = this.showLabelList.filter((i: any) => i.id !== item.id);
    }
    this.openPanel = true;
    this.showLabel = '';
    this.showLabelListChange.emit(this.showLabelList);
  }

  // 关闭选中标签
  close(item: any) {
    if (this.disabledSelect) {
      return;
    }
    this.showLabelList = this.showLabelList.filter((i: any) => i.id !== item.id);
    item.checked = false;
    this.openPanel = false;
    this.showLabel = '';
    this.showLabelListChange.emit(this.showLabelList);
  }

  //创建标签
  createLabel() {
    this.stopPropagation();
    if (!this.showLabel) {
      this.log.warn(this.translate.instant('请输入标签名称'));
      return;
    }
    if (this.showLabel.length > 30) {
      this.log.warn(this.translate.instant('标签名称不能大于30字符'));
      return;
    }
    const param = {
      labelName: this.showLabel,
      shareType: 1
    };
    this.oppoCommonService.CreateLabel(param).subscribe(
      (res) => {
        this.log.info(this.translate.instant('保存成功'));
        // console.log(res.data)
        this.showLabel = '';
        this.getLabelList(res.id);

        // console.log(this.labelList)
      },
      (error) => {
        this.log.error(this.translate.instant(`保存失败`), this.translate.instant(error));
      }
    );
  }

  //删除标签
  deleteLabel(item: any) {
    if (this.disabledSelect || this.IsDisabled) {
      return;
    }
    this.stopPropagation();
    this.dialogs.confirm(this.translate.instant(`删除后，已打此标签的客户也将去掉此标签，确定删除？`)).subscribe(
      () => {
        this.oppoCommonService.deleteLabel(item.id).subscribe(
          () => {
            this.log.info(this.translate.instant('删除成功'));
            this.showLabel = '';
            this.getLabelList();
          },
          (error) => this.log.error(this.translate.instant('删除失败'), this.translate.instant(error))
        );
      },
      () => this.log.debug('取消删除')
    );
  }

  // 阻止子组件冒泡
  stopPropagation(e?: any) {
    e = e || window.event;
    if (e.stopPropagation) {
      //W3C阻止冒泡方法
      e.stopPropagation();
    } else {
      e.cancelBubble = true; //IE阻止冒泡方法
    }
  }
}
