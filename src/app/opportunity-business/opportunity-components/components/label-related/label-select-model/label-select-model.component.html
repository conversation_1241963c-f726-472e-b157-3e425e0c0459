<div class="m-section__content">
  <div
    id="dropdown"
    class="m-dropdown m-dropdown__toggle m-dropdown--inline m-dropdown--arrow"
    [ngClass]="{ multiple: multiple }"
    data-dropdown-persistent="true"
    aria-expanded="true"
    (click)="pannelClick()"
  >
    <span class="select_label" *ngFor="let i of showLabelList">
      {{ i.labelName }}
      <i class="la la-close" (click)="close(i)"></i>
    </span>
    <input
      type="text"
      [disabled]="IsDisabled"
      class="margin-left_6 btn lz-vehicle-selection-show select-drop-down"
      placeholder="{{ '输入标签名后，点击创建可直接生成新标签' | translate }}"
      [(ngModel)]="showLabel"
      (keyup)="labelList = filterCarName(labelListFilter)"
    />
  </div>
  <!-- 下拉框内容 -->
  <div class="m-dropdown__wrapper" [hidden]="openPanel">
    <div class="lz-vehicle-selection-wr">
      <div class="m-dropdown__inner">
        <div class="m-dropdown__body">
          <div class="m-dropdown__content">
            <div class="row flex_warp_unset lz-vehicle-selection-container lz-vehicle-brand">
              <div class="flex_full overflow_hidden lz-vehicle-selection-container-right font_color_black_65">
                <ul class="list-style list-unstyled text-left">
                  <li class="lz-vehicle-selection-item" *ngFor="let item of labelList">
                    <div
                      class="dropdown_stype lz-vehicle-selection-hover"
                      [ngClass]="{ 'lz-vehicle-selection-select': item.checked }"
                      (click)="getLabelChecked(item)"
                    >
                      <div>
                        <i class="iconfont iconbiaoqian margin-right_5"></i>
                        <span>{{ item.labelName }}</span>
                      </div>
                      <div>
                        <i
                          *ngIf="!item.checked"
                          class="iot iconhuabanfuzhi icon-delete"
                          (click)="deleteLabel(item)"
                        ></i>
                        <i *ngIf="item.checked" class="la la-check"></i>
                      </div>
                    </div>
                  </li>
                  <span class="create_label_style" (click)="createLabel()">
                    <i class="la la-plus"></i>
                    <i class="text_underLine">{{ '创建新标签' | translate }}</i>
                  </span>
                  <!-- <span class="padding_10 display_inline_block" *ngIf="!labelList || labelList.length==0">暂无数据</span> -->
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
