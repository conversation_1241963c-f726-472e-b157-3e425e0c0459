::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #9ea7c0;
  opacity: 0.7;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px #fff;
  background: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  box-shadow: inset 0 0 6px #c4c5d6;
  background-color: transparent;
}

::-webkit-scrollbar-thumb:hover {
  box-shadow: inset 0 0 6px #c4c5d6;
  background-color: #c5cad9;
}

input:-webkit-autofill {
  animation-name: autofill;
  animation-fill-mode: both;
}

@keyframes autofill {
  to {
    color: #000;
    background: none;
  }
}

.m-dropdown__wrapper {
  width: 100%;
  background: #ffffff;
  position: absolute;
  border-radius: 0px 0px 4px 4px;
  // min-width: 443px;
  padding: 0 !important;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.15);
  z-index: 9;
}

.lz-vehicle-selection-result-text {
  display: inline-block;
  padding: 5px 0;
}

.lz-vehicle-selection-type {
  margin: 0;
  border-bottom: solid 1px #dcd7d7;
  padding: 9px 12px;
}

.lz-vehicle-selection-letter {
  // display: inline-block;
  padding: 0 6px;
  text-align: center;
  // border-right: solid 1px #dcd7d7;
}

.lz-vehicle-selection-letter li {
  padding: 0 3px;
  color: rgba(0, 0, 0, 0.55);
  margin: 5px 0;
  word-break: break-all;
}

.lz-vehicle-selection-letter li:hover {
  border-radius: 2px;
}

.lz-vehicle-selection-letter-li-active {
  border-radius: 2px;
}

.lz-vehicle-selection-container {
  max-height: 260px;
  // overflow: auto;
  margin-left: 0 !important;
  margin-right: 0 !important;

  .lz-vehicle-selection-letter,
  .flex_full {
    padding-top: 0;
  }
}

.lz-vehicle-selection-container b {
  font-weight: bold;
}

.lz-vehicle-selection-item {
  // margin-left: 4px;

  p {
    margin: 0;
    padding: 4px 12px;

    input[type="checkbox"] {
      vertical-align: -2px;
    }
  }
}

.lz-vehicle-selection-cartitle {
  font-weight: bold;
}

.m-section__content {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.m-dropdown .m-dropdown__wrapper {
  display: block;
  width: 100%;
}

.m-dropdown.m-dropdown--inline {
  display: block;
}

.m-section__content .m-dropdown__toggle {
  display: block;
  background: url("/assets/font/select_drop_down.svg") no-repeat 96% center #fff;
  text-align: left;
  padding-right: 50px;
  // height: 32px;
}

.lz-vehicle-filtering {
  padding: 6px;

  input {
    border-color: rgba(0, 0, 0, 0.15);
    height: 32px !important;
  }
}

.m-dropdown .m-dropdown__wrapper .m-dropdown__body {
  padding: 0;
}

.lz-vehicle-selection-wr {
  position: relative;
  height: 100%;
}

.lz-vehicle-selection-modal {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #000;
  opacity: 0.1;
  z-index: 2;
}

button {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -12px;
  margin-left: -20px;
  border: none;
  background: none;
}

.lz-vehicle-selection-show {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  // text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  background: #fff;
}

.text_col {
  color: #757575;
}

.lz-vehicle-brand {
  width: 100%;
  // margin-left: 15px !important;
  margin-right: 15px !important;
}

.letter_container {
  max-height: 260px;
  max-width: 100px;
  overflow: auto;
  min-width: 50px;
}

.lz-vehicle-selection-select {
  color: #4056ff;
  cursor: pointer;
  background: rgba(64, 86, 255, 0.1);
}

.lz-vehicle-selection-container-right {
  border-left: solid 1px #dcd7d7;

  ul {
    overflow: auto;
    max-height: 214px;
  }
}

.list-inline li {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100px;
}

.text_overflow_unset {
  text-overflow: unset !important;
}

.all_check {
  vertical-align: -2px;
  margin-left: 16px;
}

.dropdown_stype {
  justify-content: space-between;
  align-items: center;
  display: flex;
  padding: 2px 10px;
}

.text_underLine {
  text-decoration: underline;
  font-style: normal;
}

.create_label_style {
  padding: 10px;
  cursor: pointer;
  display: inline-block;
  display: flex;
  align-items: center;

  &:hover {
    color: #4777fd;
  }
}

.multiple input {
  background-color: transparent !important;
  border: none;
  cursor: pointer;
  height: 30px !important;
  line-height: 30px;
  padding: 0;
}

.select_label {
  padding: 4px 6px;
  background: #edeefa;
  margin: 6px;
  display: inline-block;
  line-height: 20px;
  color: #53587a;
  font-size: 14px;
  border-radius: 4px;
  cursor: auto;

  i {
    font-size: 14px;
    margin-left: 6px;
  }

  i:hover {
    color: #4056ff;
    cursor: pointer;
  }
}

.m-dropdown {
  cursor: pointer;

  :focus {
    border-color: #ff732e !important;
    outline: none !important;
  }
}

.lz-vehicle-selection-hover .icon-delete {
  display: none;
}

.lz-vehicle-selection-hover:hover {
  color: #4056ff;
  // border-bottom-color: #dfe0e2 !important;
  cursor: pointer;
  background: rgba(64, 86, 255, 0.1);
}

.lz-vehicle-selection-hover:hover .icon-delete {
  display: inline !important;
}

input::placeholder {
  font-size: 13px;
}
.flex_full {
  flex: 1;
}
.overflow_hidden {
  overflow: hidden;
}
