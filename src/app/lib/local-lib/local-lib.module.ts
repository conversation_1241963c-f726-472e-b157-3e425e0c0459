import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LocalDatePipe } from './pipe/local-date.pipe';
import { LocalNumberPipe } from './pipe/local-number.pipe';
import { LocalNzDateTimeDirective } from './directives/local-nz-date-time.directive';
import { LocalLogDatePipe } from './pipe/log-date.pipe';

@NgModule({
  imports: [CommonModule],
  declarations: [LocalDatePipe, LocalNumberPipe, LocalLogDatePipe, LocalNzDateTimeDirective],
  exports: [LocalDatePipe, LocalNumberPipe, LocalLogDatePipe, LocalNzDateTimeDirective]
})
export class LocalLibModule {}
