import { Directive, OnDestroy, AfterViewInit, ElementRef } from '@angular/core';
import { Subscription, timer } from 'rxjs';
import { finalize, map, switchMap } from 'rxjs/operators';
import { AccountSettingService } from '@app/account-settings/services/account-setting.service';
import { NzDatePickerComponent } from 'ng-zorro-antd/date-picker';

/**
 * 改变日期时间格式,同步NzDatePickerComponent, NzTimePickerComponent
 * 并将大写AM/PM改为小写am/pm
 */

@Directive({
  selector: '[appLocalNzDateTime]'
})
export class LocalNzDateTimeDirective implements AfterViewInit, OnDestroy {
  onDateTimeChange: Subscription;
  onEmitValue: Subscription;
  onValueChange: Subscription;
  onOpenChange: Subscription;

  constructor(
    private accountSettingService: AccountSettingService,
    private el: ElementRef,
    private nzDatePickerComponent: NzDatePickerComponent
  ) {}

  ngAfterViewInit(): void {
    this.onDateTimeChange = this.accountSettingService.onDateTimeChange.subscribe(() => {
      // 改变日期时间格式,同步nz组件时间格式
      this.nzDatePickerComponent.datePickerService.emitValue$.next();
    });

    this.onEmitValue = this.nzDatePickerComponent.datePickerService.emitValue$.subscribe(() => {
      this.updateInputValue();
    });

    this.onValueChange = this.nzDatePickerComponent.datePickerService.valueChange$.subscribe(() => {
      this.updateInputValue();
      this.updatePanelHeaderText(this.nzDatePickerComponent.realOpenState);
    });

    this.onOpenChange = this.nzDatePickerComponent.nzOnOpenChange.subscribe((isOpen: boolean) => {
      this.updatePanelHeaderText(isOpen);
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe();
  }

  /**
   * 改变日期时间格式,并将value值AM/PM改为小写am/pm
   */
  updateInputValue() {
    const updateValue$: Subscription = timer(0)
      .pipe(
        map(() => {
          this.nzDatePickerComponent?.updateInputValue();
          this.nzDatePickerComponent?.focus(); // 触发angular变更检测
        }),
        switchMap(() => timer(0)),
        finalize(() => updateValue$.unsubscribe())
      )
      .subscribe(() => {
        Array.from(this.el.nativeElement.querySelectorAll('.ant-picker-input > input'))?.forEach(
          (elInput: HTMLInputElement) => {
            if (!elInput?.value?.trim()) {
              return;
            }
            elInput.value = elInput.value.toLowerCase();
          }
        );
      });
  }

  /**
   * 将时间选择器弹窗里的AM/PM改为小写am/pm
   */
  updatePanelHeaderText(isOpen = false) {
    if (!isOpen) {
      return;
    }
    const updateValue$: Subscription = timer(0)
      .pipe(finalize(() => updateValue$.unsubscribe()))
      .subscribe(() => {
        Array.from(document.querySelectorAll('.ant-picker-time-panel .ant-picker-header-view'))?.forEach(
          (div: HTMLDivElement) => {
            if (!div.innerText?.trim()) {
              return;
            }
            div.innerText = div.innerText.toLowerCase();
          }
        );
      });
  }

  unsubscribe() {
    this.onDateTimeChange?.unsubscribe();
    this.onDateTimeChange = undefined;
    this.onEmitValue?.unsubscribe();
    this.onEmitValue = undefined;
    this.onValueChange?.unsubscribe();
    this.onValueChange = undefined;
    this.onOpenChange?.unsubscribe();
    this.onOpenChange = undefined;
  }
}
