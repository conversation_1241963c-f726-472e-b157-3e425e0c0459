import { Pipe, PipeTransform, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { formatDate } from '@angular/common';

import { I18nService } from '@app/core/i18n.service';
import { AccountSettingService } from '@app/account-settings/services/account-setting.service';
import { Subscription } from 'rxjs';

export function formatLocalDate(
  value: string | number | Date,
  format: string,
  locale: string,
  timezone?: string
): string {
  let formatValue = formatDate(value, format || 'shortDate', locale, timezone);
  // 修复angular 12小时制 am/pm 一直是大写问题, 当只有一个a时,表示小写 am/pm
  if (format && format.split('').filter((chat) => chat === 'a').length === 1) {
    formatValue = formatValue.toLowerCase();
  }
  return formatValue;
}

@Pipe({
  name: 'localLogDate'
})
export class LocalLogDatePipe implements PipeTransform, OnDestroy {
  value = '';

  onDateTimeChange: Subscription | undefined;

  constructor(
    private accountSettingService: AccountSettingService,
    private cd: ChangeDetectorRef,
    private i18nService: I18nService
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe();
  }

  transform(value: any, format?: string, timezone?: string, locale?: string) {
    if (!value) {
      return '';
    }
    if (value.indexOf('编辑账号有效期：') === -1) {
      this.unsubscribe();
      return value;
    }
    this.replaceValue(value, format, timezone, locale);
    this.unsubscribe();
    if (!this.onDateTimeChange) {
      this.onDateTimeChange = this.accountSettingService.onDateTimeChange.subscribe(() => {
        this.replaceValue(value, format, timezone, locale);
      });
    }
    return this.value;
  }

  replaceValue(value: any, format?: string, timezone?: string, locale?: string) {
    const _this = this;
    this.value = value.replace(
      /\{\"startDateA\":'([^']*)',\"endDateA\":'([^']*)',\"startDateB\":'([^']*)',\"endDateB\":'([^']*)'\}/g,
      function (_match: any, startDateA: string, endDateA: string, startDateB: string, endDateB: string) {
        startDateA = _this.updateValue(startDateA, format, timezone, locale);
        endDateA = _this.updateValue(endDateA, format, timezone, locale);
        startDateB = _this.updateValue(startDateB, format, timezone, locale);
        endDateB = _this.updateValue(endDateB, format, timezone, locale);
        return startDateA + ' - ' + endDateA + ' 变成 ' + startDateB + ' - ' + endDateB;
      }
    );
    this.cd.markForCheck();
  }

  updateValue(value: any, format?: string, timezone?: string, locale?: string) {
    return formatLocalDate(value, format || 'shortDate', locale || this.i18nService.localeId, timezone);
  }

  unsubscribe() {
    this.onDateTimeChange?.unsubscribe();
    this.onDateTimeChange = undefined;
  }
}
