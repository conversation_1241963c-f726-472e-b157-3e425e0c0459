import { Pipe, PipeTransform } from '@angular/core';
import { formatNumber } from '@angular/common';

import { I18nService } from '@app/core/i18n.service';

@Pipe({
  name: 'localNumber'
})
export class LocalNumberPipe implements PipeTransform {
  constructor(private i18nService: I18nService) {}

  transform(value: any, format?: string) {
    if (!value) {
      return '';
    }
    return formatNumber(value, this.i18nService.localeId, format || '.2-2');
  }
}
