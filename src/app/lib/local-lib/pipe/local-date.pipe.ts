import { Pipe, PipeTransform, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { formatDate } from '@angular/common';

import { I18nService } from '@app/core/i18n.service';
import { AccountSettingService } from '@app/account-settings/services/account-setting.service';
import { Subscription } from 'rxjs';

export function formatLocalDate(
  value: string | number | Date,
  format: string,
  locale: string,
  timezone?: string
): string {
  let formatValue = formatDate(value, format || 'shortDate', locale, timezone);
  // 修复angular 12小时制 am/pm 一直是大写问题, 当只有一个a时,表示小写 am/pm
  if (format && format.split('').filter((chat) => chat === 'a').length === 1) {
    formatValue = formatValue.toLowerCase();
  }
  return formatValue;
}

@Pipe({
  name: 'localDate'
})
export class LocalDatePipe implements PipeTransform, OnDestroy {
  value = '';

  onDateTimeChange: Subscription | undefined;

  constructor(
    private accountSettingService: AccountSettingService,
    private cd: ChangeDetectorRef,
    private i18nService: I18nService
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe();
  }

  transform(value: any, format?: string, timezone?: string, locale?: string) {
    if (!value) {
      return '';
    }
    this.updateValue(value, format, timezone, locale);
    this.unsubscribe();
    if (!this.onDateTimeChange) {
      this.onDateTimeChange = this.accountSettingService.onDateTimeChange.subscribe(() => {
        this.updateValue(value, format, timezone, locale);
      });
    }
    return this.value;
  }

  updateValue(value: any, format?: string, timezone?: string, locale?: string) {
    this.value = formatLocalDate(value, format || 'shortDate', locale || this.i18nService.localeId, timezone);
    this.cd.markForCheck();
  }

  unsubscribe() {
    this.onDateTimeChange?.unsubscribe();
    this.onDateTimeChange = undefined;
  }
}
