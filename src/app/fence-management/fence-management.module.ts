import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { SharedModule } from '../shared/shared.module';
import { MapModule } from '@app/map/map.module';

import { FenceManagementRoutingModule } from './fence-management-routing.module';
import { FenceListComponent } from './partial/fence-list/fence-list.component';
import { HistoryFencewarningComponent } from './partial/history-fencewarning/history-fencewarning.component';

import { NewFenceComponent } from './partial/new-fence/new-fence.component';
import { EditFenceComponent } from './partial/edit-fence/edit-fence.component';
import { FenceUiComponent } from './partial/fence-ui/fence-ui.component';
import { FenceDetailsComponent } from './partial/fence-details/fence-details.component';
import { DetialsCardComponent } from './partial/detials-card/detials-card.component';

@NgModule({
  declarations: [
    FenceListComponent,
    NewFenceComponent,
    EditFenceComponent,
    FenceDetailsComponent,
    FenceUiComponent,
    DetialsCardComponent,
    HistoryFencewarningComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    FenceManagementRoutingModule,
    MapModule
  ],
  entryComponents: [HistoryFencewarningComponent]
})
export class FenceManagementModule {}
