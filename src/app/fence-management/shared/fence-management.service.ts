import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { WebApiResultResponse, PagingResponse } from '../../core/http/web-api-result-response';
import { QueryPageList } from '@app/shared/models/type';

@Injectable({
  providedIn: 'root'
})
export class FenceManagementService extends WebApiResultResponse {
  fenceId: number;
  constructor(private http: HttpClient) {
    super();
  }
  // 获取列表
  getFenceList(params: QueryPageList): Observable<PagingResponse> {
    const url = 'glcrm-fence-api/v1/api/fencePage/page';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取围栏列表总数
  getTotalNumber(params: any): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/fencePage/totalCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取未绑定设备列表总数
  getUnboundNumber(params: any): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/fence/getDeviceListCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取已绑定设备列表总数
  getBoundNumber(params: any): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/fence/getDeviceListFromFenceCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 判断围栏名称是否重名
  checkFenceName(name: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/fence/checkFenceName?name=` + name;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取省信息
  getProvince(): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/sysDistrict/provinceList';
    return this.http.post(url, null).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取市信息
  getCity(param: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/sysDistrict/cityListByCode?code=` + param;
    return this.http.post(url, null).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取区信息
  getDistrict(param: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/sysDistrict/cityListByCode?code=` + param;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取未绑定设备
  getUnbound(params: any) {
    const url = 'glcrm-fence-api/v1/api/fence/getDeviceList';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取已绑定设备
  getBound(params: any) {
    const url = 'glcrm-fence-api/v1/api/fence/getDeviceListFromFence';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 添加围栏信息
  addFenceInfo(param: any): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/fence/addFenceInfo';
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 移入设备
  addDevice(params: any) {
    const url = 'glcrm-fence-api/v1/api/fence/addDeviceToFence';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 移出设备
  removeDevice(params: any) {
    const url = 'glcrm-fence-api/v1/api/fence/removeDeviceFromFence';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 修改围栏信息
  updFenceInfo(param: any): Observable<any> {
    const url = 'glcrm-fence-api/v1/api/fence/updFenceInfo';
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 围栏详情查询
  fenceDetail(param: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/fencePage/detail?fenceId=` + param;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 删除围栏信息
  deleteFence(param: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/fencePage/delete?fenceId=` + param;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
