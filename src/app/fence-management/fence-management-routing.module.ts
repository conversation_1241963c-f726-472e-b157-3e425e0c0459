import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';

import { FenceListComponent } from './partial/fence-list/fence-list.component';
import { NewFenceComponent } from './partial/new-fence/new-fence.component';
import { EditFenceComponent } from './partial/edit-fence/edit-fence.component';
import { FenceDetailsComponent } from './partial/fence-details/fence-details.component';

const routes: Routes = RouteExtensions.withHost(
  { path: '', component: FenceListComponent, data: { title: '围栏管理' } },
  [
    { path: 'newFence', component: NewFenceComponent, data: { title: '新建围栏' } },
    { path: ':id/edit', component: EditFenceComponent, data: { title: '编辑围栏' } },
    { path: ':id', component: FenceDetailsComponent, data: { title: '详情' } }
  ]
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class FenceManagementRoutingModule {}
