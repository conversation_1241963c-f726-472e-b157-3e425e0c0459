<div class="m-portlet list_header">
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      class="full-screen no-header"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
      (reset)="reset($event)"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'fenceName'" label="{{ '围栏名称' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入围栏名称' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field
        [name]="'enabled'"
        label="{{ '启用状态' | translate }}"
        [type]="'string'"
        [custom]="enabledStateList"
      >
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择启用状态' | translate }}"
          >
            <nz-option nzLabel="{{ '全部' | translate }}" [nzValue]="undefined"></nz-option>
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'beginDate'" label="{{ '开始日期' | translate }}" [type]="'date'">
        <ng-template
          ngx-query-value-input-template
          dataType="date"
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <nz-range-picker
            class="w-full"
            [nzFormat]="accountSetting.dateFormat"
            [nzAllowClear]="false"
            [(ngModel)]="rule.datas[dataIndex]"
            appLocalNzDateTime
          ></nz-range-picker>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'endDate'" label="{{ '结束日期' | translate }}" [type]="'date'">
        <ng-template
          ngx-query-value-input-template
          dataType="date"
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <nz-range-picker
            [nzFormat]="accountSetting.dateFormat"
            class="w-full"
            [nzAllowClear]="false"
            [(ngModel)]="rule.datas[dataIndex]"
            appLocalNzDateTime
          ></nz-range-picker>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field
        [name]="'fenceAlertType'"
        label="{{ '报警类型' | translate }}"
        [type]="'string'"
        [custom]="alarmTypeList"
      >
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择报警类型' | translate }}"
          >
            <nz-option nzLabel="{{ '全部' | translate }}" [nzValue]="undefined"></nz-option>
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption">
      <div class="m-portlet__head-tools">
        <ul class="m-portlet__nav">
          <li class="m-portlet__nav-item" appApplyPermission="add">
            <button nz-button nzType="primary" (click)="newFence()">
              {{ '新建围栏' | translate }}
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="m-portlet__body p-0">
    <ngx-datatable
      #dt
      appNgxDataTable
      class="material"
      [scrollbarH]="true"
      [rows]="fenceList"
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="loadFences($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
      [columnMode]="'force'"
    >
      <ngx-datatable-column
        [width]="200"
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.orgName }}">{{ row.orgName }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="250"
        name="{{ '围栏名称' | translate }}"
        prop="fenceName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" nzTooltipTitle="{{ row.fenceName | translate }}" nz-tooltip>
            {{ row.fenceName | translate }}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '启用状态' | translate }}"
        prop="enabledStr"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [class]="row.enabled === 1 ? 'enable-green-span' : 'enable-red-span'" *ngIf="row.enabled === 1">
            {{ '启用' | translate }}
          </span>
          <span [class]="row.enabled === 1 ? 'enable-green-span' : 'enable-red-span'" *ngIf="row.enabled !== 1">
            {{ '禁用' | translate }}
          </span>

          <span *ngIf="row.enabled === 0" class="enable-span">
            &nbsp;|&nbsp;
            <span translate>失效</span>
          </span>
          <span *ngIf="row.enabled === 1 && !row.remainingTime" class="enable-span">
            &nbsp;|&nbsp;
            <span translate>有效</span>
          </span>
          <span *ngIf="row.enabled === 1 && row.remainingTime" class="enable-span">
            &nbsp;|&nbsp;
            <span translate>余</span>
            {{ row.remainingTime }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="150"
        name="{{ '报警类型' | translate }}"
        prop="fenceAlertTypeStr"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span>
            {{ row.fenceAlertTypeStr | translate }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '开始时间' | translate }}"
        prop="beginDate"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.beginDate | localDate : accountSetting.dateTimeHourMinuteFormat | replaceEmpty }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '结束时间' | translate }}"
        prop="endDate"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.endDate | localDate : accountSetting.dateTimeHourMinuteFormat | replaceEmpty }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="170"
        name="{{ '设备数量' | translate }}"
        prop="deviceNum"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="150"
        name="{{ '围栏形状' | translate }}"
        prop="fenceShape"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span *ngIf="row.fenceShape === 2">
            {{ '行政区围栏' | translate }}
          </span>
          <span *ngIf="row.fenceShape !== 2">
            {{ '自定义围栏' | translate }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '操作' | translate }}"
        headerClass="text-left"
        cellClass="text-left"
        [frozenRight]="true"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span appApplyPermission="detail" class="margin_right pointer">
            <span
              (click)="checkDetail(row)"
              nz-tooltip
              nzTooltipPlacement="top"
              nzTooltipTitle="{{ '详情' | translate }}"
            >
              <img src="/assets/font/detail.png" />
            </span>
          </span>
          <span appApplyPermission="detail" class="margin_right">|</span>

          <span appApplyPermission="edit" class="margin_right pointer">
            <span
              (click)="editDetail(row)"
              nz-tooltip
              nzTooltipPlacement="top"
              nzTooltipTitle="{{ '编辑' | translate }}"
            >
              <img src="/assets/font/edit.png" />
            </span>
          </span>
          <span appApplyPermission="edit" class="margin_right">|</span>

          <span appApplyPermission="alert_record" class="margin_right">
            <span (click)="historyRecordModal(row)">
              <!-- <span> -->
              <a>
                <img
                  src="/assets/font/history.svg"
                  nz-tooltip
                  nzTooltipPlacement="top"
                  nzTooltipTitle="{{ '预警记录' | translate }}"
                />
              </a>
            </span>
          </span>
          <span appApplyPermission="alert_record" class="margin_right">|</span>

          <span class="margin_right" appApplyPermission="del">
            <a (click)="delete(row)" nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ '删除' | translate }}">
              <img src="/assets/font/delete.png" />
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [frozenRight]="true" [width]="50" headerClass="datatable-header-cell-acitons text-left">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
