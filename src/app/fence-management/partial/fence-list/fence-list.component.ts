import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { finalize } from 'rxjs/operators';

import moment from 'moment';
import { TranslateService } from '@ngx-translate/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';

import { environment } from '@env/environment';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { Logger } from '@core/logger.service';
import { Dialogs } from '@core/dialogs.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate } from '@app/shared/models/type';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';

import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';
import { HistoryFencewarningComponent } from '../history-fencewarning/history-fencewarning.component';
import { setSeconds } from 'date-fns';

declare const $: any;

@Component({
  selector: 'app-fence-list',
  templateUrl: './fence-list.component.html',
  styleUrls: ['./fence-list.component.scss']
})
export class FenceListComponent implements OnInit, AfterViewInit {
  log: Logger;
  loading = false;

  accountSetting = accountSetting;

  fenceList: Array<any>;
  selectedFenceList: Array<any> = [];
  // visibleColumns: string[] = [];
  notHideColumns: string[] = [];
  isShowCollapse = false;

  // 组织机构
  organizationValue: string;
  nodes: Array<any> = [];

  fenceTypeList: Array<any> = [
    { key: '0', name: '普通围栏' },
    { key: '1', name: '一次性围栏' }
  ];

  enabledStateList: Array<any> = [
    { key: '0', name: '禁用' },
    { key: '1', name: '启用' }
  ];

  alarmTypeList: Array<any> = [
    { key: '0', name: '驶入' },
    { key: '1', name: '驶出' }
  ];

  currentNumber = 10;
  totalNumber: number;
  template: any;
  remainingTime: any;
  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'fenceName', op: 'cn' },
          { field: 'enabled', op: 'eq' },
          // 使用ngx-bootstrap组件，op先为cn，后转换成bt
          { field: 'beginDate', op: 'cn' },
          { field: 'endDate', op: 'cn' },
          { field: 'fenceAlertType', op: 'eq' }
        ],
        groups: []
      }
    }
  ];
  // datatables:any;
  // count:number | undefined;

  mode: QueryMode = QueryMode.plainCollapse;
  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  public datatable: any;
  event: any;
  timestamp: number;
  timeDifference: any;
  originTemplate: any;

  constructor(
    private dialogs: Dialogs,
    private router: Router,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private fenceManagementService: FenceManagementService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger(``);
    // 存储最初的查询模板
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
    this.checkFilters(); // 回显过滤条件
  }

  ngOnInit() {
    this.timestamp = new Date().getTime();
    this.getUserAllOrganizations();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  // 解决Edge浏览器下，ngx-datatable组件header处有'null'空值的现象
  removeHeaderNull() {
    $('.datatable-header-cell-label').each(function () {
      if ($(this).text() === 'null') {
        $(this).remove();
      }
    });
  }
  // 获取机构
  getUserAllOrganizations() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => (this.nodes = organNodes));
  }

  onSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selectedFenceList = event.selected;
    }
    console.log(this.selectedFenceList);
  }
  refreshData() {
    this.loadFences(this.event);
  }

  parameterHandling(eventPage: any) {
    const page: any = {
      filter: {
        groups: [],
        op: 'or',
        rules: []
      },
      filters: [],
      pageIndex: eventPage.pageIndex,
      pageSize: eventPage.pageSize
    };
    eventPage.filter.rules.forEach((rule: any) => {
      if (rule.field === 'beginDate' || rule.field === 'endDate') {
        const arr: Array<any> = ['', ''];
        if (rule.data) {
          const startTime = moment(rule.data[0]).hour(0).minute(0).seconds(0);
          const endTime = moment(rule.data[1]).hour(23).minute(59).seconds(59);
          arr[0] = new Date(moment(startTime).format('YYYY-MM-DD HH:mm:ss')).getTime();
          arr[1] = new Date(moment(endTime).format('YYYY-MM-DD HH:mm:ss')).getTime();
          page.filters.push({
            field: rule.field,
            op: 'ge',
            term: arr[0]
          });
          page.filters.push({
            field: rule.field,
            op: 'le',
            term: arr[1]
          });
        }
        page.filter.rules.push({
          field: rule.field.op,
          op: 'bt',
          data: undefined,
          datas: arr
        });
      } else {
        page.filter.rules.push(rule);
        if (rule.data) {
          page.filters.push({
            field: rule.field,
            op: rule.op,
            term: rule.data
          });
        }
      }
    });
    // console.log(page, 999);
    return page;
  }

  loadFences(event: any) {
    this.event = event;
    this.footer.showTotalElements = true;
    const params = this.parameterHandling(event.page);
    this.datatable = event.datatable;
    this.loading = true;
    this.selectedFenceList.length = 0;
    this.fenceManagementService
      .getFenceList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          console.log('获取围栏列表', response);
          if (response.code === 0) {
            let orgNameMax = 0;
            let fenceNameMax = 0;
            response.data.forEach((item: any) => {
              orgNameMax = item.orgName.length > orgNameMax ? item.orgName.length : orgNameMax;
              fenceNameMax = item.fenceName.length > fenceNameMax ? item.fenceName.length : fenceNameMax;
              console.log(orgNameMax);
            });
            // orgNameMax = 14 * orgNameMax + 34;
            // orgNameMax = orgNameMax>200?orgNameMax:200
            // fenceNameMax = 14 * fenceNameMax + 34;
            // fenceNameMax = fenceNameMax>200?fenceNameMax:200
            orgNameMax = Math.max.apply(null, [14 * orgNameMax + 34, 100]);
            fenceNameMax = Math.max.apply(null, [14 * fenceNameMax + 34, 150]);
            console.log(this.table);
            this.table.headerComponent.onColumnResized(orgNameMax, this.table.headerComponent.columns[0]);
            this.table.headerComponent.onColumnResized(fenceNameMax, this.table.headerComponent.columns[1]);
            this.fenceList = this.getRemainingTime(response.data);
            this.currentNumber = this.fenceList.length;
          } else {
            console.log('围栏列表获取失败。', response.msg);
          }
        },
        (error) => console.log('围栏列表获取失败。', error)
      );
  }

  // 获取剩余时间
  getRemainingTime(data: any) {
    for (let i = 0; i < data.length; i++) {
      const end = new Date(data[i].endDate).getTime();
      let timeDifference = end - this.timestamp;
      const day = Math.floor(timeDifference / 1000 / 60 / 60 / 24); // 天
      timeDifference = timeDifference % (1000 * 60 * 60 * 24);
      const hour = Math.floor(timeDifference / 1000 / 60 / 60); // 小时
      timeDifference = timeDifference % (1000 * 60 * 60);
      const min = Math.floor(timeDifference / 1000 / 60); // 分钟
      timeDifference = timeDifference % (1000 * 60);
      if (day === 0) {
        data[i].remainingTime = hour + 'h' + min + 'min';
      }
    }
    return data;
  }

  getTotal() {
    const params = this.parameterHandling(this.event.page);
    this.fenceManagementService
      .getTotalNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.code === 0) {
            this.totalNumber = response.totalCount;
          } else {
            console.log('未绑定设备列表总数获取失败。', response.msg);
          }
        },
        (error) => console.log('未绑定设备列表总数获取失败。', error)
      );
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  delete(row: any) {
    const param = {
      fenceId: row.id
    };
    this.translate.get(`真的要删除吗？`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.fenceManagementService.deleteFence(row.id).subscribe(
            (response) => {
              if (response.code === 0) {
                // this.nzMessage.create('success', 'This is a message of ${type}', {
                //   nzDuration: 1000000
                // });
                this.translate.get(`删除成功`).subscribe((res) => {
                  this.log.success(res);
                });
                this.refreshData();
              } else if (response.code === 500) {
                this.translate.get(response.msg).subscribe((res) => {
                  this.log.info(res);
                });
              }
            },
            (error) => console.log(`${row.fenceName}围栏删除失败,失败信息:`, error)
          );
        },
        () => console.log(`取消删除 ${row.fenceName}`)
      );
    });
  }
  // 新建
  newFence() {
    sessionStorage.setItem('fence', JSON.stringify(this.event.query.query));
    this.router.navigate(['/fence/newFence']);
  }
  // 查看详情
  checkDetail(row: any) {
    sessionStorage.setItem('fence', JSON.stringify(this.event.query.query));
    this.router.navigate(['/fence/' + row.id]);
  }
  // 编辑
  editDetail(row: any) {
    sessionStorage.setItem('fence', JSON.stringify(this.event.query.query));
    this.router.navigateByUrl('/fence/' + row.id + '/edit');
  }
  // 返回 保留筛选条件
  checkFilters() {
    const arr = environment.prePath.split('&');
    const prePage = arr[arr.length - 1];
    if (prePage && prePage.search('fence') !== -1) {
      // console.log('进入if');
      const template = sessionStorage.getItem('fence');
      const obj = JSON.parse(template);
      this.queryTemplates[0].template = obj;
    } else {
      environment.prePath = '';
    }
    sessionStorage.removeItem('fence');
  }
  // 重置查询模板
  reset($event: any) {
    this.queryTemplates = this.originTemplate;
  }
  // 折叠展开工具栏
  showCollapse() {
    this.isShowCollapse = !this.isShowCollapse;
    // console.log(`isShowCollapse`, this.isShowCollapse);
    this.ngxQuery.showCollapse(this.isShowCollapse);
  }

  historyRecordModal(row: any) {
    console.log(row);
    const initialState = { data: row };
    const modalRef: BsModalRef = this.modalService.show(HistoryFencewarningComponent, {
      initialState,
      class: 'modal-lg modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: any) => {
      // tslint:disable-next-line:forin
      for (const r in val) {
        row[r] = val[r];
      }
      onHidden.unsubscribe();
    });
  }
}
