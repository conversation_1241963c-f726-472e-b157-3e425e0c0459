.interval {
  display: grid;
  grid-template-columns: 80% 20%;
  width: 100%;
  padding-top: 15px;
}

:host ::ng-deep .float-right {
  position: absolute;
  right: -20%;
  top: -50px;
}
.function-button {
  margin-top: 18px;
  margin-left: 30px;
}

.show-collapse {
  display: inline-block;
}
.unFold {
  margin-left: 10px;
  color: #3574fa;
  font-size: 14px;
  height: 22px;
  font-weight: 400;
  line-height: 22px;
}
.ngx-style {
  margin-bottom: 20px;
}

.m-dropdown__wrapper {
  top: 70px !important;
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  /* flex-direction: column; */
  height: 50px;
  position: static !important;
  /* width: 15%; */
  /* margin-left: 80%; */
  /* right: 0; */
  justify-content: flex-end;
}

:host ::ng-deep .col-6 {
  position: static;
}

.enable-span {
  width: 28px;
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
  color: #575e72;
  line-height: 22px;
}
.enable-red-span {
  width: 28px;
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
  color: red;
  line-height: 22px;
}
.enable-green-span {
  width: 28px;
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
  color: green;
  line-height: 22px;
}
.search-date {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  border: 1px solid #d3d6de;
  border-radius: 4px;
}
.query-input {
  width: 273px;
  height: 32px;
  background: #ffffff;
  border: none;
}
.date-logo {
  margin-right: 5px;
}
.pointer {
  cursor: pointer;
}

.ellipsis {
  display: inline-block;
  max-width: 100%;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:host ::ng-deep .m-portlet__head-caption {
  display: flex !important;
  flex-direction: row-reverse;
  align-content: center;
  height: 100%;
}
