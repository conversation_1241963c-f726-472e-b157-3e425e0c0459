import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';
import { LoggerFactory, Logger } from '@app/core';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-fence-details',
  templateUrl: './fence-details.component.html',
  styleUrls: ['./fence-details.component.scss']
})
export class FenceDetailsComponent implements OnInit {
  log: Logger;
  loading = false;

  fenceDetail: any = null; // 围栏详情
  fenceId: number;

  constructor(
    private route: ActivatedRoute,
    private fenceManagementService: FenceManagementService,
    private loggerFactory: LoggerFactory
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.fenceId = id;
        this.loadDetial();
      }
    });
  }

  loadDetial() {
    this.fenceManagementService.fenceDetail(this.fenceId).subscribe(
      (respons) => {
        if (respons.code === 0) {
          // console.log(respons, 9999999);
          this.fenceDetail = respons.data;
        } else {
          console.log('围栏信息获取失败', respons.msg);
        }
      },
      (error) => console.log('围栏信息获取失败, 失败信息：', error)
    );
  }
}
