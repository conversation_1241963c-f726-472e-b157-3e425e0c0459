.m-form__group {
  padding-left: 20px;
  padding-right: 30px;
}

.m-form .m-form__group {
  margin-bottom: 0;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 0;
}

.col-form-label {
  text-align: right !important;
}

.text_right {
  text-align: right;
  margin-top: 30px;
}

textarea {
  resize: none;
  min-height: 80px;
  max-height: 180px;
}

.fenceShape {
  display: flex;
  flex-direction: row;
}

.prompt-information {
  color: red;
  display: inline-block;
  vertical-align: middle;
}

h5 {
  margin-left: 6px;
  display: inline-block;
  margin-bottom: 0 !important;
}

.input-group {
  align-items: center !important;
}

.little-title {
  margin-left: 25px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  flex-direction: row;
}

.fenceShape-chose {
  margin: 0 auto;
}

.toolbar {
  display: grid;
  grid-template-columns: 1fr 45px;
  gap: 8px;
  position: relative;

  .query-span {
    display: grid;
    grid-template-columns: 80% 10% 10%;
    background-color: #f1f3f8;

    button {
      width: 41px;
      height: 38px;
      margin: 0px;
    }
    .query_input {
      border-radius: 5px 0px 0px 5px;
      border-color: #f1f3f8;
      background-color: #f1f3f8;
      box-shadow: none;
    }

    .cleanQuery {
      border-color: #f1f3f8;
    }

    .querybtn {
      position: absolute;
      right: 1px;
      border: none;
      border-radius: 0px 5px 5px 0px;
      border-color: #f1f3f8;
      outline: none;

      .la {
        position: absolute;
        right: 10px;
        top: 10px;
        margin-top: 3px;
      }
    }
  }

  .filter-button {
    position: absolute;
    right: 0px;
    top: 0px;
  }
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  justify-content: flex-end;
  height: 50px;
  min-width: 200px;
}

:host ::ng-deep .card-body {
  border: 0px !important;
}

.btnLocation {
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 100%;
  margin-top: 120px;

  button {
    max-width: 100px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.button-unChecked {
  background-image: url(../../../../assets/font/button-unChecked.png);
  width: 83px;
  height: 36px;
  border: 0px;
  margin-left: 41px;
  outline: none;
}

.button-checked {
  background-image: url(../../../../assets/font/button-checked.png);
  width: 83px;
  height: 36px;
  border: 0px;
  margin-left: 41px;
  outline: none;
}

.map {
  width: 100%;
  height: 456px;
  position: relative;
  margin: 0 auto;
}

.button-row {
  margin: 15px;
}

.btn-width {
  max-width: 115px !important;
}

:host .ngx-datatable.material {
  min-height: 0 !important;
}

:host ::ng-deep .m-portlet {
  margin-bottom: 1.2rem !important;
}
