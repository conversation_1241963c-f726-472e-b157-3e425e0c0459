<div class="m-portlet">
  <div class="m-portlet__body">
    <div class="row">
      <div class="col-md-12">
        <div class="little-title">
          <div class="img-front">
            <img src="/assets/font/managementTitle-front.png" />
          </div>
          <h5 translate>基本信息</h5>
        </div>
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label">
        <span class="text-danger">*</span>
        <span translate>所属机构</span>
      </label>
      <div class="col-sm-4">
        <nz-tree-select
          class="w-full"
          [nzNodes]="nodes"
          nzShowLine
          [nzShowSearch]="true"
          nzPlaceHolder="{{ '请选择所属机构' | translate }}"
          [(ngModel)]="organizationValue"
          (ngModelChange)="orgIdChanged()"
          [nzDisabled]="title === '围栏编辑' || unEditable"
        ></nz-tree-select>
        <span class="prompt-information">{{ orgError }}</span>
      </div>
      <label class="col-sm-2 col-form-label">
        <span class="text-danger">*</span>
        <span translate>围栏名称</span>
      </label>
      <div class="col-sm-4">
        <input
          type="text"
          nz-input
          placeholder="{{ '请输入围栏名称' | translate }}"
          maxlength="50"
          [(ngModel)]="fenceName"
          (change)="checkFenceName()"
          [disabled]="unEditable"
        />
        <span class="prompt-information">{{ fenceError }}</span>
        <span class="prompt-information">{{ fenceNameError }}</span>
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label">
        <span class="text-danger">*</span>
        <span translate>开始时间</span>
      </label>
      <div class="col-sm-4">
        <nz-date-picker
          class="w-full"
          [nzShowTime]="{
            nzFormat: accountSetting.timeHourMinuteFormat,
            nzUse12Hours: accountSetting.hourTime === HourTime.Hour12
          }"
          [nzFormat]="accountSetting.dateTimeHourMinuteFormat"
          [(ngModel)]="beginDateTime"
          (ngModelChange)="startTimeError = ''"
          [nzDisabled]="unEditable"
          appLocalNzDateTime
        ></nz-date-picker>
        <span class="prompt-information">{{ startTimeError }}</span>
      </div>
      <label class="col-sm-2 col-form-label">
        <span class="text-danger">*</span>
        <span translate>结束时间</span>
      </label>
      <div class="col-sm-4">
        <nz-date-picker
          class="w-full"
          [nzShowTime]="{
            nzFormat: accountSetting.timeHourMinuteFormat,
            nzUse12Hours: accountSetting.hourTime === HourTime.Hour12
          }"
          [nzFormat]="accountSetting.dateTimeHourMinuteFormat"
          [(ngModel)]="endDateTime"
          (ngModelChange)="endTimeChange()"
          [nzDisabled]="unEditable"
          appLocalNzDateTime
        ></nz-date-picker>
        <span class="prompt-information">{{ endTimeError }}</span>
        <span class="prompt-information">{{ endTimeLessError }}</span>
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label">
        <span class="text-danger">*</span>
        <span translate>报警类型</span>
      </label>
      <div class="col-sm-4" style="padding-top: calc(0.375rem + 1px)">
        <nz-radio-group [(ngModel)]="fenceAlertType" [nzDisabled]="unEditable">
          <label nz-radio [nzValue]="0">{{ '驶入' | translate }}</label>
          <label nz-radio [nzValue]="1">{{ '驶出' | translate }}</label>
        </nz-radio-group>
      </div>
      <label class="col-sm-2 col-form-label" translate>围栏描述</label>
      <div class="col-sm-4">
        <textarea
          nz-input
          placeholder="{{ '请输入描述' | translate }}"
          rows="2"
          maxlength="200"
          [(ngModel)]="fenceDescription"
          [disabled]="unEditable"
        ></textarea>
      </div>
    </div>
  </div>
</div>
<div class="m-portlet">
  <div class="m-portlet__body">
    <div class="row">
      <div class="col-md-12">
        <div class="little-title">
          <div class="img-front">
            <img src="/assets/font/managementTitle-front.png" />
          </div>
          <h5 translate>围栏定位</h5>
        </div>
      </div>
    </div>
    <div class="fenceShape-chose">
      <div class="form-group m-form__group row">
        <label class="col-sm-2 col-form-label">
          <span class="text-danger">*</span>
          <h5 translate>围栏形状</h5>
        </label>
        <div class="col-sm-10">
          <div class="input-group">
            <nz-radio-group [(ngModel)]="shapeType" [nzDisabled]="unEditable" (ngModelChange)="shapeTypeChange()">
              <label nz-radio nzValue="1">{{ '自定义围栏' | translate }}</label>
            </nz-radio-group>

            <button
              (click)="changeFenceShape(FenceShapeType.Circle)"
              *ngIf="shapeType === '1'"
              [disabled]="unEditable"
              [ngClass]="{
                'button-checked': fenceShape === 0,
                'button-unChecked': fenceShape !== 0
              }"
              translate
            >
              圆形
            </button>
            <button
              (click)="changeFenceShape(FenceShapeType.Polygon)"
              *ngIf="shapeType === '1'"
              [disabled]="unEditable"
              [ngClass]="{
                'button-checked': fenceShape === 1,
                'button-unChecked': fenceShape !== 1
              }"
              translate
            >
              多边形
            </button>
          </div>
          <span class="prompt-information">{{ drawFenceError }}</span>
        </div>
      </div>
      <div class="form-group m-form__group row" style="min-height: 60px" *ngIf="showDistrictFence">
        <label class="col-sm-2 col-form-label">
          <span class="text-danger"></span>
        </label>
        <div class="col-sm-4">
          <div class="row" style="padding: 0 15px">
            <label>
              <input
                type="radio"
                value="2"
                class="input-radio"
                [(ngModel)]="shapeType"
                name="shapeType"
                [disabled]="unEditable"
                (ngModelChange)="shapeTypeChange()"
              />
              &nbsp;&nbsp;
              <span translate>行政区围栏</span>
            </label>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <nz-select
                style="width: 120px"
                [compareWith]="compareFn"
                [(ngModel)]="selectProvinceValue"
                (ngModelChange)="changeProvince($event)"
                nzAllowClear
                [nzDisabled]="unEditable"
                nzPlaceHolder="{{ '--省--' | translate }}"
                *ngIf="shapeType === '2'"
              >
                <nz-option *ngFor="let item of selectProvince" [nzValue]="item" [nzLabel]="item.name"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <nz-select
                style="width: 120px"
                [compareWith]="compareFn"
                [(ngModel)]="selectCityValue"
                (ngModelChange)="changeCity($event)"
                nzAllowClear
                [nzDisabled]="unEditable"
                nzPlaceHolder="{{ '--市--' | translate }}"
                *ngIf="selectProvinceValue && shapeType === '2'"
              >
                <nz-option *ngFor="let item of selectCity" [nzValue]="item" [nzLabel]="item.name"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3">
              <nz-select
                style="width: 120px"
                [compareWith]="compareFn"
                [(ngModel)]="selectDistrictValue"
                (ngModelChange)="changeDistrict($event)"
                nzAllowClear
                [nzDisabled]="unEditable"
                nzPlaceHolder="{{ '--区--' | translate }}"
                *ngIf="selectProvinceValue && selectCityValue && shapeType === '2'"
              >
                <nz-option *ngFor="let item of selectDistrict" [nzValue]="item" [nzLabel]="item.name"></nz-option>
              </nz-select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="map">
      <app-fence-ui-map
        [fence]="fence"
        [disabled]="unEditable"
        (getPolygonInfo)="getPolygonInfo($event)"
      ></app-fence-ui-map>
    </div>
  </div>
</div>

<div class="text_right button-row">
  <button type="button" nz-button (click)="back()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()" [nzLoading]="saving" [disabled]="unEditable">
    {{ '保存' | translate }}
  </button>
</div>
<div class="m-portlet">
  <div class="m-portlet__body">
    <div class="row">
      <div class="col-md-12">
        <div class="little-title">
          <div class="img-front">
            <img src="/assets/font/managementTitle-front.png" />
          </div>
          <h5 translate>绑定设备</h5>
          <img
            src="/assets/font/question-mark.svg"
            [ngStyle]="{ 'margin-left': '5px' }"
            style="cursor: pointer"
            nz-tooltip
            nzTooltipPlacement="top"
            nzTooltipTitle="{{ '选填' | translate }}"
          />
        </div>
      </div>
      <div class="col-md-5">
        <div class="m-portlet">
          <div class="m-portlet__head">
            <div class="m-portlet__head-tools">
              <div class="toolbar">
                <ngx-query
                  [hidden]="true"
                  [datePickerReadonly]="false"
                  [columnNumber]="1"
                  class="full-screen no-header"
                  #unboundNgxQuery
                  [queryTemplates]="unboundQueryTemplates"
                  [isRetainQueryMode]="true"
                  [showModeButtons]="true"
                  [mode]="mode"
                  [showPlainCollapseToolBar]="false"
                >
                  <ngx-query-field [name]="'search'" [label]="''" [type]="'string'">
                    <ng-template
                      ngx-query-value-input-template
                      dataType="text"
                      let-rules="rules"
                      let-rule="rule"
                      let-dataIndex="dataIndex"
                    >
                      <button class="querybtn btn">
                        <i class="la la-search"></i>
                      </button>
                      <input
                        type="text"
                        [(ngModel)]="rule.datas[dataIndex]"
                        class="form-control form-control-sm query_input"
                        placeholder="{{ '请输入车主名/车牌号/设备号' | translate }}"
                        (keyup.enter)="executeQuery($event)"
                      />
                    </ng-template>
                  </ngx-query-field>
                  <ngx-query-field
                    [name]="'status'"
                    label="{{ '设备状态' | translate }}"
                    [type]="'string'"
                    isCollapse="true"
                  >
                    <ng-template
                      ngx-query-value-input-template
                      dataType="text"
                      let-rules="rules"
                      let-rule="rule"
                      let-dataIndex="dataIndex"
                      let-placeholder="placeholder"
                    >
                      <input
                        type="text"
                        [(ngModel)]="rule.datas[dataIndex]"
                        placeholder="{{ '设备状态' | translate }}"
                        class="form-control form-control-sm"
                      />
                    </ng-template>
                  </ngx-query-field>
                </ngx-query>
                <div>
                  <span class="query-span">
                    <input
                      type="text"
                      class="form-control query_input"
                      placeholder="{{ '车主名/车牌号/设备号' | translate }}"
                      [(ngModel)]="queryInput"
                      (keyup.enter)="executeQuery()"
                    />
                    <div style="position: relative">
                      <button class="cleanQuery btn" (click)="cleanQuery()" *ngIf="queryInput">
                        <span>x</span>
                      </button>
                    </div>
                    <div style="position: relative">
                      <button class="querybtn btn" (click)="executeQuery()">
                        <img src="/assets/font/search-logo.svg" alt="{{ '查询' | translate }}" />
                      </button>
                    </div>
                  </span>
                </div>
                <button
                  type="button"
                  nz-button
                  nz-popover
                  nzPopoverTrigger="click"
                  nzPopoverPlacement="bottom"
                  [(nzPopoverVisible)]="filterVisible"
                  [nzPopoverContent]="filterPopover"
                  [nzPopoverOverlayStyle]="{ width: '140px' }"
                >
                  <img src="/assets/font/filter.png" alt="{{ '筛选' | translate }}" />
                </button>
              </div>
            </div>
          </div>
          <div class="m-portlet__body p-0">
            <ngx-datatable
              appNgxDataTable
              #unboundDt
              class="material"
              [scrollbarH]="true"
              (select)="onUnboundSelect($event)"
              [rows]="unboundList"
              [saveState]="false"
              [loadingIndicator]="loading"
              [selected]="selectedUnbound"
              [selectionType]="'checkbox'"
              [ngxQuery]="unboundNgxQuery"
              (loadValue)="loadUnboundList($event)"
              [isRetainCurrentPageQuery]="false"
              ngxNoPageFooterWatcher
              [footer]="unboundFooter"
              [count]="unboundCurrentNumber"
              [columnMode]="'force'"
              [selectAllRowsOnPage]="false"
              externalPaging="false"
            >
              <ngx-datatable-column
                [width]="40"
                [sortable]="false"
                [canAutoResize]="false"
                [draggable]="false"
                [resizeable]="true"
                [headerCheckboxable]="true"
                [checkboxable]="true"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="80"
                name="{{ '车主名' | translate }}"
                prop="owner_name"
                headerClass="text-left"
                cellClass="text-left"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="150"
                name="{{ '车牌号' | translate }}"
                prop="plate_number"
                headerClass="text-left"
                cellClass="text-left"
              >
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span style="width: 90%">
                    <div class="address" [title]="row.plate_number">
                      {{ row.plate_number }}
                    </div>
                  </span>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column
                [width]="80"
                name="{{ '设备号' | translate }}"
                prop="device_no"
                headerClass="text-left"
                cellClass="text-left"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="120"
                name="{{ '设备类型' | translate }}"
                prop="endTime"
                headerClass="text-left"
                cellClass="text-left"
              >
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span *ngIf="row.is_wireLess == 0" translate>有线设备</span>
                  <span *ngIf="row.is_wireLess == 1" translate>无线设备</span>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column
                [width]="150"
                name="{{ '设备状态' | translate }}"
                prop="status"
                headerClass="text-left"
                cellClass="text-left"
              >
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ deviceStatusInfo[row?.status || DeviceStatus.Invalid].name | translate }}
                </ng-template>
              </ngx-datatable-column>
            </ngx-datatable>
            <br />
            <div class="footer-style">
              <nopage-datatable-footer
                #unboundFooter
                [currentNumber]="unboundCurrentNumber"
                [totalNumber]="unboundTotalNumber"
                (getTotal)="getUnboundTotal()"
                [checkTurnPage]="UnboundTurnPage.bind(this)"
              ></nopage-datatable-footer>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-2">
        <div class="btnLocation">
          <button nz-button nzType="primary" nzBlock (click)="addMenu()" class="btn-width" [disabled]="!fenceId">
            <span translate>移入</span>
            <i nz-icon nzType="arrow-right" nzTheme="outline"></i>
          </button>
          <button nz-button nzBlock (click)="removeMenu()" class="btn-width" [disabled]="!fenceId">
            <span translate>移出</span>
            <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
          </button>
        </div>
      </div>
      <div class="col-md-5">
        <div class="m-portlet">
          <div class="m-portlet__head">
            <div class="m-portlet__head-caption">
              <div class="m-portlet__head-tools">
                <ul class="m-portlet__nav">
                  <li class="m-portlet__nav-item">
                    <span>
                      <span translate>已绑定设备数量:</span>
                      {{ boundtotalNumber }}
                    </span>
                  </li>
                </ul>
                <div class="toolbar">
                  <ngx-query
                    [datePickerReadonly]="false"
                    [columnNumber]="1"
                    class="full-screen no-header"
                    [showModeButtons]="true"
                    [mode]="mode"
                    [showPlainCollapseToolBar]="false"
                    #boundNgxQuery
                    [queryTemplates]="boundQueryTemplates"
                    [isRetainQueryMode]="true"
                    [hidden]="true"
                  >
                    <ngx-query-field [name]="'fenceId'" label="{{ '围栏id' | translate }}" [type]="'string'">
                      <ng-template
                        ngx-query-value-input-template
                        dataType="text"
                        let-rules="rules"
                        let-rule="rule"
                        let-dataIndex="dataIndex"
                        let-placeholder="placeholder"
                      >
                        <input
                          type="text"
                          [(ngModel)]="rule.datas[dataIndex]"
                          class="form-control form-control-sm"
                          placeholder="{{ '围栏id' | translate }}"
                        />
                      </ng-template>
                    </ngx-query-field>
                  </ngx-query>
                </div>
              </div>
            </div>
          </div>
          <div class="m-portlet__body p-0">
            <ngx-datatable
              appNgxDataTable
              #boundDt
              class="material"
              [scrollbarH]="true"
              (select)="onBoundSelect($event)"
              [rows]="boundList"
              [saveState]="false"
              [loadingIndicator]="bindLoading"
              [selected]="selectedBound"
              [selectionType]="'checkbox'"
              [ngxQuery]="boundNgxQuery"
              (loadValue)="lodeBoundList($event)"
              [isRetainCurrentPageQuery]="false"
              ngxNoPageFooterWatcher
              [footer]="boundFooter"
              [count]="boundCurrentNumber"
              [columnMode]="'force'"
              [selectAllRowsOnPage]="false"
              externalPaging="false"
            >
              <ngx-datatable-column
                [width]="40"
                [sortable]="false"
                [canAutoResize]="false"
                [draggable]="false"
                [resizeable]="true"
                [headerCheckboxable]="true"
                [checkboxable]="true"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="80"
                name="{{ '车主名' | translate }}"
                prop="owner_name"
                headerClass="text-left"
                cellClass="text-left"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="150"
                name="{{ '车牌号' | translate }}"
                prop="plate_number"
                headerClass="text-left"
                cellClass="text-left"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="80"
                name="{{ '设备号' | translate }}"
                prop="device_no"
                headerClass="text-left"
                cellClass="text-left"
              ></ngx-datatable-column>
              <ngx-datatable-column
                [width]="120"
                name="{{ '设备类型' | translate }}"
                prop="is_wireLess"
                headerClass="text-left"
                cellClass="text-left"
              >
                <ng-template let-row="row" ngx-datatable-cell-template>
                  <span *ngIf="row.is_wireLess == 0" translate>有线设备</span>
                  <span *ngIf="row.is_wireLess == 1" translate>无线设备</span>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column
                [width]="150"
                name="{{ '设备状态' | translate }}"
                prop="status"
                headerClass="text-left"
                cellClass="text-left"
              >
                <ng-template let-row="row" ngx-datatable-cell-template>
                  {{ deviceStatusInfo[row?.status || DeviceStatus.Invalid].name | translate }}
                </ng-template>
              </ngx-datatable-column>
            </ngx-datatable>
            <br />

            <div class="footer-style">
              <nopage-datatable-footer
                #boundFooter
                [currentNumber]="boundCurrentNumber"
                [totalNumber]="boundtotalNumber"
                (getTotal)="getBoundTotal()"
                [checkTurnPage]="BoundTurnPage.bind(this)"
              ></nopage-datatable-footer>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="text_right button-row">
  <button type="button" nz-button nzType="primary" (click)="back()">
    {{ '返回' | translate }}
  </button>
</div>

<ng-template #filterPopover>
  <div nz-row [nzGutter]="[0, 8]">
    <div nz-col [nzSpan]="24">
      <nz-checkbox-wrapper nz-row [nzGutter]="[0, 8]">
        <div nz-col [nzSpan]="24" *ngFor="let item of deviceStatusList">
          <span nz-checkbox [nzValue]="item.value" [(ngModel)]="item.checked">
            {{ item.label | translate }}
          </span>
        </div>
      </nz-checkbox-wrapper>
    </div>
    <div nz-col [nzSpan]="24">
      <div nz-row nzAlign="middle" nzJustify="end" [nzGutter]="[8, 0]">
        <div nz-col>
          <button nz-button nzSize="small" (click)="reset()">{{ '重置' | translate }}</button>
        </div>
        <div nz-col>
          <button nz-button nzSize="small" nzType="primary" (click)="determine()">
            {{ '确定' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
