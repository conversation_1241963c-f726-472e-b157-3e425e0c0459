import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  ChangeDetectorRef,
  ViewChild,
  AfterViewInit,
  OnChanges
} from '@angular/core';

import { finalize } from 'rxjs/operators';

import { DatatableComponent } from '@swimlane/ngx-datatable';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate } from '@app/shared/models/type';

import { HourTime } from '@app/account-settings/models/account';
import { FenceShapeType } from '@app/map/models/map';
import { Fence } from '@app/fence-management/shared/models/fence';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { DeviceStatus, deviceStatusInfo } from '@app/data-management/device-management/shared/models/device';
import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';

import { FenceUiMapComponent } from '@app/map/components/fence-ui-map/fence-ui-map.component';

declare var $: any;
@Component({
  selector: 'app-fence-ui',
  templateUrl: './fence-ui.component.html',
  styleUrls: ['./fence-ui.component.scss']
})
export class FenceUiComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() title: string;
  @Input() fenceId: number;
  @Input() unEditable: boolean;
  @Input() saving = false;
  @Input()
  get fence(): Fence {
    return this._fence;
  }

  set fence(fence: Fence) {
    this._fence = fence;
    if (fence) {
      this.editLoad();
    }
  }

  @Output() save: EventEmitter<Fence> = new EventEmitter<Fence>();

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('unboundDt') unboundDatatable: DatatableComponent;
  @ViewChild('boundDt') boundDatatable: DatatableComponent;
  @ViewChild('unboundFooter') unboundFooter: NoPageDatatableFooterComponent;
  @ViewChild('boundFooter') boundFooter: NoPageDatatableFooterComponent;
  @ViewChild('unboundNgxQuery') unboundNgxQuery: NgxQueryComponent;
  @ViewChild('boundNgxQuery') boundNgxQuery: NgxQueryComponent;

  @ViewChild(FenceUiMapComponent) fenceUiMapComponent: FenceUiMapComponent;

  log: Logger;
  loading = false;
  bindLoading = false;
  showDistrictFence = false; // 是否展示行政区围栏

  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  accountSetting = accountSetting;
  HourTime = HourTime;

  // 地图配置
  mapType: number = 1; //1:谷歌,2：百度
  initPosition: any; // 初始化定位
  map: any;

  // 组织机构
  organizationValue: string = '-1';
  nodes: Array<any> = [];

  unboundList: Array<any>; // 未绑定列表
  selectedUnbound: Array<any> = [];
  UnboundEvent: any;
  unboundParams: any;
  unboundCurrentNumber = 10;
  unboundTotalNumber: number;

  boundList: Array<any>;
  selectedBound: Array<any> = [];
  BoundEvent: any;
  boundCurrentNumber = 10;
  boundtotalNumber: number = 0;

  isSearchFirst: boolean = true; // 进行了第一次search查询
  firstLoadList: boolean = true; // 第一次加载列表
  queryInput: string; // 为绑定列表输入框查询

  filterVisible = false;
  deviceStatusList: Array<{ label: string; value: string; checked: boolean }> = [
    { label: '在线', value: '2', checked: false },
    { label: '离线', value: '1', checked: false },
    { label: '未启用', value: '0', checked: false }
  ];

  private _fence: Fence = null;

  // 画围栏所需参数
  fenceSpecial: any = {
    centerPoint: {}, // 圆心坐标
    radius: 0, // 圆半径
    administrative: '', // 行政区域名称
    provinceValue: '', // 省选中的value
    provinceName: '', // 名称
    cityValue: '', // 市选中的value
    cityName: '',
    districtValue: '', // 区选中的value
    districtName: ''
  };
  fencePoints: Array<any> = [];
  fenceArea: number = 0; // 面积
  shapeType: any = '1'; // 围栏形状的类型
  FenceShapeType = FenceShapeType;
  fenceShape: FenceShapeType; // 围栏形状

  // 围栏参数
  orgId: string; // 组织机构id
  fenceName: string; // 围栏名称
  fenceNameError: string; // 围栏名称输入错误提示

  orgError: string; // 请输入所属机构
  fenceError: string; // 请输入围栏名称
  startTimeError: string; // 请输入开始时间
  endTimeError: string; // 请输入结束时间
  drawFenceError: string; // 请先选择围栏形状
  endTimeLessError: string; // 结束时间不能小于开始时间
  unchangedName: any = '';

  fenceDescription: string; // 围栏描述

  enable: any = true; // 是否启用
  fenceAlertType: any = 0; // 报警类型
  beginDateTime: Date; // 开始时间
  endDateTime: Date; // 结束时间

  districtId: any; // 行政区传入id
  deviceId: Array<any> = []; // 设备Id
  // 省级列表参数
  selectProvince: Array<any> = []; // 列表
  selectProvinceValue: any; // value值
  // 市级列表参数
  selectCity: Array<any> = [];
  selectCityValue: any;
  // 区级列表参数
  selectDistrict: Array<any> = [];
  selectDistrictValue: any;

  // 查询所需参数配置
  mode: QueryMode = QueryMode.plainCollapse;
  isShowCollapse = false;
  unboundQueryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'search', op: 'cn' },
          { field: 'status', op: 'eq' }
        ],
        groups: []
      }
    }
  ];
  boundQueryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'fenceId', op: 'cn' }],
        groups: []
      }
    }
  ];

  // tslint:disable-next-line:no-any
  compareFn = (o1: any, o2: any) => (o1 && o2 ? o1.id === o2.id : o1 === o2);

  constructor(
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private fenceManagementService: FenceManagementService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    if (this.fenceId) {
      this.boundQueryTemplates[0].template.rules[0].data = this.fenceId.toString();
    }
    this.getUserAllOrganizations();
    this.getProvinceList();
  }

  ngAfterViewInit(): void {
    this.changeDetectorRef.detectChanges();
  }

  ngOnChanges() {
    if (this.fenceId) {
      this.boundQueryTemplates[0].template.rules[0].data = this.fenceId.toString();
    }
  }

  endTimeChange() {
    this.endTimeError = '';
    this.endTimeLessError = '';
  }

  // 获取机构
  getUserAllOrganizations() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => (this.nodes = organNodes));
  }

  // 编辑加载内容
  editLoad() {
    this.getBoundTotal();
    this.organizationValue = this.fence.orgId;
    this.loadUnboundList(this.UnboundEvent);
    this.unchangedName = this.fence.fenceName;
    this.fenceName = this.fence.fenceName;
    this.fenceAlertType = this.fence.fenceAlertType;
    this.beginDateTime = new Date(Number(this.fence.beginDate));
    this.endDateTime = new Date(Number(this.fence.endDate));
    if (this.fence.fenceDescription) {
      this.fenceDescription = this.fence.fenceDescription;
    }
    this.fenceShape = this.fence.fenceShape;
    this.fenceSpecial = JSON.parse(this.fence.fenceSpecialJson);
    this.fencePoints = this.fence.fencePoints;
    this.fenceArea = this.fence.fenceArea;
  }

  // 时间去掉秒
  timeModification(time: any) {
    const dateArr = time.split(' ')[0];
    const timeArr = time.split(' ')[1].split(':');
    const returnTime = dateArr + ' ' + timeArr[0] + ':' + timeArr[1];
    return returnTime;
  }

  // 围栏名称查重
  checkFenceName() {
    this.fenceNameError = '';
    this.fenceError = '';
    if (this.fenceName !== this.unchangedName) {
      this.fenceManagementService.checkFenceName(this.fenceName).subscribe(
        (response) => {
          if (response.code === 0) {
            if (response.msg === 'true') {
              this.translate.get('此围栏名称已存在，请重新输入').subscribe((res: string) => {
                this.fenceNameError = res;
              });
            }
          } else {
            console.log('围栏名称查重失败', response.msg);
          }
        },
        (error) => console.log('围栏名称查重失败', error)
      );
    }
  }

  shapeTypeChange() {
    this.drawFenceError = '';
    this.fenceUiMapComponent.changePolygonType();
    if (!this.fenceId) {
      this.fenceSpecial.administrative = '';
      this.fenceSpecial.centerPoint = '';
      this.fenceSpecial.radius = '';
      this.fenceSpecial.provinceValue = '';
      this.fenceSpecial.cityValue = '';
      this.fenceSpecial.districtValue = '';
      this.fencePoints = [];
    }
  }

  changeFenceShape(fenceShape: FenceShapeType) {
    this.fenceShape = fenceShape;
    this.fenceUiMapComponent.changePolygonType(this.fenceShape);
    this.drawFenceError = '';
    this.fencePoints = [];
    this.fenceArea = 0;
    this.fenceSpecial.radius = 0;
    this.fenceSpecial.centerPoint = null;
  }

  getPolygonInfo(info: any) {
    console.log('getPolygonInfo', info);
    switch (this.fenceShape) {
      case FenceShapeType.Circle:
        this.fencePoints = info.fencePoints;
        this.fenceArea = info.fenceArea;
        this.fenceSpecial.radius = info.radius;
        this.fenceSpecial.centerPoint = info.centerPoint;
        break;
      case FenceShapeType.Polygon:
        this.fencePoints = info.fencePoints;
        this.fenceArea = info.fenceArea;
        break;
      default:
        break;
    }
  }

  // 画行政区域
  administrativeDraw(param: any, type?: string) {
    this.fenceShape = FenceShapeType.District;
  }
  // 获取省级列表
  getProvinceList() {
    this.fenceManagementService.getProvince().subscribe(
      (response) => {
        if (response.code === 0) {
          for (let i = 0; i < response.data.length; i++) {
            this.selectProvince.push({
              name: response.data[i].name,
              code: response.data[i].code,
              id: response.data[i].id
            });
          }
          // console.log(this.selectProvince, 'selectProvince');
        } else {
          console.log('省级列表获取失败', response.msg);
        }
      },
      (error) => console.log('省级列表获取失败', error)
    );
  }

  // 省级列表更改
  changeProvince(param: any) {
    this.drawFenceError = '';
    this.fenceSpecial.provinceName = '';
    this.fenceSpecial.cityName = '';
    this.fenceSpecial.districtName = '';
    this.selectCity = [];
    this.selectDistrict = [];
    this.fencePoints = [];
    this.selectCityValue = null;
    if (param) {
      this.fenceSpecial.provinceValue = this.selectProvinceValue;
      const code = this.selectProvinceValue.code;
      this.districtId = this.selectProvinceValue.id;
      this.fenceSpecial.provinceName = this.selectProvinceValue.name;
      this.fenceSpecial.administrative = this.fenceSpecial.provinceName;
      this.getCityList(code);
      this.administrativeDraw(this.fenceSpecial.administrative, 'province');
    } else {
      this.map.clearOverlays();
    }
  }
  // 获取市级列表
  getCityList(param: any) {
    this.fenceManagementService.getCity(param).subscribe(
      (response) => {
        if (response.code === 0) {
          for (let i = 0; i < response.data.length; i++) {
            this.selectCity.push({
              name: response.data[i].name,
              code: response.data[i].code,
              id: response.data[i].id
            });
          }
          // console.log(this.selectCity, 'city');
        } else {
          console.log('市级列表获取失败', response.msg);
        }
      },
      (error) => console.log('市级列表获取失败', error)
    );
  }
  // 市级列表更改
  changeCity(param: any) {
    this.fenceSpecial.cityName = '';
    this.fenceSpecial.districtName = '';
    this.selectDistrict = [];
    this.selectDistrictValue = null;
    this.fencePoints = [];
    if (param) {
      this.fenceSpecial.cityValue = this.selectCityValue;
      const code = this.selectCityValue.code;
      this.districtId = this.selectCityValue.id;
      this.fenceSpecial.cityName = this.selectCityValue.name;
      this.fenceSpecial.administrative = this.fenceSpecial.cityName;
      this.getDistrictList(code);
      this.administrativeDraw(this.fenceSpecial.administrative, 'city');
    } else {
      this.map.clearOverlays();
    }
  }
  // 获取区级列表
  getDistrictList(param: any) {
    this.fenceManagementService.getDistrict(param).subscribe(
      (response) => {
        if (response.code === 0) {
          for (let i = 0; i < response.data.length; i++) {
            this.selectDistrict.push({
              name: response.data[i].name,
              code: response.data[i].code,
              id: response.data[i].id
            });
          }
          console.log(this.selectDistrict, 'selectDistrict');
        } else {
          console.log('区级列表获取失败', response.msg);
        }
      },
      (error) => console.log('区级列表获取失败', error)
    );
  }
  // 区级列表更改
  changeDistrict(param: any) {
    this.fenceSpecial.districtName = '';
    this.fencePoints = [];
    if (param) {
      this.fenceSpecial.districtValue = this.selectDistrictValue;
      const code = this.selectDistrictValue.code;
      this.districtId = this.selectDistrictValue.id;
      this.fenceSpecial.districtName = this.selectDistrictValue.name;
      this.fenceSpecial.administrative = this.fenceSpecial.districtName;
      this.administrativeDraw(this.fenceSpecial.administrative, 'district');
    } else {
      this.map.clearOverlays();
    }
  }

  orgIdChanged() {
    this.orgError = '';
    this.loadUnboundList(this.UnboundEvent);
  }

  // 绑定设备列表
  // 获取未绑定列表
  loadUnboundList(event: any) {
    this.UnboundEvent = event;
    if (this.firstLoadList) {
      this.unboundParams = event.page;
      this.unboundParams.filters.push({
        field: 'orgId',
        op: 'eq',
        term: this.organizationValue
      });
      this.firstLoadList = false;
    }
    this.unboundParams.filters.forEach((filter: any) => {
      if (filter.field === 'orgId') {
        filter.term = this.organizationValue;
      }
    });
    this.unboundParams.pageIndex = event.page.pageIndex;
    this.unboundParams.pageSize = event.page.pageSize;
    this.loading = true;
    this.unboundFooter.showTotalElements = true;
    this.fenceManagementService
      .getUnbound(this.unboundParams)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.code === 0) {
            this.unboundList = response.data;
            this.unboundCurrentNumber = this.unboundList.length;
          } else {
            console.log('设备列表获取失败。', response.msg);
          }
        },
        (error) => console.log('设备列表获取失败。', error)
      );
  }
  // 回车查询未绑定列表
  executeQuery() {
    this.unboundNgxQuery.resetQueryTemplate(); // 重置查询，主要目的为将页码重置
    if (this.isSearchFirst) {
      this.unboundParams.filters.push({
        field: 'search',
        op: 'cn',
        term: this.queryInput
      });
    } else {
      const filters = this.unboundParams.filters;
      for (let i = 0; i < filters.length; i++) {
        if (filters[i].field === 'search') {
          this.unboundParams.filters[i].term = this.queryInput;
        }
      }
    }
    this.isSearchFirst = false;
    this.loadUnboundList(this.UnboundEvent);
  }

  cleanQuery() {
    this.unboundNgxQuery.resetQueryTemplate(); // 重置查询，主要目的为将页码重置
    this.queryInput = undefined;
    this.executeQuery();
  }

  // 确定
  determine() {
    this.unboundNgxQuery.resetQueryTemplate(); // 重置查询，主要目的为将页码重置
    const deviceStatus = JSON.stringify(this.deviceStatusList.filter((item) => item.checked).map((item) => item.value));
    let statusFilter = this.unboundParams.filters.find((item: any) => item.field === 'status');
    if (statusFilter) {
      statusFilter.term = deviceStatus;
    } else {
      this.unboundParams.filters.push({ field: 'status', op: 'eq', term: deviceStatus });
    }
    this.filterVisible = false;
    this.loadUnboundList(this.UnboundEvent);
  }

  // 获取未绑定列表的总数
  getUnboundTotal() {
    this.fenceManagementService.getUnboundNumber(this.unboundParams).subscribe(
      (response) => {
        if (response.code === 0) {
          this.unboundTotalNumber = response.count;
        } else {
          console.log('设备列表总数获取失败。', response.msg);
        }
      },
      (error) => console.log('设备列表总数获取失败。', error)
    );
  }
  // 未绑定翻页
  UnboundTurnPage() {
    return this.unboundNgxQuery.validateQuery();
  }
  // 获取绑定列表
  lodeBoundList(event: any) {
    this.BoundEvent = event;
    this.boundFooter.showTotalElements = true;
    const params = event.page;
    if (this.fenceId) {
      if (params.filters.length === 0) {
        params.filters.push({
          field: 'fenceId',
          op: 'eq',
          term: this.fenceId.toString()
        });
      }
    }
    this.bindLoading = true;
    this.fenceManagementService
      .getBound(params)
      .pipe(finalize(() => (this.bindLoading = false)))
      .subscribe(
        (response) => {
          if (response.code === 0) {
            this.boundList = response.data;
            this.boundCurrentNumber = this.boundList.length;
          } else {
            console.log('设备列表获取失败。', response.msg);
          }
        },
        (error) => console.log('设备列表获取失败。', error)
      );
  }
  // 未绑定列表选择
  onUnboundSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selectedUnbound = event.selected;
    }
  }
  // 已绑定列表选择
  onBoundSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selectedBound = event.selected;
    }
  }
  // 获取绑定列表总数
  getBoundTotal() {
    const params = this.BoundEvent.page;
    this.fenceManagementService.getBoundNumber(params).subscribe(
      (response) => {
        if (response.code === 0) {
          this.boundtotalNumber = response.count;
        } else {
          console.log('未绑定设备列表总数获取失败。', response.msg);
        }
      },
      (error) => console.log('未绑定设备列表总数获取失败。', error)
    );
  }
  // 绑定列表翻页
  BoundTurnPage() {
    return this.boundNgxQuery.validateQuery();
  }

  // 重置
  reset() {
    this.deviceStatusList = this.deviceStatusList.map((item) => {
      item.checked = false;
      return item;
    });
    this.determine(); // 重置后进行查询
  }

  // 移入列表
  addMenu() {
    if (this.fenceId) {
      if (this.selectedUnbound.length === 0) {
        this.translate.get('请选择设备。').subscribe((res: string) => {
          this.log.error(res);
        });
      } else {
        for (let i = 0; i < this.selectedUnbound.length; i++) {
          this.deviceId.push(this.selectedUnbound[i].id);
        }
        const params = {
          deviceId: this.deviceId,
          fenceId: this.fenceId
        };
        this.saving = true;
        this.fenceManagementService
          .addDevice(params)
          .pipe(finalize(() => (this.saving = false)))
          .subscribe(
            (response) => {
              if (response.code === 0) {
                this.lodeBoundList(this.BoundEvent);
                this.getBoundTotal();
                this.deviceId = [];
                this.selectedUnbound = [];
              }
            },
            (error) => console.log('移入失败。', error)
          );
      }
    } else {
      this.translate.get('请先保存围栏信息后再绑定设备。').subscribe((res: string) => {
        this.log.error(res);
      });
    }
  }
  // 移出列表
  removeMenu() {
    if (this.fenceId) {
      if (this.selectedBound.length === 0) {
        this.translate.get('请选择设备。').subscribe((res: string) => {
          this.log.error(res);
        });
      } else {
        for (let i = 0; i < this.selectedBound.length; i++) {
          this.deviceId.push(this.selectedBound[i].id);
        }
        const params = {
          deviceId: this.deviceId,
          fenceId: this.fenceId
        };
        this.saving = true;
        this.fenceManagementService
          .removeDevice(params)
          .pipe(finalize(() => (this.saving = false)))
          .subscribe(
            (response) => {
              if (response.code === 0) {
                this.lodeBoundList(this.BoundEvent);
                this.getBoundTotal();
                this.deviceId = [];
                this.selectedBound = [];
              }
            },
            (error) => console.log('移出失败。', error)
          );
      }
    } else {
      this.translate.get('请先保存围栏信息后再绑定设备。').subscribe((res: string) => {
        this.log.error(res);
      });
    }
  }

  checkInput() {
    let isValid = true;
    if (!this.organizationValue) {
      this.orgError = '请输入所属机构';
      isValid = false;
      this.translate.get('请输入所属机构').subscribe((res: string) => {
        this.orgError = res;
      });
    }
    if (this.organizationValue === '-1') {
      this.orgError = '请输入所属机构';
      isValid = false;
      this.translate.get('请输入所属机构').subscribe((res: string) => {
        this.orgError = res;
      });
    }
    if (!this.fenceName) {
      this.fenceError = '请输入围栏名称';
      isValid = false;
      this.translate.get('请输入围栏名称').subscribe((res: string) => {
        this.fenceError = res;
      });
    }
    if (this.fenceNameError) {
      isValid = false;
      switch (this.fenceNameError) {
        case '围栏名称重复':
          break;
        case '请输入汉字、字母、数字':
          break;
        default:
          break;
      }
    }
    if (!this.beginDateTime) {
      this.startTimeError = '请输入开始时间';
      isValid = false;
      this.translate.get('请输入开始时间').subscribe((res: string) => {
        this.startTimeError = res;
      });
    }
    if (!this.endDateTime) {
      this.endTimeError = '请输入结束时间';
      isValid = false;
      this.translate.get('请输入结束时间').subscribe((res: string) => {
        this.endTimeError = res;
      });
    }
    if (this.beginDateTime && this.endDateTime && this.endDateTime.getTime() <= this.beginDateTime.getTime()) {
      this.endTimeLessError = '结束时间不应早于或等于开始时间';
      isValid = false;
      this.translate.get('结束时间不应早于或等于开始时间').subscribe((res: string) => {
        this.endTimeLessError = res;
      });
    }
    if (this.fencePoints.length === 0) {
      this.drawFenceError = '请先绘制围栏';
      this.translate.get('请先绘制围栏').subscribe((res: string) => {
        this.drawFenceError = res;
      });
      isValid = false;
    }
    return isValid;
  }

  // 提交
  submit() {
    if (this.checkInput()) {
      const begin = this.beginDateTime.getTime();
      const end = this.endDateTime.getTime();
      if (this.fenceShape === FenceShapeType.District) {
        this.fenceArea = null;
      }
      if (this.fenceShape !== FenceShapeType.Circle) {
        // 不是圆形的时候清空相关数据
        this.fenceSpecial.centerPoint = null;
        this.fenceSpecial.radius = null;
      }
      if (this.fenceShape !== FenceShapeType.District) {
        // 由行政区变成其他形状的围栏时，行政区相关数据清空
        this.districtId = null;
        this.fenceSpecial.administrative = null;
        this.fenceSpecial.provinceValue = null;
        this.fenceSpecial.provinceName = null;
        this.fenceSpecial.cityValue = null;
        this.fenceSpecial.cityName = null;
        this.fenceSpecial.districtValue = null;
        this.fenceSpecial.districtName = null;
      }
      const fenceSpecialJson = JSON.stringify(this.fenceSpecial);

      const params: any = {
        orgId: this.organizationValue,
        fenceName: this.fenceName,
        fenceDescription: this.fenceDescription,
        fenceShape: this.fenceShape,
        fencePolygons: this.fencePoints,
        fenceArea: this.fenceArea,
        fenceAlertType: this.fenceAlertType,
        beginDateTime: begin,
        endDateTime: end,
        fenceType: 0,
        enabled: 1,
        districtId: this.districtId,
        fenceSpecialJson: fenceSpecialJson,
        id: this.fenceId
      };
      if (!this.saving) {
        this.save.next(params);
      }
    }
  }

  back() {
    window.history.go(-1);
  }
}
