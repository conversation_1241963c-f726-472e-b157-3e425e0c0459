import { Component, OnInit, EventEmitter } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { map, finalize } from 'rxjs/operators';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { Fence } from '@app/fence-management/shared/models/fence';
import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';
@Component({
  selector: 'app-edit-fence',
  templateUrl: './edit-fence.component.html',
  styleUrls: ['./edit-fence.component.scss']
})
export class EditFenceComponent implements OnInit {
  log: Logger;
  title = '围栏编辑';
  saving = false;
  unEditable: boolean = false; // 是否可编辑
  fence$ = new EventEmitter<Fence>();

  fenceId: any;

  constructor(
    private translate: TranslateService,
    private route: ActivatedRoute,
    private fenceManagementService: FenceManagementService,
    private loggerFactory: LoggerFactory
  ) {
    this.translate.get('围栏编辑').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {
    this.load();
  }

  submit(fence: Fence) {
    this.saving = true;
    this.fenceManagementService
      .updFenceInfo(fence)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        () => {
          this.unEditable = true;
          console.log(`围栏 ${fence.fenceName} 编辑成功!`);
        },
        (error) => console.log(`围栏 ${fence.fenceName} 保存失败，失败信息:`, error)
      );
  }

  private load() {
    // console.log('调用load');
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.fenceId = id;
        this.fenceManagementService.fenceDetail(id).subscribe(
          (response) => {
            if (response.code === 0) {
              this.fence$.next(response.data);
            } else {
              console.log('围栏信息获取失败', response.msg);
            }
          },
          (error) => console.log('围栏信息获取失败, 失败信息：', error)
        );
      }
    });
  }
}
