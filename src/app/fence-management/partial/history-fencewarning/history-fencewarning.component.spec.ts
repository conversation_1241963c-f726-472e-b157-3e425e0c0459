/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { HistoryFencewarningComponent } from './history-fencewarning.component';

describe('HistoryFencewarningComponent', () => {
  let component: HistoryFencewarningComponent;
  let fixture: ComponentFixture<HistoryFencewarningComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [HistoryFencewarningComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HistoryFencewarningComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
