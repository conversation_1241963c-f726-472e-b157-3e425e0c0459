import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { timer } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { startOfDay, addDays } from 'date-fns';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { QueryTemplate } from '@app/shared/models/type';
import { EventService } from '@app/events-management/shared/event.service';

@Component({
  selector: 'app-history-fencewarning',
  templateUrl: './history-fencewarning.component.html',
  styleUrls: ['./history-fencewarning.component.scss']
})
export class HistoryFencewarningComponent implements OnInit {
  @Input() data: any;

  log: Logger;
  title: string = '历史预警列表';
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'startEndTime', op: 'cn' },
          { field: 'deviceVehiclQuery', op: 'cn' }
        ],
        groups: []
      }
    }
  ];
  startEndTime: any;
  queryContent: any;
  loading = false;

  historyWarningList: any;
  currentNumber = 10;
  totalNumber: number;
  event: any;
  datatable: any;

  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  constructor(
    public activeModal: BsModalRef,
    private eventService: EventService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {}

  export() {
    const params = this.loadProperty(this.event.page);

    this.eventService
      .historyFenceExportByFenceId(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.ok) {
          const link = document.createElement('a');
          const blob = new Blob([response.body], { type: 'application/zip' });
          link.setAttribute('href', window.URL.createObjectURL(blob));
          this.translate.get('围栏预警记录').subscribe((res) => {
            link.setAttribute('download', res + '-' + new Date().getTime() + '.xlsx');
          });

          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.refreshData();
        }
      });
  }

  refreshData() {
    this.ngxQuery.resetQueryTemplate();
  }

  getTotal() {
    console.log(this.event);
    const params = this.loadProperty(this.event.page);
    this.eventService
      .getHistoryWarnTotalNumberByFenceId(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response.code === '200') {
          this.totalNumber = response.data;
        }
      });
  }
  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    console.log(page);
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'startEndTime' && rule.data) {
        map['startTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['endTimeStamp'] = addDays(startOfDay(rule.data[1]), 1).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.fenceId = this.data.id + '';
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  changeDate() {
    timer().subscribe(() => this.ngxQuery.executeQuery());
  }

  loadHistoryWarning(event: any) {
    this.event = event;

    this.footer.showTotalElements = true;
    const params = this.loadProperty(event.page);
    this.datatable = event.datatable;
    this.loading = true;
    this.eventService
      .getHistoryFenceWarningListByFenceId(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response.code === '200') {
          this.historyWarningList = response.data;
          this.currentNumber = this.historyWarningList.length;
          console.log(this.historyWarningList);
        }
      });
  }
}
