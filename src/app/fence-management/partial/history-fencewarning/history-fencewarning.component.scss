.modal-body {
  background-color: #f4f7fb;
}

.toolbar {
  display: grid;
  grid-template-columns: 90% 10%;

  .querybtn {
    position: absolute;
    top: 0;
    right: 0;
    border: none;
    border-radius: 0px 5px 5px 0px;
    width: 41px;
    height: 38px;
    background-color: #f1f3f8;

    .la {
      margin-left: -6px;
    }
  }
}

.footer-style {
  position: relative;
  display: flex;
  justify-content: flex-end;
  height: 50px;
}

.address {
  display: block;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.body_header {
  padding-left: 30px;
  display: grid;
  grid-template-columns: 25% 25% 25% 25%;

  div {
    display: grid;
    grid-template-rows: 50% 50%;

    .body_header_title {
      margin-top: 30px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Regular;
      font-weight: 400;
      color: #b7bfd4;
    }

    .body_header_text {
      margin-top: 16px;
      margin-bottom: 30px;
      font-size: 16px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      color: #454c62;
    }
  }
}

.header {
  display: grid;
  grid-template-columns: 80% 20%;
  padding-top: 10px;
  padding-bottom: 10px;
}

.search-date {
  padding-left: 5px;
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  border: 1px solid #d3d6de;
  border-radius: 4px;
  width: 300px;
}

.height {
  height: 32px;
}

.query-input {
  width: 253px;
  height: 32px;
  background: #ffffff;
  border: none;
}

.date-logo {
  margin-right: 3px;
}

.delete-logo {
  margin-right: 10px;
}

.interval {
  width: 100%;
  margin-left: -80px;
}

:host ::ng-deep {
  .dropmenu {
    display: none !important;
  }

  ngx-query.full-screen .card .card-body {
    border-bottom: none !important;
  }

  .card {
    background-color: rgba(0, 0, 0, 0);
  }

  .ngx-datatable.material {
    min-height: 252px !important;
  }
}
