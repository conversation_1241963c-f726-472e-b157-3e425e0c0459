<div class="modal-header">
  <h5 class="modal-title" translate>预警记录</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div class="m-portlet header">
    <div class="interval">
      <ngx-query
        [hidden]="false"
        [datePickerReadonly]="false"
        [columnNumber]="2"
        class="full-screen no-header"
        #ngxQuery
        [queryTemplates]="queryTemplates"
        [showModeButtons]="true"
        [mode]="mode"
        [showPlainCollapseToolBar]="false"
      >
        <ngx-query-field [name]="'startEndTime'" [label]="''" [type]="'date'">
          <ng-template
            ngx-query-value-input-template
            dataType="date"
            let-rules="rules"
            let-rule="rule"
            let-dataIndex="dataIndex"
            let-placeholder="placeholder"
          >
            <nz-range-picker
              [nzFormat]="accountSetting.dateFormat"
              [nzAllowClear]="false"
              (ngModelChange)="changeDate()"
              [(ngModel)]="rule.datas[dataIndex]"
              appLocalNzDateTime
            ></nz-range-picker>
          </ng-template>
        </ngx-query-field>
        <ngx-query-field [name]="'deviceVehiclQuery'" [label]="''" [type]="'string'">
          <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
            <nz-input-group [nzSuffix]="suffixIconSearch">
              <input
                nz-input
                type="text"
                placeholder="{{ '请输入车主名/车牌号/车架号/设备号' | translate }}"
                [(ngModel)]="rule.datas[dataIndex]"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <i nz-icon nzType="search" nzTheme="outline" class="pointer" (click)="ngxQuery.executeQuery()"></i>
            </ng-template>
          </ng-template>
        </ngx-query-field>
      </ngx-query>
    </div>

    <span style="margin-top: 17px">
      <button nz-button nzType="primary" (click)="export()">
        {{ '导出' | translate }}
      </button>
    </span>
  </div>
  <div class="m-portlet">
    <div class="body_header">
      <div>
        <span class="body_header_title" translate>围栏名称</span>
        <span class="body_header_text">{{ data.fenceName }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>预警类型</span>
        <span class="body_header_text">{{ data.fenceAlertTypeStr | translate }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>起止时间</span>
        <span class="body_header_text">
          {{ data.beginDate | localDate : accountSetting.dateFormat }} ～
          {{ data.endDate | localDate : accountSetting.dateFormat }}
        </span>
      </div>
      <div>
        <span class="body_header_title" translate>围栏形状</span>
        <span class="body_header_text" *ngIf="data.fenceShape === 2">{{ '行政区围栏' | translate }}</span>
        <span class="body_header_text" *ngIf="data.fenceShape !== 2">{{ '自定义围栏' | translate }}</span>
      </div>
    </div>
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="historyWarningList"
        [count]="6"
        appNgxDataTable
        [saveState]="false"
        [loadingIndicator]="loading"
        [ngxQuery]="ngxQuery"
        (loadValue)="loadHistoryWarning($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [columnMode]="'force'"
        appNgxDataTable
        [selectAllRowsOnPage]="false"
        externalPaging="false"
      >
        <ngx-datatable-column
          [width]="100"
          name="{{ '车主名' | translate }}"
          prop="vehicleOwner"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          [width]="140"
          name="{{ '车牌号' | translate }}"
          prop="vehicleNo"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          [width]="140"
          name="{{ '车架号' | translate }}"
          prop="vin"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          [width]="140"
          name="{{ '设备号' | translate }}"
          prop="deviceNo"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          [width]="160"
          name="{{ '设备型号' | translate }}"
          prop="deviceType"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          [width]="200"
          name="{{ '预警时间' | translate }}"
          prop="alertTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            {{ row.alertTime | localDate : accountSetting.dateTimeFormat }}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [width]="300"
          name="{{ '预警地址' | translate }}"
          prop="address"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <span style="width: 90%">
              <a class="address" title="{{ row.address }}">{{ row.address }}</a>
            </span>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column maxWidth="50" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <img src="/assets/font/refresh_17.png" (click)="refreshData()" style="margin-top: 10px; cursor: pointer" />
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
