import { Component, OnInit, Input, ChangeDetectorRef, AfterViewInit } from '@angular/core';

import { Logger, LoggerFactory } from '@app/core';
import { vehicleStatusInfo, VehicleStatus } from '@app/data-management/vehicle-management/shared/models/vehicle';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';

@Component({
  selector: 'app-detials-card',
  templateUrl: './detials-card.component.html',
  styleUrls: ['./detials-card.component.scss']
})
export class DetialsCardComponent implements OnInit, AfterViewInit {
  @Input() fenceId: number;

  log: Logger;
  vehicleStatusInfo = vehicleStatusInfo;
  VehicleStatus = VehicleStatus;
  accountSetting = accountSetting;

  loading = false;
  fence: any = {};
  flag: boolean = false;
  deviceList: any;

  constructor(
    private loggerFactory: LoggerFactory,
    private fenceManagementService: FenceManagementService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.getFenceDetail();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  getFenceDetail() {
    this.fenceManagementService.fenceDetail(this.fenceId).subscribe((response) => {
      if (response.code === 0) {
        this.fence = response.data;
        this.deviceList = response.deviceList;
        const fenceSpecialJson = JSON.parse(this.fence.fenceSpecialJson);
        if (this.fence.fenceShape === 2) {
          this.fence.cityName =
            fenceSpecialJson.provinceName + fenceSpecialJson.cityName + fenceSpecialJson.districtName;
        }
        if (this.fence.fenceShape === 0) {
          this.fence.rad = (fenceSpecialJson.radius / 1000).toFixed(2);
        }
        this.deviceList.forEach((ele: any) => {
          ele.infoShow = false;
        });
        this.flag = true;
        this.dataConversion(this.fence.fenceShape);
      }
    });
  }

  dataConversion(fenceShape: any) {
    switch (fenceShape) {
      case 0:
        this.fence.fenceShape = '圆形';
        break;
      case 1:
        this.fence.fenceShape = '多边形';
        break;
      case 2:
        this.fence.fenceShape = '行政区围栏';
        break;
      default:
        break;
    }
  }
}
