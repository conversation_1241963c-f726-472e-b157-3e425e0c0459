<div class="detail-card" *ngIf="flag">
  <div class="detail-card-header">
    <div class="detail-card-header-title">
      <img src="/assets/font/title-front.png" />
      {{ '详情' | translate }}
    </div>
  </div>

  <div class="detail-card-body">
    <div class="box">
      <div class="box-header">
        <div class="box-title" translate>围栏信息</div>
      </div>
      <div class="box-body">
        <div class="detail-row">
          <label translate>所属机构</label>
          <div class="detail-value">
            {{ fence?.orgName }}
          </div>
        </div>
        <div class="detail-row">
          <label translate>围栏名称</label>
          <div class="detail-value">
            {{ fence?.fenceName }}
          </div>
        </div>
        <div class="detail-row">
          <label translate>启用状态</label>
          <div class="detail-value">
            <nz-badge [nzColor]="fence.enabled === 1 ? '#38d19d' : '#ff5e5e'"></nz-badge>
            <div style="display: inline-block">
              <span style="margin-right: 20px" [style.color]="fence.enabled === 1 ? '#38d19d' : '#ff5e5e'" translate>
                {{ fence.enabled === 1 ? '启用' : '禁用' }}
              </span>
              <span translate>{{ fence.enabled === 1 ? '有效' : '失效' }}</span>
            </div>
          </div>
        </div>
        <div class="detail-row">
          <label translate>预警类型</label>
          <div class="detail-value">
            {{ fence.fenceAlertTypeStr | translate }}
          </div>
        </div>
        <div class="detail-row">
          <label translate>开始时间</label>
          <div class="detail-value">
            {{ fence.beginDate | localDate : accountSetting.dateTimeHourMinuteFormat }}
          </div>
        </div>
        <div class="detail-row">
          <label translate>结束时间</label>
          <div class="detail-value">
            {{ fence.endDate | localDate : accountSetting.dateTimeHourMinuteFormat }}
          </div>
        </div>

        <div class="detail-row" *ngIf="fence.fenceShape === '行政区围栏'">
          <label translate>围栏形状</label>
          <div class="detail-value">
            {{ fence.fenceShape | translate }}
          </div>
        </div>
        <div class="detail-row" *ngIf="fence.fenceShape !== '行政区围栏'">
          <label translate>围栏形状</label>
          <div class="detail-value">
            {{ fence.fenceShape + '围栏' | translate }}
          </div>
        </div>
        <div class="detail-row" *ngIf="fence.fenceShape === '圆形'">
          <label translate>围栏区域</label>
          <div class="detail-value" *ngIf="fence.fenceArea">
            {{ fence.fenceArea }}km² （
            <span translate>半径</span>
            {{ fence.rad }}km）
          </div>
        </div>
        <div class="detail-row" *ngIf="fence.fenceShape === '多边形'">
          <label translate>围栏区域</label>
          <div class="detail-value" *ngIf="fence.fenceArea">{{ fence.fenceArea }}km²</div>
        </div>
        <div class="detail-row" *ngIf="fence.fenceShape === '行政区围栏'">
          <label translate>围栏区域</label>
          <div class="detail-value">
            {{ fence.cityName }}
          </div>
        </div>

        <div class="detail-row">
          <label translate>围栏描述</label>
          <div class="detail-value">
            {{ fence.fenceDescription }}
          </div>
        </div>
      </div>
    </div>

    <div class="box">
      <div class="box-header">
        <div class="box-title" translate>绑定设备信息</div>
        <div>{{ '已绑定设备数量:' | translate }}{{ fence.deviceNum }}</div>
      </div>

      <div class="box-body">
        <div *ngIf="!deviceList || deviceList?.length === 0">
          <span class="no-device" translate>暂无绑定设备</span>
        </div>

        <div class="sub-box" *ngFor="let item of deviceList; let i = index">
          <div class="sub-box-header device-header">
            <div class="device-title">
              <label>
                <span translate>No.</span>
                {{ i + 1 }}
              </label>
            </div>
            <div>{{ item.deviceno }}</div>
            <div>
              <span translate>{{ item.iswireless === 1 ? '无线' : '有线' }}</span>
              <nz-badge [nzColor]="item.status === 0 ? 'yellow' : item.status === 1 ? 'red' : 'green'"></nz-badge>
            </div>
            <i
              nz-icon
              [nzType]="item.infoShow ? 'up' : 'down'"
              nzTheme="outline"
              class="pointer"
              (click)="item.infoShow = !item.infoShow"
            ></i>
          </div>

          <div *ngIf="item.infoShow" class="sub-box-body">
            <div class="detail-row">
              <label translate>车主名</label>
              <div class="detail-value">
                {{ item.ownerName }}
              </div>
            </div>

            <div class="detail-row">
              <label translate>车牌号</label>
              <div class="detail-value">
                <span class="detail-value">{{ item.plateNumber }}</span>
                <img
                  class="img-car"
                  [src]="vehicleStatusInfo[item?.vehicleStatus || VehicleStatus.Offline].legendIcon"
                />
              </div>
            </div>

            <div class="detail-row">
              <label translate>车辆型号</label>
              <div class="detail-value">
                {{ item.modelName }}
              </div>
            </div>

            <div class="detail-row">
              <label translate>车架号</label>
              <div class="detail-value">
                {{ item.vin }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="detail-card-footer">
    <button type="button" nz-button nzSize="small" [routerLink]="['/fence', fenceId, 'edit']">
      {{ '编辑' | translate }}
    </button>
    <button type="button" nz-button nzSize="small" nzType="primary" routerLink="/fence">
      {{ '返回' | translate }}
    </button>
  </div>
</div>
