import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { finalize } from 'rxjs/operators';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { FenceManagementService } from '@app/fence-management/shared/fence-management.service';
import { Fence } from '@app/fence-management/shared/models/fence';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-new-fence',
  templateUrl: './new-fence.component.html',
  styleUrls: ['./new-fence.component.scss']
})
export class NewFenceComponent implements OnInit {
  log: Logger;
  title = '新建围栏';
  fenceId: number;
  saving = false;
  unEditable: boolean = false; // 是否可编辑

  constructor(
    private router: Router,
    private translate: TranslateService,
    private fenceManagementService: FenceManagementService,
    private loggerFactory: LoggerFactory
  ) {
    this.translate.get('新建围栏').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {}

  submit(fence: Fence) {
    this.saving = true;
    // console.log(fence, '这是新建接受到的数据');
    this.fenceManagementService
      .addFenceInfo(fence)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (response) => {
          // console.log(response, '提交传回来的值');
          this.fenceId = parseInt(response.msg, 10);
          this.unEditable = true;
        },
        (error) => console.log('围栏创建失败。', error)
      );
  }
}
