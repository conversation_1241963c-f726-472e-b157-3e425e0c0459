<!-- begin:: Page -->
<div class="m-grid m-grid--hor m-grid--root m-page">
  <div
    class="m-grid__item m-grid__item--fluid m-grid m-grid--ver-desktop m-grid--desktop m-grid--tablet-and-mobile m-grid--hor-tablet-and-mobile m-login m-login--1 m-login--singin"
    id="m_login"
  >
    <div class="m-grid__item m-grid__item--order-tablet-and-mobile-2 m-login__aside">
      <div class="m-stack m-stack--hor m-stack--desktop">
        <div class="m-stack__item m-stack__item--fluid">
          <div class="m-login__wrapper">
            <div class="m-login__logo">
              <img src="/assets/media/app/img/logos/lunz.png" />
            </div>
            <div *ngIf="forgetPassword" class="m-login__signin animated fadeIn">
              <div class="m-login__head">
                <h3 class="m-login__title">
                  {{ appName }}
                  <small>{{ version }}</small>
                </h3>
              </div>
              <form class="m-login__form m-form" (ngSubmit)="login()" [formGroup]="loginForm" novalidate>
                <div class="form-group m-form__group">
                  <input
                    appAutoFocus
                    class="form-control m-input m-login__form-input-first"
                    type="text"
                    placeholder="帐号"
                    formControlName="username"
                    autocomplete="off"
                  />
                </div>
                <div class="form-group m-form__group">
                  <input
                    class="form-control m-input m-login__form-input--last"
                    type="password"
                    placeholder="密码"
                    formControlName="password"
                  />
                </div>
                <div class="row m-login__form-sub">
                  <div class="col m--align-left">
                    <label class="m-checkbox m-checkbox--focus">
                      <input type="checkbox" name="remember" formControlName="remember" />
                      记住我的登录
                      <span></span>
                    </label>
                  </div>
                  <div class="col m--align-right">
                    <a (click)="showForgetPassword()" id="m_login_forget_password" class="m-link">忘记密码？</a>
                  </div>
                </div>
                <div class="m-login__form-action">
                  <button
                    id="m_login_signin_submit"
                    type="submit"
                    class="btn btn-focus m-btn m-btn--pill m-btn--custom m-btn--air"
                    [disabled]="loginForm.invalid || isLoading"
                  >
                    登&nbsp;&nbsp;录
                  </button>
                </div>
                <div
                  [hidden]="!error || isLoading"
                  class="m-alert m-alert--icon m-alert--icon-solid m-alert--outline alert alert-danger alert-dismissible fade show"
                  role="alert"
                >
                  <div class="m-alert__icon">
                    <i class="la la-warning"></i>
                    <span></span>
                  </div>
                  <div class="m-alert__text">
                    {{ error }}
                  </div>
                </div>
              </form>
            </div>
            <div *ngIf="!forgetPassword" class="m-login__forget-password animated flipInX">
              <div class="m-login__head">
                <h3 class="m-login__title">忘记密码 ?</h3>
                <div class="m-login__desc">
                  <!-- Enter your email to reset your password: -->
                </div>
              </div>
              <div class="m-login__form m-form">
                <div class="m-stack m-stack--ver m-stack--general">
                  <div class="m-stack__item m-stack__item--center">请联系系统管理员重置您的密码。</div>
                </div>
                <div class="m-login__form-action">
                  <button
                    (click)="showForgetPassword()"
                    id="m_login_forget_password_cancel"
                    class="btn btn-outline-focus m-btn m-btn--pill m-btn--custom"
                  >
                    返回登录
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="m-stack__item m-stack__item--center">
          <div class="m-login__account">
            <span class="m-login__account-msg">2017 &copy; 青岛轮子软件科技有限公司</span>
            &nbsp;|&nbsp;
            <a href="http://www.lunztech.cn" target="_blank" class="m-link">联系我们</a>
          </div>
        </div>
      </div>
    </div>
    <div
      class="m-grid__item m-grid__item--fluid m-grid m-grid--center m-grid--hor m-grid__item--order-tablet-and-mobile-1 m-login__content"
    >
      <div class="m-grid__item m-grid__item--middle">
        <h3 class="m-login__welcome">轮子科技</h3>
        <p class="m-login__msg">
          人、代码、生活。
          <br />
          你所向往的，就是你应为之努力的！
        </p>
      </div>
    </div>
  </div>
</div>
<!-- end:: Page -->
