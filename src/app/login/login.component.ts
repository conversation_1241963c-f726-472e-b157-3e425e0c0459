import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';

import { finalize } from 'rxjs/operators';
import { environment } from '@env/environment';
import { AuthenticationService, Logger, LoggerFactory } from '@app/core';
import { CreateSubscriptionService } from '@app/shared';

declare const URI: any;

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  log: Logger;
  loginForm: FormGroup;

  isLoading = false;
  error: string = null;
  forgetPassword: boolean = true;
  appName: string = environment.appName;
  version: string = environment.version;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private authenticationService: AuthenticationService,
    private subscriptionService: CreateSubscriptionService
  ) {
    this.createForm();
    this.log = this.loggerFactory.getLogger('Login');
  }

  ngOnInit() {}

  showForgetPassword() {
    this.forgetPassword = !this.forgetPassword;
  }

  login() {
    this.isLoading = true;
    sessionStorage.removeItem('logout');
    this.authenticationService
      .login(this.loginForm.value)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.loginForm.markAsPristine();
        })
      )
      .subscribe(
        (credentials) => {
          this.log.debug(`${credentials.username} Login Successfully.`);

          const uri = new URI(location.href);
          const queryString = uri.query();
          let navigatePromise;
          if (queryString && queryString !== null && queryString !== '') {
            const query = URI.parseQuery(queryString.toLowerCase());
            if (query.returnurl && query.returnurl !== null && query.returnurl !== '') {
              this.router.navigateByUrl(query.returnurl).then((s) => {
                this.subscriptionService.login$.next(s);
              });
              return;
            }
          }
          navigatePromise = this.router.navigate(['/']).then((s) => {
            this.subscriptionService.login$.next(s);
          });
        },
        (error) => (this.error = error.error.message)
      );
  }

  private createForm() {
    this.loginForm = this.formBuilder.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
      remember: false
    });
  }
}
