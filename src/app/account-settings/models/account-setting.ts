import { Theme } from '@app/shared/models/theme';
import { AccountSetting, Language, MapType, HourTime, dateFormats } from './account';

const defaultTimeZone = 'Asia/Shanghai';
let timeZone = defaultTimeZone;
const isWhereAbouts =
  window.location.href.includes('glcrm-ui-dev.lunztech.cn') || window.location.href.includes('whereabouts.online');
try {
  timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
} catch (error) {
  timeZone = defaultTimeZone;
}

export let accountSetting: AccountSetting = {
  language: Language['en-US'],
  mapType: MapType.Osm,
  timeZone,

  lng: 113.**************,
  lat: 22.**************,
  address: `5th F, Building 2, Jia'an Science & Technology Park, Shenzhen, China`,

  defaultLng: 113.**************,
  defaultLat: 22.**************,
  defaultAddress: `5th F, Building 2, Jia'an Science & Technology Park, Shenzhen, China`,

  hourTime: HourTime.Hour24,
  dateFormat: dateFormats[0],
  timeFormat: 'HH:mm:ss',
  dateTimeFormat: `${dateFormats[0]} HH:mm:ss`,
  defaultLogo: isWhereAbouts ? '/assets/font/glcrm_logo.png' : '/assets/font/glcrm_logo_old.png',
  logo: isWhereAbouts ? '/assets/font/glcrm_logo.png' : '/assets/font/glcrm_logo_old.png',
  theme: Theme.Dark,
  get timeHourMinuteFormat(): string {
    return this.timeFormat.replace(':ss', '');
  },
  get dateTimeHourMinuteFormat(): string {
    const timeFormat = this.timeFormat.replace(':ss', '');
    return `${this.dateFormat} ${timeFormat}`;
  }
};
