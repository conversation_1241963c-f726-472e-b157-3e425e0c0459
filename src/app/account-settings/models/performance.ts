import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';

export class LinkMans {
  @IsNotEmpty({ message: '姓名不能为空' })
  linkmanName: string;

  @IsNotEmpty({ message: '联系电话不能为空' })
  linkmanTel: string;

  @MinLength(6, { message: '请输入6-50个字符的邮箱' })
  @IsEmail({}, { message: '请输入6-50个字符的邮箱' })
  @IsNotEmpty({ message: '邮箱不能为空' })
  linkmanEmail: string;
  remark: string;
}
export class FunctionJson {
  @ValidateNested({ each: true })
  linkMans: LinkMans[] = [new LinkMans()];
  operateWay: number = 2; // 1:门店自行确认 no ，2.平台协助确认 yes
}
export class PerformanceModel {
  id: any; // 传id 则为编辑，不传则为新增
  isOpen: boolean = false;
  functionJson: FunctionJson = new FunctionJson();
  orgId: string;
  businessType: number = OpportunityType.accident;
}

export enum OpportunityType {
  accident = 101
}
