import { Theme } from '@app/shared/models/theme';

export enum Language {
  /** 中文 */
  'zh-CN' = 1,
  /** 英文 */
  'en-US',
  /** 西班牙文 */
  'es-MX'
}

/** 地图类型 */
export enum MapType {
  /** 谷歌地图 */
  Google = 1,
  /** 谷歌卫星图 */
  GoogleSatellite,
  /** 谷歌混合图 */
  GoogleHybrid,
  /** 百度地图 */
  Baidu,
  /** 百度卫星图 */
  BaiduSatellite,
  /** 百度混合图 */
  BaiduHybrid,
  /** Osm */
  Osm
}

/** 小时制 */
export enum HourTime {
  /** 12小时制 */
  Hour12 = 1,
  /** 24小时制 */
  Hour24
}

export const dateFormats: string[] = [
  'yyyy-MM-dd',
  'dd-MM-yyyy',
  'yyyy.MM.dd',
  'dd.MM.yyyy',
  'yyyy/MM/dd',
  'dd/M/yy',
  'M/dd/yy',
  'dd/MM/yyyy'
];

export interface Organization {
  departmentId: string;
  departmentName: string;
  companyId: string;
  companyName: string;
}

export interface Account {
  /** id */
  id?: string;
  /** 组织id */
  organizationId: string;
  /** 登录名, 长度为1 - 50个字符, 必填 */
  loginName: string;
  /** 用户姓名, 长度为1 - 50个字符, 必填 */
  name: string;
  /** email */
  email: string;
  /** 开始日期 */
  beginDay: string;
  /** 结束日期 */
  endDay: string;
  /** 角色id */
  roleId: string;
  /** 手机号 */
  tel?: string;
  /** 备注, 长度0 - 400个字符 */
  remarks?: string;
  /** 用户简称, 长度1 - 100字符 */
  shortName?: string;
  dateRange?: [Date, Date];
  /** 组织id(旧用户中心) */
  departmentId?: string;
  /** 组织名称(旧用户中心) */
  departmentName?: string;
  /** 详情接口: 角色列表 */
  roles?: Array<{ id: string; name: string }>;
  organizations?: Array<Organization>;
  /** ui判断账号过期,账号有效期是否小于今天 */
  expired?: boolean;
  /** 判断是否跟登录账号属于平级机构 */
  isSameUserOrgan?: boolean;
}

export interface AccountSetting {
  /** 用户Id（account的id） */
  userId?: string;
  /** 后台数据库默认为null/undefined 1 中文；2 英文 3 西班牙文 */
  language?: Language | undefined | null;
  /** 后台默认为 MapType.Google */
  mapType?: MapType;
  /** 当前时区 */
  timeZone?: string;
  /** 地址经度 */
  lng?: number;
  /** 地址纬度 */
  lat?: number;
  /** 详细地址 */
  address?: string;
  /** 默认地址经度 */
  defaultLat?: number;
  /** 默认地址纬度 */
  defaultLng?: number;
  /** 默认详细地址 */
  defaultAddress?: string;
  /** 小时制 1: 12小时; 2: 24小时; 默认24小时 */
  hourTime?: HourTime;
  /** 日期格式 默认: yyyy/MM/dd */
  dateFormat?: string;
  /** 时间格式 12小时制: h:mm:ss a; 24小时制: HH:mm:ss */
  timeFormat?: string;
  /** 时分时间格式 */
  timeHourMinuteFormat?: string;
  /** 日期&时间格式 `${this.dateFormat} ${this.timeFormat}` */
  dateTimeFormat?: string;
  /** 日期&时分时间格式 `${this.dateFormat} ${this.timeFormat}` */
  dateTimeHourMinuteFormat?: string;
  /** logo url地址 */
  logo?: string;
  /** 默认logo  */
  defaultLogo?: string;
  /** 主题 */
  theme?: Theme;
}
