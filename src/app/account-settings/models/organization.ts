export interface Organization {
  /** 机构id */
  id?: string;
  /** 父机构id */
  parentId: string;
  /** 机构名称 */
  name: string;
  /** 组织邮箱 */
  email: string;
  /** 机构电话 */
  officePhone?: string;
  /** 机构地址 */
  officeAddress?: string;
  /** 备注 */
  remarks?: string;
  /** 是否启用 0:禁用 1:启用 默认:1 */
  enabled: number;
  loginName?: string;
}

export interface OrganVehicle {
  /** 车辆*/
  carId: string;
  /** 设备id*/
  deviceId: string;
  /** 设备号*/
  deviceNo: string;
  /** 设备*/
  modelName: string;
  /** 所有者*/
  ownerName: string;
  /** 车牌号*/
  plateNumber: string;
  /** 是否无线设备 1:是; 0:否 */
  isWireLess: '1' | '0';
  /** 是否有传感器 1:是; 0:否 */
  isSensor: '1' | '0';
  type?: 'organ' | 'vehicle';
}

export interface OrganVehicleNode {
  /** id*/
  id?: string;
  /** 组织名称*/
  orgName?: string;
  /** 父级*/
  parentId?: string;
  cars?: OrganVehicle[];
  children: OrganVehicleNode[];
  type?: 'organ' | 'vehicle';
  deviceNo?: string;
  isWireLess: '1' | '0';
  isSensor: '1' | '0';
}
