<div class="tab-body">
  <form nz-form [formGroup]="form">
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzFor="address" nzRequired>
        {{ '地址' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20" [nzErrorTip]="addressErrorTpl">
        <textarea
          rows="2"
          nz-input
          id="address"
          formControlName="address"
          [placeholder]="'请输入地址' | translate"
          maxlength="100"
        ></textarea>
        <ng-template #addressErrorTpl let-control>
          <ng-container *ngIf="control?.dirty && control?.errors">{{ '请输入地址' | translate }}</ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '定位点' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20" [nzErrorTip]="lngErrorTpl">
        <div class="user-map" id="user-map">
          <app-user-location-map [coordinate]="coordinate" (change)="changeCoordinate($event)"></app-user-location-map>
        </div>
        <input type="hidden" formControlName="lng" />
        <ng-template #lngErrorTpl let-control>
          <ng-container *ngIf="control?.dirty && control?.errors">{{ '请设置定位点' | translate }}</ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>

<div class="tab-footer">
  <button type="button" nz-button (click)="reset()">
    {{ '取消' | translate }}
  </button>
  <button type="submit" nz-button nzType="primary" (click)="submit()">
    {{ '确定' | translate }}
  </button>
</div>
