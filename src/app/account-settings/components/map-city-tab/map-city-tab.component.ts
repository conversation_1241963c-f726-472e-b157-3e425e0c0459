import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { of } from 'rxjs';
import { catchError, finalize, map } from 'rxjs/operators';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { Coordinate } from 'ol/coordinate';
import { TranslateService } from '@ngx-translate/core';

import { HttpMapService } from '@app/map/services/http-map.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { mapConfig } from '@app/map/models/map-config';
import { AccountService } from '@app/account-settings/services/account.service';
import { accountSetting } from '@app/account-settings/models/account-setting';

@Component({
  selector: 'app-map-city-tab',
  templateUrl: './map-city-tab.component.html',
  styleUrls: ['./map-city-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MapCityTabComponent implements OnInit {
  log: Logger;
  form: FormGroup;
  saving = false;
  coordinate: Coordinate = mapConfig.center;
  translates = {
    更新成功: ''
  };

  constructor(
    public accountService: AccountService,
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private httpMapService: HttpMapService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {
    this.getAddress();
  }

  changeCoordinate(e: { coordinate: Coordinate; address: string }) {
    const getLocation$ = e.address ? of(e.address) : this.getLocation(e.coordinate);
    getLocation$.pipe(finalize(() => this.cd.markForCheck())).subscribe((address) => {
      this.form.patchValue({
        lng: e.coordinate[0],
        lat: e.coordinate[1],
        address
      });
    });
  }

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const params = this.form.getRawValue();
    this.accountService
      .setAccountSetting(params)
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(res.message);
            return;
          }
          accountSetting.lng = params.lng;
          accountSetting.lat = params.lat;
          accountSetting.address = params.address;
          mapConfig.center = [accountSetting.lng, accountSetting.lat];
          this.log.success(this.translates['更新成功']);
        },
        (err) => this.log.error(err)
      );
  }

  reset() {
    this.form.reset(accountSetting || {});
  }

  getAddress() {
    this.saving = true;
    let address = '';
    const detail$ = accountSetting.address ? of(accountSetting.address) : this.getLocation(this.coordinate);
    detail$
      .pipe(
        map((addr) => (address = addr)),
        finalize(() => {
          this.saving = false;
          this.form.patchValue({ address });
          accountSetting.address = address;
          this.cd.markForCheck();
        })
      )
      .subscribe();
  }

  getLocation(coordinate: Coordinate) {
    if (coordinate[0] === accountSetting.defaultLng && coordinate[1] === accountSetting.defaultLat) {
      return of(accountSetting.defaultAddress);
    }
    return this.httpMapService.getLocation(coordinate).pipe(
      map((res) => res.data || ''),
      catchError(() => of('')),
      finalize(() => this.cd.markForCheck())
    );
  }

  buildForm() {
    this.form = this.formBuilder.group({
      lng: [this.coordinate[0], [Validators.required]],
      lat: [this.coordinate[1]],
      address: [{ value: null, disabled: true }]
    });
  }
}
