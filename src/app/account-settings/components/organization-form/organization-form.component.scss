@import "../../../styles/themes/theme-value";
:host {
  display: block;
  height: 100%;
}

.organ-form {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 0 8px 8px 8px;
  overflow-y: auto;

  &-header {
    padding: 0 0 15px 0;
  }

  &-body {
    flex: 1;
    height: 0;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    position: absolute;
    bottom: 0;
    right: 0;
  }
}

.title {
  position: relative;
  padding-left: 10px;
  line-height: 28px;
  color: #38476e;
  font-weight: 500;

  &::before {
    width: 3px;
    height: 7px;
    content: " ";
    position: absolute;
    top: 50%;
    left: 0;
    border-radius: 1px;
    transform: translateY(-50%);
    background-color: $system-color;
  }
}

.count {
  position: absolute;
  right: 0;
  text-align: right;
  font-weight: 400;
  color: #575e72;
  line-height: 12px;
  font-size: 12px;
  background-color: #fff;
}

textarea {
  margin-top: 2px;
}

:host ::ng-deep {
  .ant-form-item-label > label {
    font-weight: 400;
    color: #575e72;
  }

  .ant-form-explain,
  .ant-form-extra {
    min-height: auto;
    font-size: 12px;
    line-height: 22px;
  }
  .ant-tag {
    margin-bottom: 3px;
  }
  .ant-form-item-label {
    white-space: normal;
  }
}
.selected-box {
  width: 100%;
  min-width: 0;
  padding: 7px 11px 4px;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 3.5px;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .content {
    flex: 1;
  }

  &.disabled-box {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}
