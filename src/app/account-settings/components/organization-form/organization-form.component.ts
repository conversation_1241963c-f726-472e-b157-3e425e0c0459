import { cloneDeep } from 'lodash';
import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { CustomValidators } from 'ngx-custom-validators';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { LoggerFactory, Logger, Dialogs } from '@app/core';

import { PageMode } from '@app/shared/models/page-mode';
import { regex } from '@app/shared/utils/regex';
import { Organization } from '@app/account-settings/models/organization';
import { OrganizationService } from './../../services/organization.service';
import { SelectAccountComponent } from '../select-account/select-account.component';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-organization-form',
  templateUrl: './organization-form.component.html',
  styleUrls: ['./organization-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrganizationFormComponent implements OnInit {
  @Input() widgets: string[] = [];
  @Input() pageMode: PageMode = PageMode.Detail;
  @Input()
  get organId(): string | undefined {
    return this._organId;
  }
  set organId(val: string | undefined) {
    this._organId = val;
    this.getDetail();
    this.getSpecialistList();
  }
  @Input() set organInfo(value: NzTreeNodeOptions) {
    if (!value) {
      return;
    }
    this.organTopId = value.id;
  }

  @Output() save = new EventEmitter<any>();

  log: Logger;
  translates = {
    更新成功: '',
    更新失败: ''
  };

  PageMode = PageMode;
  form: FormGroup;
  saving = false;
  organization: Organization;

  _organId: string | undefined;
  specialistList: Organization[] = [];
  specialistListClone: Organization[] = [];
  organTopId: string | undefined; // 顶级的i机构id
  isDisabled: boolean;

  constructor(
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private organizationService: OrganizationService,
    private modalService: BsModalService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {}
  selectSpecialists() {
    const initialState = { organId: this.organTopId };
    const modalRef: BsModalRef = this.modalService.show(SelectAccountComponent, {
      initialState,
      class: 'modal-account-settings',
      ignoreBackdropClick: true
    });
    modalRef.content.action.subscribe((value: any) => {
      if (value) {
        this.specialistList = this.uniqueArr(this.specialistList, value);
        this.cd.markForCheck();
      }
    });
  }
  uniqueArr(arr1: any[], arr2: any[]) {
    let arr1Set = new Set(arr1.map((item) => item.id)); // 转换为Set
    let arr2Map = new Map(arr2.map((item) => [item.id, item]));
    let newArr = arr1
      .map((item) => {
        // 实现覆盖
        if (arr2Map.has(item.id)) {
          return { ...item, ...arr2Map.get(item.id) };
        }
        return { ...item };
      })
      .concat(...arr2.filter((item) => !arr1Set.has(item.id))); // 拼接多的部分，即没覆盖的部分
    // 去重
    let uniqueArr = Array.from(new Set(newArr.map((item) => item.id))).map((id) => {
      return newArr.find((item) => item.id === id);
    });
    return uniqueArr;
  }
  onClose(idx: number) {
    this.specialistList.splice(idx, 1);
  }
  getSpecialistList() {
    this.organizationService.getSpecialistList(this.organId).subscribe(
      (res) => {
        this.specialistList = res.data || [];
        this.specialistListClone = res.data || [];
      },
      () => {
        this.specialistList = [];
        this.specialistListClone = [];
      }
    );
  }
  submit() {
    if (this.saving) {
      return;
    }
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const params = this.form.getRawValue();
    this.organizationService
      .edit(params)
      .pipe(
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            this.saving = false;
            this.log.error(this.translates['更新失败']);
            return;
          }
          this.save.emit();
          this.getDetail(true);
        },
        () => {
          this.saving = false;
          this.log.error(this.translates['更新失败']);
        }
      );
  }
  saveSpecialistList(id: string) {
    this.saving = true;

    this.organizationService
      .saveSpecialistList({
        orgId: id,
        userInfos: this.specialistList.map((it) => {
          return { id: it.id, name: it.name, loginName: it.loginName };
        })
      })
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res: any) => {
          if (!res.success) {
            this.log.error(this.translates['更新失败']);
            return;
          }
          this.specialistListClone = cloneDeep(this.specialistList);
          this.log.success(this.translates['更新成功']);
          this.save.emit();
        },
        () => {
          this.log.error(this.translates['更新失败']);
        }
      );
  }
  reset() {
    this.form.reset(this.organization || {});
    this.specialistList = cloneDeep(this.specialistListClone);
  }

  getDetail(isSubmit?: boolean) {
    if (!this.organId) {
      return;
    }
    this.saving = true;
    this.organizationService
      .detail(this.organId)
      .pipe(
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe((res) => {
        if (isSubmit) {
          this.saveSpecialistList(res.id);
        }

        if (!res) {
          this.saving = false;
          return;
        }
        this.saving = false;
        res.officeAddress = res.officeAddress || '';
        res.remarks = res.remarks || '';
        this.organization = res;
        const disabled = this.pageMode === PageMode.Detail;
        this.isDisabled = disabled;
        this.form.reset({
          id: res.id,
          parentId: res.parentId || null,
          name: { value: res.name || null, disabled },
          email: { value: res.email || null, disabled },
          enabled: res.enabled || 1,
          officePhone: { value: res.officePhone || null, disabled },
          officeAddress: { value: res.officeAddress || '', disabled },
          remarks: { value: res.email || '', disabled }
        });
      });
  }

  buildForm() {
    this.form = this.formBuilder.group({
      id: [null, [Validators.required]],
      parentId: [null],
      name: [
        null,
        [
          Validators.required,
          CustomValidators.rangeLength([1, 100]),
          Validators.pattern(regex.bookString),
          Validators.pattern(regex.space)
        ]
      ],
      email: [
        null,
        [
          Validators.required,
          CustomValidators.email,
          CustomValidators.rangeLength([6, 50]),
          Validators.pattern(regex.space)
        ]
      ],
      enabled: [1],
      officePhone: [null],
      officeAddress: [''],
      remarks: ['']
    });
  }
}
