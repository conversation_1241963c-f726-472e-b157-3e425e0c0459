<div class="organ-form">
  <div class="organ-form-header">
    <div class="title" translate>机构信息</div>
  </div>
  <div class="organ-form-body">
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzRequired nzFor="name">
          {{ '所属机构' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="nameErrorTpl">
          <input
            nz-input
            id="name"
            formControlName="name"
            minlength="1"
            maxlength="100"
            zr-trim
            [placeholder]="'请输入机构名称' | translate"
          />
          <ng-template #nameErrorTpl let-control>
            <div *ngIf="control.errors.pattern; else nameOtherErrorTpl" translate>不允许输入空格</div>
            <ng-template #nameOtherErrorTpl>
              <div translate [translateParams]="{ minLen: '1', maxLen: '100' }">长度x-xx，不限制输入内容</div>
            </ng-template>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzRequired nzFor="email">
          {{ '邮箱' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="emailErrorTpl">
          <input
            nz-input
            id="email"
            minlength="6"
            maxlength="50"
            formControlName="email"
            [placeholder]="'请输入邮箱' | translate"
          />
          <ng-template #emailErrorTpl let-control>
            <div *ngIf="control.errors.pattern; else emailOtherErrorTpl" translate>不允许输入空格</div>
            <ng-template #emailOtherErrorTpl>
              <div translate [translateParams]="{ minLen: '6', maxLen: '50' }">长度x-xx，不限制输入内容</div>
            </ng-template>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzFor="officePhone">
          {{ 'account.联系电话' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="officePhoneErrorTpl">
          <input
            nz-input
            id="officePhone"
            formControlName="officePhone"
            [placeholder]="'account.请输入联系电话' | translate"
            maxlength="50"
          />
          <ng-template #officePhoneErrorTpl let-control>
            <div translate [translateParams]="{ Len: '50' }">长度x，不限制输入内容</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzFor="officeAddress">
          {{ '办公地址' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="officeAddressErrorTpl">
          <textarea
            rows="2"
            nz-input
            id="officeAddress"
            formControlName="officeAddress"
            [placeholder]="'请输入办公地址' | translate"
            maxlength="500"
          ></textarea>
          <div class="count">{{ form.get('officeAddress').value.length }}/500</div>
          <ng-template #officeAddressErrorTpl let-control>
            <div translate [translateParams]="{ Len: '500' }">长度x，不限制输入内容</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzFor="remarks">
          {{ '备注' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14">
          <textarea
            rows="4"
            nz-input
            id="remarks"
            formControlName="remarks"
            [placeholder]="'请输入备注' | translate"
            maxlength="400"
          ></textarea>
          <div class="count">{{ form.get('remarks').value.length }}/400</div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="8" nzFor="remarks">
          {{ '平台运营专家' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14">
          <div class="selected-box {{ isDisabled ? 'disabled-box' : '' }}">
            <div class="content">
              <ng-container *ngFor="let item of specialistList; let i = index">
                <nz-tag
                  [nzColor]="isDisabled ? '' : 'geekblue'"
                  [nzMode]="isDisabled ? 'default' : 'closeable'"
                  (nzOnClose)="onClose(i)"
                >
                  {{ item.loginName }} - {{ item.name }}
                </nz-tag>
              </ng-container>
            </div>

            <button
              *ngIf="widgets.includes('group_edit')"
              nz-button
              nzType="primary"
              (click)="selectSpecialists()"
              [disabled]="isDisabled"
              [nzSize]="'small'"
            >
              {{ 'select' | translate }}
            </button>
          </div>
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>
  <div class="organ-form-footer">
    <button type="button" nz-button (click)="reset()" [disabled]="pageMode === PageMode.Detail">
      {{ '取消' | translate }}
    </button>
    <button
      *ngIf="widgets.includes('group_edit')"
      type="submit"
      nz-button
      nzType="primary"
      (click)="submit()"
      [disabled]="pageMode === PageMode.Detail"
    >
      {{ '确定' | translate }}
    </button>
  </div>
</div>
