<div class="account-form">
  <div class="account-form-header">
    <button (click)="onBack()" nz-button nzSize="small">
      <i nz-icon nzType="left"></i>
    </button>
    <span *ngIf="id" translate>{{ id ? '编辑' : '新建' }}</span>
  </div>

  <div class="account-form-body">
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired>
          {{ '机构' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="organizationIdErrorTpl">
          <nz-tree-select
            [nzNodes]="organTree"
            nzShowSearch
            formControlName="organizationId"
            [nzPlaceHolder]="'请输入机构名称' | translate"
          ></nz-tree-select>
          <ng-template #organizationIdErrorTpl let-control>
            {{ '请选择所属机构' | translate }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="loginName">
          {{ '登录账号' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="loginNameErrorTpl">
          <input
            nz-input
            id="loginName"
            formControlName="loginName"
            minlength="1"
            maxlength="50"
            [placeholder]="'请输入登录账号' | translate"
          />
          <ng-template #loginNameErrorTpl let-control>
            <div *ngIf="control.errors.pattern; else loginNameOtherErrorTpl" translate>不允许输入空格</div>
            <ng-template #loginNameOtherErrorTpl>
              <div translate [translateParams]="{ minLen: '1', maxLen: '50' }">长度x-xx，不限制输入内容</div>
            </ng-template>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="name">
          {{ '所属账号名称' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="nameErrorTpl">
          <input
            nz-input
            id="name"
            formControlName="name"
            minlength="1"
            maxlength="50"
            [placeholder]="'请输入所属账号名称' | translate"
          />
          <ng-template #nameErrorTpl let-control>
            <div translate [translateParams]="{ minLen: '1', maxLen: '50' }">长度x-xx，不限制输入内容</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="email">
          {{ '邮箱' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="emailErrorTpl">
          <input nz-input id="email" formControlName="email" maxlength="50" [placeholder]="'请输入邮箱' | translate" />
          <ng-template #emailErrorTpl let-control>
            <div *ngIf="control.errors.pattern; else emailOtherErrorTpl" translate>不允许输入空格</div>
            <ng-template #emailOtherErrorTpl>
              <div translate [translateParams]="{ minLen: '6', maxLen: '50' }">长度x-xx，不限制输入内容</div>
            </ng-template>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="beginDay">
          {{ '账号有效期' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="dateRangeErrorTpl">
          <nz-range-picker
            [nzFormat]="accountSetting.dateFormat"
            nzDropdownClassName="disable-date-range-input"
            nzAllowClear
            formControlName="dateRange"
            [nzDisabledDate]="disabledDate"
            (nzOnOpenChange)="toggleDateRangeModal($event)"
            appLocalNzDateTime
          ></nz-range-picker>
          <ng-template #dateRangeErrorTpl let-control>
            {{ '请选择账号有效期' | translate }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="roleId">
          {{ '角色' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="roleIdErrorTpl">
          <nz-select formControlName="roleId" nzAllowClear nzShowSearch [nzPlaceHolder]="'请选择角色' | translate">
            <nz-option
              *ngFor="let item of roles"
              [nzValue]="item.id"
              [nzLabel]="item.name"
              [nzDisabled]="!!item.disabled"
            ></nz-option>
          </nz-select>
          <ng-template #roleIdErrorTpl let-control>
            {{ '请选择角色' | translate }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzFor="otherContact">
          {{ 'account.联系电话' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="otherContactErrorTpl">
          <input
            nz-input
            id="otherContact"
            formControlName="otherContact"
            maxlength="50"
            [placeholder]="'account.请输入联系电话' | translate"
          />
          <ng-template #otherContactErrorTpl let-control>
            <div translate [translateParams]="{ Len: '50' }">长度x，不限制输入内容</div>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzFor="remarks">
          {{ '备注' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14">
          <textarea
            rows="4"
            nz-input
            id="remarks"
            formControlName="remarks"
            [placeholder]="'请输入备注' | translate"
            maxlength="400"
          ></textarea>
          <div class="count">{{ form.get('remarks').value?.length }}/400</div>
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>

  <div class="account-form-footer">
    <button type="button" nz-button (click)="reset()">
      {{ '取消' | translate }}
    </button>
    <button type="submit" nz-button nzType="primary" (click)="submit()">
      {{ '确定' | translate }}
    </button>
  </div>
</div>
