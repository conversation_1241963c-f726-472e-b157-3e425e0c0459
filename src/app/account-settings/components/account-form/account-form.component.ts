import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, of, timer } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { CustomValidators } from 'ngx-custom-validators';
import { startOfDay, endOfDay } from 'date-fns';
import { differenceInCalendarDays } from 'date-fns';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory, Logger } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { regex } from '@app/shared/utils/regex';
import { Role } from '@app/account-settings/models/role';
import { Account } from '@app/account-settings/models/account';

import { RoleService } from '@app/account-settings/services/role.service';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-account-form',
  templateUrl: './account-form.component.html',
  styleUrls: ['./account-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccountFormComponent implements OnInit {
  @Input()
  get id(): string | undefined {
    return this._id;
  }
  set id(val: string | undefined) {
    this._id = val;
    this.getRoles().subscribe(() => this.getDetail());
  }

  @Input() organTree: NzTreeNodeOptions[] = [];
  @Output() back = new EventEmitter<void>();

  log: Logger;
  translates = {
    新增失败: '',
    新增成功: '',
    更新成功: '',
    更新失败: ''
  };

  accountSetting = accountSetting;
  form: FormGroup;
  saving = false;

  account: Account;
  roles: Role[] = [];

  today = new Date();
  disabledDate = (current: Date): boolean => {
    return differenceInCalendarDays(current, this.today) < 0;
  };

  _id: string | undefined;

  constructor(
    public accountService: AccountService,
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private roleService: RoleService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {}

  onBack() {
    this.back.emit();
  }

  submit() {
    if (this.saving) {
      return;
    }
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    const params = this.form.getRawValue();
    params.beginDay = startOfDay(params.dateRange[0]).getTime();
    params.endDay = endOfDay(params.dateRange[1]).getTime();
    Reflect.deleteProperty(params, 'dateRange');

    let successMsg = this.translates['新增成功'];
    let errorMsg = this.translates['新增失败'];
    let save$ = this.accountService.create(params);
    if (this.id) {
      params.id = this.id;
      successMsg = this.translates['更新成功'];
      errorMsg = this.translates['更新失败'];
      save$ = this.accountService.edit(params);
    }
    save$
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(errorMsg);
            return;
          }
          this.log.success(successMsg);
          this.back.emit();
        },
        (err) => this.log.error(errorMsg)
      );
  }

  reset() {
    this.form.reset(this.account || { remarks: '' });
  }

  toggleDateRangeModal(isOpen: boolean) {
    const dateRange = this.form.get('dateRange');
    let dates = dateRange.value;
    if (!isOpen && dates) {
      const startDate = dates[0];
      const endDate = dates[1];
      // nz-date-range bug 开始时间会大于结束时间
      if (endDate.getTime() < startDate.getTime()) {
        timer(30)
          .pipe(finalize(() => this.cd.markForCheck()))
          .subscribe(() => {
            dates = [endDate, startDate];
            dateRange.setValue(dates);
          });
      }
    }
    // modal弹窗是异步
    timer(300).subscribe(() => {
      document
        .querySelectorAll('.disable-date-range-input input.ant-calendar-input')
        .forEach((item) => item.setAttribute('disabled', 'disabled'));
    });
  }

  getDetail() {
    if (!this.id) {
      return;
    }
    this.saving = true;
    this.accountService
      .detail(this.id)
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe((res) => {
        if (Array.isArray(res) && res.length !== 1) {
          return;
        }
        const account = res[0];
        if (account.beginDay && account.endDay) {
          account.dateRange = [
            new Date(account.beginDay.replace(/\-/g, '/')),
            new Date(account.endDay.replace(/\-/g, '/'))
          ];
        }
        if (Array.isArray(account.roles) && account.roles.length > 0) {
          const roleIds = this.roles.map((role) => role.id);
          // 默认选中当前系统配置的角色
          const currRole = account.roles.find((role) => roleIds.includes(role.id));
          account.roleId = currRole ? currRole.id : null;
          // 拼接用户中心带过来的角色列表
          const userCenterRoles = account.roles
            .filter((role) => !roleIds.includes(role.id))
            .map((role) => ({ ...role, disabled: true }));
          this.roles = [...this.roles, ...userCenterRoles];
        }
        if (Array.isArray(account.organizations) && account.organizations.length > 0) {
          account.organizationId = account.organizations[0].departmentId;
        }
        account.remarks = account.remarks || '';
        this.account = account;
        this.form.patchValue(account);
      });
  }

  getRoles(): Observable<null> {
    if (this.roles.length > 0) {
      return of(null);
    }
    return this.roleService.getPageList().pipe(
      map((res) => {
        if (Array.isArray(res)) {
          this.roles = res;
        }
        return null;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  buildForm() {
    this.form = this.formBuilder.group({
      id: [null],
      organizationId: [null, [Validators.required]],
      loginName: [null, [Validators.required, Validators.pattern(regex.space), Validators.pattern(regex.bookString)]],
      name: [null, [Validators.required]],
      email: [null, [Validators.required, CustomValidators.rangeLength([6, 50]), Validators.pattern(regex.space)]],
      dateRange: [null, [Validators.required]],
      roleId: [null, [Validators.required]],
      otherContact: [null, [Validators.maxLength(50)]],
      remarks: ['']
    });
  }
}
