:host {
  display: block;
  height: 100%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

.account-form {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 0 8px 8px 8px;
  overflow-y: auto;

  &-header {
    display: flex;
    align-items: center;
    padding-left: 20px;
    margin-bottom: 15px;

    > button {
      width: 26px;
      height: 26px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 !important;
    }

    span {
      margin-left: 14px;
      color: #454c62;
      line-height: 18px;
    }
  }

  &-body {
    flex: 1;
    height: 0;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    position: absolute;
    bottom: 0;
    right: 0;
  }
}

.count {
  position: absolute;
  right: 0;
  text-align: right;
  font-weight: 400;
  color: #575e72;
  line-height: 12px;
  font-size: 12px;
  background-color: #fff;
}

textarea {
  margin-top: 2px;
}

.ant-calendar-picker-input {
  height: 32px;
}

:host ::ng-deep {
  .ant-form-item-label > label {
    font-weight: 400;
    color: #575e72;
  }

  .ant-form-explain,
  .ant-form-extra {
    min-height: auto;
    font-size: 12px;
    line-height: 22px;
  }

  .ant-select-selection__rendered {
    line-height: 24px;
  }

  .ant-calendar-picker {
    width: 100%;
  }

  .disable-date-range-input {
    input.ant-calendar-input {
      color: rgba(0, 0, 0, 0.65) !important;
      background-color: transparent !important;
    }

    .ant-calendar-ok-btn {
      color: #fff !important;

      &.ant-calendar-ok-btn-disabled {
        color: rgba(0, 0, 0, 0.25) !important;
      }
    }
  }

  .ant-form-item-label {
    white-space: normal;
  }
}
