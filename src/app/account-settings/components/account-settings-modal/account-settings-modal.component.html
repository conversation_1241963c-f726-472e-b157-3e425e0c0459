<div class="modal-header">
  <h5 class="modal-title" translate>账号设置</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <nz-tabset class="account-setting-tabs" (nzSelectChange)="changeTabs($event)" [nzSelectedIndex]="nzSelectedIndex">
    <nz-tab *ngIf="widgets.includes('mapCity')" [nzTitle]="'客户城市配置' | translate">
      <app-map-city-tab></app-map-city-tab>
    </nz-tab>
    <nz-tab *ngIf="widgets.includes('accountSecurity')" [nzTitle]="'账号密码' | translate">
      <app-account-security-tab></app-account-security-tab>
    </nz-tab>
    <nz-tab *ngIf="widgets.includes('customerManagement')" [nzTitle]="'用户中心' | translate">
      <app-customer-management-tab [widgets]="widgets"></app-customer-management-tab>
    </nz-tab>
    <nz-tab *ngIf="widgets.includes('role')" [nzTitle]="'角色' | translate">
      <app-role-tab [tabIndex]="nzSelectedIndex" [widgets]="widgets"></app-role-tab>
    </nz-tab>
    <nz-tab *ngIf="widgets.includes('logoTheme')" [nzTitle]="'Logo&主题' | translate">
      <app-logo-theme-tab></app-logo-theme-tab>
    </nz-tab>
    <nz-tab *ngIf="widgets.includes('timeFormat')" [nzTitle]="'时间格式' | translate">
      <app-date-time-format-tab></app-date-time-format-tab>
    </nz-tab>
  </nz-tabset>
</div>
