import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ApplyPermissionService } from '@app/shared/services/apply-permission.service';

@Component({
  selector: 'app-account-settings-modal',
  templateUrl: './account-settings-modal.component.html',
  styleUrls: ['./account-settings-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccountSettingsModalComponent implements OnInit {
  nzSelectedIndex = 0;

  currRoute = '/accountSettings';
  widgets: string[] = [];

  constructor(public activeModal: BsModalRef, private applyPermissionService: ApplyPermissionService) {}

  ngOnInit() {
    this.widgets = this.applyPermissionService
      .getPageWidgets(this.currRoute)
      .filter((item) => item.authorised)
      .map((item) => item.id);
  }

  changeTabs(e: any) {
    const { index } = e;
    if (index === this.nzSelectedIndex) {
      return;
    }
    this.nzSelectedIndex = index;
  }
}
