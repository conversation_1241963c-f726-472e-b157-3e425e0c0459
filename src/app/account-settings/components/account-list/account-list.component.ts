import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, Input } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { endOfDay } from 'date-fns';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory, Logger, Dialogs } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { PageMode } from '@app/shared/models/page-mode';
import { Account } from '@app/account-settings/models/account';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-account-list',
  templateUrl: './account-list.component.html',
  styleUrls: ['./account-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccountListComponent implements OnInit {
  @Input() widgets: string[] = [];

  @Input()
  get organTree(): NzTreeNodeOptions[] {
    return this._organTree;
  }
  set organTree(val: NzTreeNodeOptions[]) {
    this._organTree = val || [];
    this.pageMode = PageMode.List;
    this.name = '';
    this.getList();
  }

  log: Logger;
  translates = {
    '真的要删除吗？': '',
    '真的要重置密码吗？': '',
    删除失败: '',
    删除成功: '',
    更新成功: '',
    更新失败: ''
  };

  accountSetting = accountSetting;
  PageMode = PageMode;
  pageMode = PageMode.List;

  name = '';
  list: Account[] = [];
  loading = false;

  user: Account;
  userId = '';
  // 即组织机构id
  departmentId = '';
  userInfo = JSON.parse(localStorage.getItem('userInfo'));

  _organTree: NzTreeNodeOptions[] = [];

  constructor(
    private accountService: AccountService,
    private cd: ChangeDetectorRef,
    private dialogs: Dialogs,
    private LoggerFactory: LoggerFactory,
    private translate: TranslateService
  ) {
    this.log = this.LoggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
    this.departmentId = JSON.parse(localStorage.getItem('userInfo')).departmentId;
  }

  ngOnInit() {}

  add() {
    this.userId = '';
    this.pageMode = PageMode.Add;
  }

  edit(user: Account) {
    this.user = user;
    this.userId = user.id;
    this.pageMode = PageMode.Edit;
  }

  delete(user: Account) {
    this.dialogs.confirm(this.translates['真的要删除吗？']).subscribe(() => {
      this.accountService.delete(user.id).subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(this.translates['删除失败']);
            return;
          }
          this.log.success(this.translates['删除成功']);
          this.getList();
        },
        (err) => this.log.error(this.translates['删除失败'])
      );
    });
  }

  resetPwd(user: Account) {
    this.dialogs.confirm(this.translates['真的要重置密码吗？']).subscribe(() => {
      this.accountService.resetPwd(user.loginName).subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(this.translates['更新失败']);
            return;
          }
          this.log.success(this.translates['更新成功']);
        },
        (err) => this.log.error(this.translates['更新失败'])
      );
    });
  }

  onBack() {
    this.pageMode = PageMode.List;
    this.getList();
  }

  getList() {
    if (!Array.isArray(this.organTree) || this.organTree.length !== 1) {
      return;
    }
    this.loading = true;
    this.accountService
      .getPageList(this.name.trim(), [this.organTree[0].key])
      .pipe(
        finalize(() => {
          this.loading = false;
          this.cd.markForCheck();
        })
      )
      .subscribe((res) => {
        res = res || [];
        const now = new Date();
        this.list = res.map((item) => {
          item.expired = false;
          item.roles = Array.isArray(item.roles) ? item.roles : [];
          // isSameUserOrgan 同级机构禁止修改、删除、重置密码功能
          item.isSameUserOrgan =
            Array.isArray(item.organizations) &&
            item.organizations.length > 0 &&
            item.organizations[0].departmentId === this.departmentId;
          if (item.endDay) {
            const endDay = endOfDay(new Date(item.endDay.replace(/\-/g, '/')));
            item.expired = endDay.getTime() <= now.getTime();
            item.endDay = item.endDay.substring(0, 10);
          }
          return item;
        });
      });
  }
}
