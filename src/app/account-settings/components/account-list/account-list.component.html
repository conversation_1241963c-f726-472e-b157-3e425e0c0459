<div class="account" *ngIf="pageMode === PageMode.List">
  <div class="account-header">
    <nz-input-group [nzSuffix]="suffixIconSearch">
      <input
        type="text"
        nz-input
        placeholder="{{ '账号/所属账号名称' | translate }}"
        (keyup.enter)="getList()"
        [(ngModel)]="name"
      />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <i nz-icon nzType="search" class="pointer" (click)="getList()"></i>
    </ng-template>

    <button *ngIf="widgets.includes('account_add')" nz-button nzType="primary" (click)="add()">
      {{ '新建' | translate }}
    </button>
  </div>
  <div class="account-body">
    <div class="account-list">
      <nz-spin class="spining" *ngIf="loading; else listTpl"></nz-spin>

      <ng-template #listTpl>
        <div class="no-data" *ngIf="list.length == 0">
          <img src="/assets/font/noDate.png" />
          <span class="no-data-title">{{ '暂无数据' | translate }}</span>
        </div>

        <div class="account-item" *ngFor="let item of list">
          <div class="account-info">
            <div class="account-name">{{ item.loginName }}</div>
            <div class="account-other">
              <div class="account-other-info">
                <img src="/assets/media/app/img/icons/user.svg" alt="" />
                {{ item.name || '-' }}
              </div>
              <div class="account-other-info">
                <img src="/assets/media/app/img/icons/role.svg" alt="" />
                <span *ngIf="!item.roles || item.roles.length === 0; else rolesTpl">-</span>
                <ng-template #rolesTpl>
                  <span *ngFor="let role of item.roles; let last = last">{{ role.name }}{{ last ? '' : '、' }}</span>
                </ng-template>
              </div>
              <div class="account-other-info">
                <img src="/assets/media/app/img/icons/email.svg" alt="" />
                {{ item.email || '-' }}
              </div>
              <div class="account-other-info" [class.red]="item.expired">
                <img src="/assets/media/app/img/icons/date.svg" alt="" />
                {{ item.endDay | localDate : accountSetting.dateFormat }}
              </div>
            </div>
          </div>
          <div class="account-operator" *ngIf="!item.isSameUserOrgan">
            <a *ngIf="widgets.includes('account_edit')" (click)="edit(item)">
              <img src="/assets/media/app/img/icons/edit.svg" nz-tooltip nzTooltipTitle="{{ '编辑' | translate }}" />
            </a>
            <a *ngIf="widgets.includes('account_delete')" (click)="delete(item)">
              <img src="/assets/media/app/img/icons/delete.svg" nz-tooltip nzTooltipTitle="{{ '删除' | translate }}" />
            </a>
            <a *ngIf="widgets.includes('account_reset_pwd')" (click)="resetPwd(item)">
              <img
                src="/assets/media/app/img/icons/reset-pwd.svg"
                nz-tooltip
                nzTooltipTitle="{{ '重置密码' | translate }}"
              />
            </a>
          </div>
        </div>
      </ng-template>
    </div>
  </div>
</div>

<app-account-form
  *ngIf="[PageMode.Add, PageMode.Edit].includes(pageMode)"
  [organTree]="organTree"
  [id]="userId"
  (back)="onBack()"
></app-account-form>
