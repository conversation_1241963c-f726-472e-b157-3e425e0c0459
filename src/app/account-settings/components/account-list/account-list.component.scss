:host {
  display: block;
  height: 100%;
}

.account {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 0 8px 8px 8px;
  overflow-y: auto;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;

    nz-input-group {
      flex: 1;
      max-width: 285px;
    }
  }

  &-body {
    flex: 1;
    height: 0;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }

  &-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: #fff;

    &:nth-child(2n) {
      background-color: #f4f7fb;
    }

    &:hover {
      background-color: #e2ebf6;
    }
  }
}

.spining {
  margin-top: 50px;
}

.account-info {
  flex: 1;

  .account-name {
    padding: 4px 0 8px;
    line-height: 14px;
    font-weight: 500;
    color: #575e72;
  }
}

.account-other {
  display: flex;
  flex-wrap: wrap;

  &-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 15px 5px 0;

    &.red {
      color: red;
    }

    img {
      margin-right: 5px;
    }
  }
}

.account-operator {
  display: flex;

  a {
    position: relative;

    &::after {
      content: "";
      display: inline-block;
      width: 1px;
      height: 14px;
      margin: 0 10px -3px;
      background-color: #0000001a;
    }

    &:last-child::after {
      width: 0;
      margin: 0;
    }
  }
}

.no-data {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 210px;
  }

  span {
    color: #a6abb6;
  }
}
