<div class="tab-body">
  <form nz-form [formGroup]="form">
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" translate>
        {{ '登录账号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <input nz-input [type]="'text'" [value]="profile.username" disabled="disabled" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="oldPassword">
        {{ '旧密码' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="oldPasswordErrorTpl">
        <nz-input-group [nzSuffix]="suffixTemplate1">
          <input
            nz-input
            [type]="oldPasswordVisible ? 'text' : 'password'"
            placeholder="{{ '请输入旧密码' | translate }}"
            formControlName="oldPassword"
            id="oldPassword"
          />
        </nz-input-group>
        <ng-template #suffixTemplate1>
          <i
            nz-icon
            [nzType]="oldPasswordVisible ? 'eye' : 'eye-invisible'"
            (click)="oldPasswordVisible = !oldPasswordVisible"
          ></i>
        </ng-template>
        <ng-template #oldPasswordErrorTpl let-control>
          <ng-container *ngIf="control?.dirty && control?.errors">
            {{ '旧密码为8～16位数字、字母组成的密码' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="newPassword">
        {{ '新密码' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="newPasswordErrorTpl">
        <nz-input-group [nzSuffix]="suffixTemplate2">
          <input
            [type]="newPasswordVisible ? 'text' : 'password'"
            nz-input
            placeholder="{{ '请设置8-16位数字、字母、符号组成的密码' | translate }}"
            formControlName="newPassword"
            id="newPassword"
          />
        </nz-input-group>
        <ng-template #suffixTemplate2>
          <i
            nz-icon
            [nzType]="newPasswordVisible ? 'eye' : 'eye-invisible'"
            (click)="newPasswordVisible = !newPasswordVisible"
          ></i>
        </ng-template>
        <ng-template #newPasswordErrorTpl let-control>
          <ng-container *ngIf="control?.dirty && control?.errors">
            {{ '请设置密码为8～16位数字、字母组成的密码，且不能与旧密码相同' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="confirmPassword">
        {{ '新密码确认' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="confirmPasswordErrorTpl">
        <nz-input-group [nzSuffix]="suffixTemplate3">
          <input
            [type]="confirmPasswordVisible ? 'text' : 'password'"
            nz-input
            placeholder="{{ '请重新输入新密码' | translate }}"
            formControlName="confirmPassword"
            id="confirmPassword"
          />
        </nz-input-group>
        <ng-template #suffixTemplate3>
          <i
            nz-icon
            [nzType]="confirmPasswordVisible ? 'eye' : 'eye-invisible'"
            (click)="confirmPasswordVisible = !confirmPasswordVisible"
          ></i>
        </ng-template>
        <ng-template #confirmPasswordErrorTpl let-control>
          <ng-container *ngIf="control?.dirty && control?.errors">
            {{ '请设置密码为8～16位数字、字母和符号组成的密码，两次输入需要一致' | translate }}
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>

<div class="tab-footer">
  <button type="button" nz-button (click)="reset()">
    {{ '取消' | translate }}
  </button>
  <button type="submit" nz-button nzType="primary" (click)="submit()">
    {{ '确定' | translate }}
  </button>
</div>
