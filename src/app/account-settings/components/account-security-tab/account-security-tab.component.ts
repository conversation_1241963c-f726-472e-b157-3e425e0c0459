import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';

import { timer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { ProfileService, Profile } from '@core/profile/profile.service';
import { AuthenticationService } from '@core/authentication/authentication.service';
import { AuthenticationOAuth2Service } from '@core/authentication/authentication-oauth2.service';
import { environment } from '@env/environment';
import { TranslateService } from '@ngx-translate/core';

declare const $: any;

@Component({
  selector: 'app-account-security-tab',
  templateUrl: './account-security-tab.component.html',
  styleUrls: ['./account-security-tab.component.scss']
})
export class AccountSecurityTabComponent implements OnInit {
  log: Logger;
  form: FormGroup;
  changePassForm: FormGroup;
  isLoading = false;
  profile: Profile = { displayName: '', username: '' };
  oldPasswordVisible = false;
  newPasswordVisible = false;
  confirmPasswordVisible = false;
  oldEqNew: boolean = false;
  new: boolean = false;
  password?: string;
  flag: boolean = false;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private profileService: ProfileService,
    public activeModal: BsModalRef,
    private authenticationService: AuthenticationService,
    private authenticationOAuth2Service: AuthenticationOAuth2Service,
    private translate: TranslateService
  ) {
    this.translate.get('修改密码').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
    this.buildForm();
  }

  ngOnInit() {
    this.getProfile();
  }

  reset() {
    this.form.reset({});
  }

  submit() {
    this.flag = true;
    const params = this.form.value;

    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (params.confirmPassword === params.oldPassword) {
      this.new = true;
      return;
    }
    if (params.confirmPassword !== params.newPassword) {
      this.oldEqNew = true;
      return;
    }
    if (this.form.invalid) {
      return;
    }
    this.profileService
      .changePassword(params.oldPassword, params.newPassword, params.confirmPassword)
      .pipe(
        finalize(() => {
          this.form.markAsPristine();
        })
      )
      .subscribe(
        () => {
          this.translate.get('密码修改成功,请重新登录！').subscribe((res: string) => {
            this.log.success(res);
          });

          timer(1500).subscribe(() => {
            this.activeModal.hide();
            this.logout();
          });
        },
        (error) => {
          this.translate.get('密码修改失败').subscribe((res: string) => {
            this.log.error(res, error.error.errorMessage);
          });
        }
      );
  }

  // 重置密码成功后,注销
  logout() {
    if (this.authenticationService.isUsing()) {
      this.authenticationService
        .logout()
        .pipe(
          finalize(() => {
            this.checkoutMenuActive().then(() => {
              localStorage.clear();
              sessionStorage.clear();
              this.router.navigate(['/login']);
            });
          })
        )
        .subscribe();
    }
    if (this.authenticationOAuth2Service.isUsing()) {
      if (environment.authentication.useServiceV1) {
        this.authenticationService.logout();
      }

      this.authenticationOAuth2Service.signout().then(() => {
        this.router.navigate(['/']);
      });
    }
  }

  checkoutMenuActive() {
    return new Promise<void>((resolve) => {
      const menuOpen = $('.m-brand__toggler--active');
      sessionStorage.setItem('logout', 'logout');
      sessionStorage.removeItem('currentRouting');
      if (menuOpen.length > 0) {
        $('#m_aside_left_minimize_toggle').trigger('click');
      }
      resolve();
    });
  }

  validateCurrentAndNewPasswordEqual = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { required: true };
    } else if (this.form.value.newPassword === this.form.value.oldPassword) {
      return { confirm: true, error: true };
    }
    return {};
  };

  validateEqual = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { required: true };
    } else if (this.form.value.newPassword !== this.form.value.confirmPassword) {
      return { confirm: true, error: true };
    }
    return {};
  };

  buildForm() {
    this.form = this.formBuilder.group({
      oldPassword: [null, [Validators.required, Validators.minLength(8), Validators.maxLength(16)]],
      newPassword: [
        null,
        [
          Validators.required,
          Validators.minLength(8),
          Validators.maxLength(16),
          this.validateCurrentAndNewPasswordEqual
        ]
      ],
      confirmPassword: [
        null,
        [Validators.required, Validators.minLength(8), Validators.maxLength(16), this.validateEqual]
      ]
    });
  }

  private getProfile(): void {
    this.profileService.getProfile().subscribe(
      (profile) => {
        this.profile = profile;
      },
      (error) => this.log.error(error)
    );
  }
}
