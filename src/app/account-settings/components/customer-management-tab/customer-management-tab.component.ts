import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  TemplateRef,
  ViewChild,
  Input
} from '@angular/core';
import { Observable, timer } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { LoggerFactory, Logger, Dialogs } from '@app/core';
import { NzConfig, NZ_CONFIG } from 'ng-zorro-antd/core/config';

import { PageMode } from '@app/shared/models/page-mode';
import { TreeService } from '@app/shared/services/tree.service';
import { Organization } from '@app/account-settings/models/organization';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { OrganizationService } from '@app/account-settings/services/organization.service';
import { NzFormatEmitEvent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzTreeSelectComponent } from 'ng-zorro-antd/tree-select';
import { NzContextMenuService, NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-customer-management-tab',
  templateUrl: './customer-management-tab.component.html',
  styleUrls: ['./customer-management-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [{ provide: NZ_CONFIG, useValue: { message: { nzTop: 90 } } }]
})
export class CustomerManagementTabComponent implements OnInit {
  @Input() widgets: string[] = [];

  log: Logger;
  translates: any = {
    请先修改之前新增机构: '',
    '真的要删除吗？': '',
    '该机构下存在任何子机构或账号信息，则不可以删除': '',
    组织机构层级最多x级: '',
    删除失败: '',
    删除成功: '',
    新增失败: '',
    新增成功: '',
    删除的组织机构下存在绑定的用户: ''
  };

  organPageMode = PageMode.Detail;
  organPageMode2 = PageMode.Edit; // 取消选中为 PageMode.Detail 状态

  searchOrganName = '';
  organTree: NzTreeNodeOptions[] = [];
  currOrganTree: NzTreeNodeOptions[] = [];
  organExpandedKeys: string[] = [];
  organSelectedKeys: string[] = [];
  contextOrgan: NzFormatEmitEvent;

  nzSelectedIndex = 0;
  @ViewChild('organTreeComponent') organTreeComponent!: NzTreeSelectComponent;
  @ViewChild('organContextTpl') organContextTpl: NzDropdownMenuComponent;

  constructor(
    private cd: ChangeDetectorRef,
    private dialogs: Dialogs,
    private loggerFactory: LoggerFactory,
    private nzContextMenuService: NzContextMenuService,
    private organizationService: OrganizationService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private treeService: TreeService,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates), { organLevel: 5 }).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {
    // this.getOrganTree().subscribe();
    this.getUserInfo();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，获取所有树结构
        this.getAllUserOrganizationsTree().subscribe();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getOrganTree().subscribe();
      }
    });
  }

  getAllUserOrganizationsTree() {
    //  return this.referenceDataService.getAllUserOrganizationsTree().subscribe((res: any) => {
    //     this.organTree = res;
    //   });
    this.organTree = [];
    this.currOrganTree = [];
    return this.referenceDataService.getAllUserOrganizationsTree().pipe(
      map((res) => {
        this.organTree = res;
        if (this.organTree.length === 0) {
          return;
        }
        if (this.organSelectedKeys.length === 0) {
          this.organPageMode = PageMode.Detail;
          this.organSelectedKeys = this.organExpandedKeys = [this.organTree[0].key];
        }
        this.currOrganTree = [this.treeService.getNodeByKey(this.organSelectedKeys[0], this.organTree)];
        return;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  changeTabs(e: any) {
    const { index } = e;
    if (index === this.nzSelectedIndex) {
      return;
    }
    this.nzSelectedIndex = index;
  }

  changeOrgan(event: NzFormatEmitEvent) {
    // fix: 修复点击nzTree不关闭context Menu问题
    this.nzContextMenuService.close();
    if (event.selectedKeys.length === 0) {
      timer(0)
        .pipe(finalize(() => this.cd.markForCheck()))
        .subscribe(() => {
          this.organPageMode = PageMode.Detail;
          this.organPageMode2 = PageMode.Detail;
          // this.organSelectedKeys = [...this.organSelectedKeys];
          this.organTree = [...this.organTree];
        });
      return;
    }
    this.organPageMode = event.node.origin.key === this.organTree[0].key ? PageMode.Detail : PageMode.Edit;
    this.organPageMode2 = PageMode.Edit;
    this.currOrganTree = [event.node.origin];
    this.organSelectedKeys = [event.keys[0]];
  }

  openOrganContext(event: NzFormatEmitEvent, tpl: NzDropdownMenuComponent): void {
    this.contextOrgan = event;
    const mouseEevnt = event.event as MouseEvent;
    this.nzContextMenuService.create(mouseEevnt, tpl || this.organContextTpl);
  }

  editOrgan() {
    this.organPageMode = PageMode.Edit;
    this.getOrganTree()
      .pipe(
        finalize(() => {
          this.organSelectedKeys = [...this.organSelectedKeys];
          this.organExpandedKeys = [...this.organExpandedKeys];
          this.organTree = [...this.organTree];
        })
      )
      .subscribe();
  }

  addOrgan() {
    this.createOrgan(this.contextOrgan.node.parentNode.key);
  }

  addSubOrgan() {
    if (this.contextOrgan.node.origin.level >= 5) {
      this.log.error(this.translates['组织机构层级最多x级']);
      this.nzContextMenuService.close(true);
      return;
    }
    this.createOrgan(this.contextOrgan.node.key, true);
  }

  createOrgan(parentId: string, isSub = false) {
    const organization: Organization = {
      parentId,
      name: 'New Group',
      email: '',
      enabled: 1
    };
    this.organizationService
      .create(organization)
      .pipe(finalize(() => this.nzContextMenuService.close(true)))
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(this.translates[res.message] || this.translates['新增失败']);
            return;
          }
          this.log.success(this.translates['新增成功']);
          this.organPageMode = PageMode.Edit;
          this.organSelectedKeys = [res.data.id];
          this.organExpandedKeys = isSub ? [...this.organExpandedKeys, parentId] : [...this.organExpandedKeys];
          // this.getOrganTree().subscribe();
          this.getUserInfo();
        },
        (err) => this.log.error(this.translates['新增失败'])
      );
  }

  deleteOrgan() {
    this.nzContextMenuService.close(true);
    if (this.contextOrgan.node.children.length > 0) {
      this.log.error(this.translates['该机构下存在任何子机构或账号信息，则不可以删除']);
      return;
    }
    this.dialogs.confirm(this.translates['真的要删除吗？']).subscribe(() => {
      this.organizationService
        .delete(this.contextOrgan.node.key)
        .pipe(finalize(() => this.nzContextMenuService.close(true)))
        .subscribe(
          (res) => {
            if (!res.success) {
              this.log.error(this.translates[res.message] || this.translates['删除失败']);
              return;
            }
            this.log.success(this.translates['删除成功']);
            this.organSelectedKeys = this.organExpandedKeys = [];
            // this.getOrganTree().subscribe();
            this.getUserInfo();
          },
          (err) => this.log.error(this.translates['删除失败'])
        );
    });
  }

  getOrganTree(): Observable<void> {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.organTree = [];
    this.currOrganTree = [];
    return this.referenceDataService.getUserChildrenOrganizations(id).pipe(
      map((res) => {
        this.organTree = res;
        if (this.organTree.length === 0) {
          return;
        }
        if (this.organSelectedKeys.length === 0) {
          this.organPageMode = PageMode.Detail;
          this.organSelectedKeys = this.organExpandedKeys = [this.organTree[0].key];
        }
        this.currOrganTree = [this.treeService.getNodeByKey(this.organSelectedKeys[0], this.organTree)];
        return;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }
}
