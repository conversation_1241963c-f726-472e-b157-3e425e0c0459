<div class="customer">
  <div class="customer-left">
    <div class="organ-tree">
      <nz-input-group>
        <input
          type="text"
          nz-input
          placeholder="{{ '请输入机构关键词' | translate }}"
          [(ngModel)]="searchOrganName"
          [ngModelOptions]="{ standalone: true }"
        />
      </nz-input-group>
      <div class="organ-tree-list">
        <nz-tree
          #organTreeComponent
          [nzData]="organTree"
          nzShowLine
          [nzExpandedKeys]="organExpandedKeys"
          [nzSelectedKeys]="organSelectedKeys"
          [nzSearchValue]="searchOrganName"
          (nzClick)="changeOrgan($event)"
          (nzContextMenu)="openOrganContext($event, contextTemplate)"
        ></nz-tree>
        <nz-dropdown-menu #contextTemplate="nzDropdownMenu">
          <ul nz-menu class="context-menu">
            <li
              *ngIf="widgets.includes('group_add') && contextOrgan && contextOrgan.node && contextOrgan.node.level > 0"
              nz-menu-item
              (click)="addOrgan()"
            >
              <i nz-icon nzType="plus" nzTheme="outline"></i>
              <a translate>新建</a>
            </li>
            <li *ngIf="widgets.includes('group_add_sub')" nz-menu-item (click)="addSubOrgan()">
              <i nz-icon nzType="apartment" nzTheme="outline"></i>
              <a translate>下级机构</a>
            </li>
            <li *ngIf="widgets.includes('group_delete')" class="delete-svg" nz-menu-item (click)="deleteOrgan()">
              <i nz-icon>
                <svg
                  width="17px"
                  height="18px"
                  viewBox="0 0 17 18"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <title>编组 10</title>
                  <g id="里程2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="用户中心-Customer-management-Account" transform="translate(-1192.000000, -397.000000)">
                      <g id="编组-5" transform="translate(1065.000000, 398.000000)">
                        <g id="编组-10" transform="translate(127.000000, 0.437500)">
                          <rect
                            id="矩形"
                            fill="#000000"
                            fill-opacity="0.65"
                            x="0"
                            y="1.********"
                            width="16.49837"
                            height="1.********"
                            rx="0.*********"
                          ></rect>
                          <path
                            d="M1.********,15.0538824 L1.********,2.******** L1.********,2.******** L2.********,2.******** L2.********,13.7160589 C2.********,14.2683436 3.********,14.7160589 3.********,14.7160589 L12.8227229,14.7160589 C13.3750077,14.7160589 13.8227229,14.2683436 13.8227229,13.7160589 L13.8227229,2.******** L13.8227229,2.******** L15.1605465,2.******** L15.1605465,15.0538824 C15.1605465,15.6061671 14.7128312,16.0538824 14.1605465,16.0538824 L2.********,16.0538824 C1.78408521,16.0538824 1.********,15.6061671 1.********,15.0538824 Z"
                            id="路径"
                            fill="#000000"
                            fill-opacity="0.65"
                          ></path>
                          <path
                            d="M4.90389938,2.89861765 L4.90389938,0.********* C4.90389938,0.299481999 5.20338138,0 5.57281115,0 L11.3700465,0 C11.7394762,0 12.0389582,0.299481999 12.0389582,0.********* L12.0389582,2.89861765"
                            id="路径"
                            stroke="#000000"
                            stroke-opacity="0.65"
                            stroke-width="1.5"
                          ></path>
                          <rect
                            id="矩形"
                            fill="#000000"
                            fill-opacity="0.65"
                            x="5.79578174"
                            y="5.35129413"
                            width="1.********"
                            height="6.24317649"
                            rx="0.*********"
                          ></rect>
                          <rect
                            id="矩形备份-3"
                            fill="#000000"
                            x="9.36331116"
                            y="5.35129413"
                            width="1.********"
                            height="6.24317649"
                            rx="0.*********"
                          ></rect>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </i>
              <a translate>删除</a>
            </li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </div>
  </div>
  <div class="customer-right">
    <nz-tabset nzType="card" (nzSelectChange)="changeTabs($event)" [nzSelectedIndex]="nzSelectedIndex">
      <nz-tab [nzTitle]="'机构' | translate" *ngIf="widgets.includes('group')">
        <app-organization-form
          [widgets]="widgets"
          [pageMode]="organPageMode"
          [organId]="organSelectedKeys[0]"
          (save)="editOrgan()"
          [organInfo]="organTree[0]"
        ></app-organization-form>
      </nz-tab>
      <nz-tab [nzTitle]="'账号' | translate" *ngIf="widgets.includes('account')">
        <app-account-list [widgets]="widgets" [organTree]="currOrganTree"></app-account-list>
      </nz-tab>
      <nz-tab [nzTitle]="'表现' | translate" *ngIf="widgets.includes('preference')">
        <app-performance-form [organTree]="currOrganTree" [pageMode]="organPageMode2"></app-performance-form>
      </nz-tab>
    </nz-tabset>
  </div>
</div>
