:host {
  display: block;
  height: calc(100vh - 215px);
}

.customer {
  display: flex;
  height: 100%;

  &-left {
    width: 348px;
    height: calc(100% - 50px);
    margin-right: 18px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
  }

  &-right {
    flex: 1;
    width: 0;
    height: 100%;
  }
}

.organ-tree {
  height: 100%;
  display: flex;
  flex-direction: column;

  nz-input-group {
    padding: 16px;
  }

  .organ-tree-list {
    flex: 1;
    height: 0;
    padding: 0 16px 16px;
    overflow: auto;
  }
}

.context-menu {
  li {
    display: flex;
    align-items: center;
  }
}

.delete-svg svg {
  transform: scale(0.83);
}

:host ::ng-deep {
  .ant-tabs-bar {
    margin: 0;
    border: 0;
    z-index: 2;
  }

  .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab {
    border: 0;
    background: none;
    border-radius: 8px 8px 0 0;
    font-weight: 500;

    &.ant-tabs-tab-active {
      border: 1px solid #e8e8e8;
      border-bottom: 1px solid #fff;
    }
  }

  .ant-tabs-content {
    display: flex;
    z-index: 1;
  }

  .ant-tabs-no-animation > .ant-tabs-content > .ant-tabs-tabpane-inactive {
    width: 0;
  }
}
