:host {
  display: block;
  height: calc(100vh - 215px);
  font-family: PingFangSC, PingFang SC;
}

.customer {
  display: flex;
  height: 100%;

  &-left {
    width: 348px;
    height: calc(100% - 50px);
    margin-right: 18px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
  }

  &-right {
    flex: 1;
    width: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;

    .ant-form {
      height: calc(100% - 50px);
      display: flex;
      flex-direction: column;
    }

    .btns {
      display: flex;
      justify-content: flex-end;
      position: absolute;
      gap: 8px;
      bottom: 0;
      right: 0;
    }
  }
}

.transfer {
  flex: 1;
  height: 0;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  position: relative;

  &-left,
  &-right {
    width: 285px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .menu-tree-container {
      flex: 1;
      height: 0;
      border-radius: 4px;
      border: 1px solid #d3d6de;
      overflow-y: auto;
    }
  }

  &-center {
    width: 90px;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    button {
      display: flex;
      justify-content: center;
      align-items: center;

      &:first-child {
        margin-bottom: 14px;
      }
    }
  }

  .title {
    position: relative;
    padding: 10px 0;
    font-weight: 400;
    color: #575e72;
  }

  .spining {
    margin-top: 50px;
  }

  .ant-form-explain {
    bottom: 0;
    color: #f5222d;
  }
}

.role-tree-contianer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .role-tree-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    input[type="text"] {
      flex: 1;
    }

    .ant-btn {
      display: flex;
      align-items: center;
      margin-left: 16px;
    }
  }

  .role-tree-list {
    flex: 1;
    height: 0;
    padding: 0 16px 16px;
    overflow: auto;

    .ant-btn {
      display: flex;
      align-items: center;
    }
  }
}

.role-node {
  width: 100%;
  display: inline-flex;
  position: relative;
  margin-bottom: 5px;
  padding: 6px 70px 6px 10px;
  border-radius: 3px;
  cursor: pointer;

  &:hover {
    background-color: #c4e8fc;
  }

  &.active {
    background-color: #bae7ff;
  }

  .name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .role-btns {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;

    i {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #034a8d;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.spining {
  margin-top: 50px;
}

.ant-form-inline .ant-form-item {
  margin-bottom: 24px !important;
}

.ant-form-explain {
  position: absolute;
}

.w-350 {
  width: 350px;
}

.no-data {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 210px;
  }

  span {
    color: #a6abb6;
  }
}

.delete-svg svg {
  transform: scale(0.83);
}

:host ::ng-deep {
  .ant-form-item-label > label {
    font-weight: 400;
    color: #575e72;
  }

  .ant-form-explain,
  .ant-form-extra {
    min-height: auto;
    font-size: 12px;
    line-height: 22px;
  }

  .has-error {
    position: absolute;
    bottom: -21px;

    .ant-form-item-explain {
      color: #ff4d4f;
    }
  }
}
