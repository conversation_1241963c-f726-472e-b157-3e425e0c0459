import { Component, OnInit, Input, ChangeDetectionStrategy, ChangeDetectorRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { NzFormatEmitEvent, NzTreeNodeOptions, NzTreeComponent } from 'ng-zorro-antd/tree';

import { environment } from '@env/environment';
import { LoggerFactory, Logger, Dialogs } from '@app/core';

import { PageMode } from '@app/shared/models/page-mode';
import { Menu } from '@app/shared/models/type';
import { Role } from '@app/account-settings/models/role';
import { TreeService } from '@app/shared/services/tree.service';
import { RoleService } from '@app/account-settings/services/role.service';

@Component({
  selector: 'app-role-tab',
  templateUrl: './role-tab.component.html',
  styleUrls: ['./role-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RoleTabComponent implements OnInit {
  @Input() widgets: string[] = [];
  @Input() tabIndex = 0;

  log: Logger;
  translates = {
    '真的要删除吗？': '',
    新增成功: '',
    新增失败: '',
    更新成功: '',
    更新失败: '',
    删除成功: '',
    删除失败: '',
    角色名称重复: ''
  };

  PageMode = PageMode;
  pageMode: PageMode = PageMode.Detail;

  form: FormGroup;
  saving = false;

  searchRoleName = '';
  roleList: Role[] = [];
  currRole: Role;
  roleListLoading = false;

  @ViewChild('leftTreeComponent') leftTreeComponent!: NzTreeComponent;
  leftTreeNodes: NzTreeNodeOptions[] = [];
  leftTreeSelectedKeys: string[] = [];

  @ViewChild('rightTreeComponent') rightTreeComponent!: NzTreeComponent;
  rightTreeNodes: NzTreeNodeOptions[] = [];
  rightTreeLoading = false;

  get disabledToRight(): boolean {
    return !this.leftTreeComponent || this.leftTreeComponent.getCheckedNodeList().length === 0;
  }

  get disabledToLeft(): boolean {
    return !this.rightTreeComponent || this.rightTreeComponent.getCheckedNodeList().length === 0;
  }

  constructor(
    private cd: ChangeDetectorRef,
    private dialogs: Dialogs,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private roleService: RoleService,
    private translate: TranslateService,
    private treeService: TreeService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
    this.init();
  }

  init(id?: string) {
    this.getRoleList().subscribe(() => {
      this.currRole = id ? this.roleList.find((item) => item.id === id) : this.roleList[0];
      this.detail(this.currRole);
    });
  }

  search() {
    this.getRoleList().subscribe();
  }

  detail(role: Role) {
    if (!role) {
      return;
    }
    this.pageMode = PageMode.Detail;
    this.currRole = role;
    this.form.reset({
      id: role.id,
      name: { value: role.name, disabled: true },
      applicationId: environment.api.userCenterV2.applicationId
    });
    this.getLeftTreeNodes();
    this.getRightTreeNodes(role.id);
  }

  add() {
    this.pageMode = PageMode.Add;
    this.currRole = undefined;
    this.form.reset({
      name: { value: null, disabled: false },
      applicationId: environment.api.userCenterV2.applicationId
    });
    this.getLeftTreeNodes();
    this.getRightTreeNodes();
  }

  edit(role: Role, e?: Event) {
    if (e) {
      e.stopPropagation();
    }
    this.currRole = role;
    this.pageMode = PageMode.Edit;
    this.form.reset({
      id: role.id,
      name: { value: role.name, disabled: false },
      applicationId: environment.api.userCenterV2.applicationId
    });
    this.getLeftTreeNodes();
    this.getRightTreeNodes(role.id);
  }

  delete(role: Role, e?: Event) {
    if (e) {
      e.stopPropagation();
    }
    this.dialogs.confirm(this.translates['真的要删除吗？']).subscribe(() => {
      this.roleService.delete(role.id).subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(this.translates['删除失败']);
            return;
          }
          this.log.success(this.translates['删除成功']);
          this.init();
        },
        (err) => this.log.error(this.translates['删除失败'])
      );
    });
  }

  /**
   * 添加权限,移到右侧(从所有节点移除未选中的节点)
   */
  addAuth() {
    // 获取所有选中的节点
    let allCheckKeys: string[] = [];
    // 左侧选中的节点
    const leftCheckNodes = this.leftTreeComponent.getCheckedNodeList().map((treeNode: any) => treeNode.key);
    this.leftTreeSelectedKeys = [];
    // 右侧所有节点
    const rightCheckNodes = this.rightTreeComponent.getTreeNodes().map((treeNode: any) => treeNode.origin);
    rightCheckNodes.forEach((node: any) => {
      this.treeService.getChildrenIds(node, allCheckKeys);
    });
    allCheckKeys = [...allCheckKeys, ...leftCheckNodes];
    const checkKeys = new Set<string>(allCheckKeys); // 去重
    allCheckKeys = Array.from(checkKeys);
    // 移除未选中的节点
    let allNodes = this.leftTreeComponent.getTreeNodes().map((treeNode: any) => treeNode.origin);
    allNodes = JSON.parse(JSON.stringify(allNodes));
    const rightTreeNodes = this.filterTreeNode(allNodes, allCheckKeys);
    this.rightTreeNodes = rightTreeNodes;
    this.setModuleAndOperateIds();
  }

  /**
   * 删除权限,移到左侧
   */
  removeAuth() {
    const checkNodes = this.rightTreeComponent.getCheckedNodeList();
    // 根节点调用remove()方法无效,因getParentNode()返回null
    let rootRemoveNodes: string[] = [];
    checkNodes.forEach((node: any) => {
      const parentNode = node.getParentNode();
      if (parentNode) {
        node.remove();
        parentNode.setChecked();
      } else {
        rootRemoveNodes.push(node.key);
      }
    });
    let allNodes = this.rightTreeComponent.getTreeNodes();
    if (rootRemoveNodes.length > 0) {
      allNodes = allNodes.filter((treeNode: any) => !rootRemoveNodes.includes(treeNode.key));
    }
    this.rightTreeNodes = allNodes.map((treeNode: any) => treeNode.origin);
    this.setModuleAndOperateIds();
  }

  changeLeftCheck(event: NzFormatEmitEvent) {
    const node = event.node;
    if (!node) {
      return;
    }
    const isChecked = node.isChecked;
    const checkedKes = event.keys ? event.keys : [];
    this.leftTreeSelectedKeys = [...checkedKes];
    // 更新子节点
    if (Array.isArray(node.children)) {
      this.updateLeftChildrenChecked(node.children, isChecked);
    }
    // 选中，父节点也选中
    if (node.parentNode && isChecked) {
      this.updateLeftParentChecked(node.parentNode, isChecked);
    }
    this.leftTreeSelectedKeys = [...this.leftTreeSelectedKeys];
  }

  submit() {
    if (this.saving) {
      return;
    }
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const params: Role = this.form.getRawValue();
    let save$ = this.roleService.create(params);
    let successMsg = this.translates['新增成功'];
    let errorMsg = this.translates['新增失败'];
    if (params.id) {
      save$ = this.roleService.edit(params);
      successMsg = this.translates['更新成功'];
      errorMsg = this.translates['更新失败'];
    }
    save$
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            errorMsg = res.message.includes('名称重复') ? this.translates['角色名称重复'] : errorMsg;
            this.log.error(errorMsg);
            return;
          }
          this.currRole = undefined;
          this.init(params.id);
          this.log.success(successMsg);
        },
        (err) => this.log.error(errorMsg)
      );
  }

  reset() {
    this.form.value.id ? this.edit(this.currRole) : this.add();
  }

  updateLeftChildrenChecked(nodes: NzTreeNodeOptions[], checked: boolean) {
    nodes.forEach((item) => {
      if (checked) {
        // 不存在添加
        if (!this.leftTreeSelectedKeys.includes(item.key)) {
          this.leftTreeSelectedKeys.push(item.key);
        }
      } else {
        // 存在删除
        const delIndex = this.leftTreeSelectedKeys.indexOf(item.key);
        if (delIndex > -1) {
          this.leftTreeSelectedKeys.splice(delIndex, 1);
        }
      }
      if (Array.isArray(item.children)) {
        this.updateLeftChildrenChecked(item.children, checked);
      }
    });
  }

  updateLeftParentChecked(item: NzTreeNodeOptions, checked: boolean) {
    // 不存在添加
    if (!this.leftTreeSelectedKeys.includes(item.key)) {
      this.leftTreeSelectedKeys.push(item.key);
    }
    if (item.parentNode) {
      this.updateLeftParentChecked(item.parentNode, checked);
    }
  }

  filterTreeNode(nodes: NzTreeNodeOptions[], ids: string[] = []) {
    nodes = nodes.filter((node) => {
      node.checked = false;
      let isExits = ids.includes(node.key);
      if (isExits && Array.isArray(node.children)) {
        node.children = this.filterTreeNode(node.children, ids);
      }
      return isExits;
    });
    return nodes;
  }

  getRoleList(): Observable<void> {
    if (this.roleListLoading) {
      return;
    }
    this.roleListLoading = true;
    return this.roleService.getPageList(this.searchRoleName.trim()).pipe(
      map((res) => {
        if (!Array.isArray(res)) {
          return;
        }
        this.roleList = Array.isArray(res) ? res : [];
        return;
      }),
      finalize(() => {
        this.roleListLoading = false;
        this.cd.markForCheck();
      })
    );
  }

  getAllAuthTreeNodes(): Menu[] {
    const moduleTree: string = localStorage.getItem(`moduleTree`);
    let menuItems: Array<any> = [];
    if (!moduleTree) {
      return menuItems;
    }
    menuItems = JSON.parse(moduleTree) || menuItems;
    return menuItems;
  }

  /**
   * 获取左侧树
   */
  getLeftTreeNodes() {
    this.leftTreeNodes = this.treeService.formatMenuNodes(
      this.getAllAuthTreeNodes(),
      this.pageMode === PageMode.Detail
    );
  }

  /**
   * 获取右侧树
   */
  getRightTreeNodes(roleId?: string) {
    let rightTreeNodes: NzTreeNodeOptions[] = [];
    if (!roleId) {
      this.rightTreeNodes = rightTreeNodes;
      return;
    }
    this.rightTreeLoading = true;
    this.roleService
      .detail(roleId)
      .pipe(
        map((res) => {
          if (!Array.isArray(res)) {
            return;
          }
          rightTreeNodes = this.treeService.formatRoleMenuNodes(res, this.pageMode === PageMode.Detail);
          return;
        }),
        finalize(() => {
          this.rightTreeNodes = rightTreeNodes;
          this.rightTreeLoading = false;
          this.setModuleAndOperateIds();
          this.cd.markForCheck();
        })
      )
      .subscribe();
  }

  getModuleAndOperateIds(nodes: NzTreeNodeOptions[], moduleIds: string[], operateIds: string[]) {
    nodes.forEach((item) => {
      item.type === 'widget' ? operateIds.push(item.key) : moduleIds.push(item.key);
      if (Array.isArray(item.children)) {
        this.getModuleAndOperateIds(item.children, moduleIds, operateIds);
      }
    });
  }

  setModuleAndOperateIds() {
    let moduleIds: string[] = [];
    let operateIds: string[] = [];
    this.getModuleAndOperateIds(this.rightTreeNodes, moduleIds, operateIds);
    this.form.get('moduleIds').setValue(moduleIds);
    this.form.get('operateIds').setValue(operateIds);
  }

  buildForm() {
    this.form = this.formBuilder.group({
      id: [null],
      applicationId: [environment.api.userCenterV2.applicationId],
      name: [{ value: null, disabled: true }, [Validators.required]],
      moduleIds: [null, [Validators.required]],
      operateIds: [null]
    });
  }
}
