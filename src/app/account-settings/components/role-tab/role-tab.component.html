<div class="customer">
  <div class="customer-left">
    <div class="role-tree-contianer">
      <div class="role-tree-header">
        <input
          type="text"
          nz-input
          placeholder="{{ '请输入角色名称' | translate }}"
          [(ngModel)]="searchRoleName"
          (ngModelChange)="search()"
        />
        <button nz-button nzType="primary" *ngIf="widgets.includes('role_add')" (click)="add()">
          <i nz-icon nzType="plus" nzTheme="outline"></i>
          {{ '新建' | translate }}
        </button>
      </div>
      <div class="role-tree-list">
        <nz-spin class="spining" *ngIf="roleListLoading; else listTpl"></nz-spin>

        <ng-template #listTpl>
          <div class="no-data" *ngIf="roleList.length == 0">
            <img src="/assets/font/noDate.png" />
            <span class="no-data-title">{{ '暂无数据' | translate }}</span>
          </div>

          <div
            class="role-node"
            [class.active]="currRole && currRole.id === item.id"
            *ngFor="let item of roleList"
            (click)="detail(item)"
          >
            <div class="name" [title]="item.name">{{ item.name }}</div>
            <div class="role-btns">
              <i
                *ngIf="widgets.includes('role_edit')"
                nz-icon
                nzType="edit"
                nz-tooltip
                nzTooltipTitle="{{ '编辑' | translate }}"
                (click)="edit(item, $event)"
              ></i>
              <i
                *ngIf="widgets.includes('role_delete')"
                class="delete-svg"
                nz-icon
                nz-tooltip
                nzTooltipTitle="{{ '删除' | translate }}"
                (click)="delete(item, $event)"
              >
                <svg
                  width="17px"
                  height="18px"
                  viewBox="0 0 17 18"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <title>编组 10</title>
                  <g id="里程2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="用户中心-Customer-management-Account" transform="translate(-1192.1890 -397.1890)">
                      <g id="编组-5" transform="translate(1065.1890 398.1890)">
                        <g id="编组-10" transform="translate(127.1890 0.437500)">
                          <rect
                            id="矩形"
                            fill="#1890ff"
                            x="0"
                            y="1.********"
                            width="16.49837"
                            height="1.********"
                            rx="0.*********"
                          ></rect>
                          <path
                            d="M1.********,15.0538824 L1.********,2.******** L1.********,2.******** L2.********,2.******** L2.********,13.7160589 C2.********,14.2683436 3.********,14.7160589 3.********,14.7160589 L12.8227229,14.7160589 C13.3750077,14.7160589 13.8227229,14.2683436 13.8227229,13.7160589 L13.8227229,2.******** L13.8227229,2.******** L15.1605465,2.******** L15.1605465,15.0538824 C15.1605465,15.6061671 14.7128312,16.0538824 14.1605465,16.0538824 L2.********,16.0538824 C1.78408521,16.0538824 1.********,15.6061671 1.********,15.0538824 Z"
                            id="路径"
                            fill="#1890ff"
                          ></path>
                          <path
                            d="M4.90389938,2.89861765 L4.90389938,0.********* C4.90389938,0.299481999 5.20338138,0 5.57281115,0 L11.3700465,0 C11.7394762,0 12.0389582,0.299481999 12.0389582,0.********* L12.0389582,2.89861765"
                            id="路径"
                            stroke="#1890ff"
                            stroke-width="1.5"
                          ></path>
                          <rect
                            id="矩形"
                            fill="#1890ff"
                            x="5.79578174"
                            y="5.35129413"
                            width="1.********"
                            height="6.24317649"
                            rx="0.*********"
                          ></rect>
                          <rect
                            id="矩形备份-3"
                            fill="#1890ff"
                            x="9.36331116"
                            y="5.35129413"
                            width="1.********"
                            height="6.24317649"
                            rx="0.*********"
                          ></rect>
                        </g>
                      </g>
                    </g>
                  </g>
                </svg>
              </i>
            </div>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
  <div class="customer-right">
    <form nz-form class="role-container" [formGroup]="form">
      <nz-form-item>
        <nz-form-label nzRequired nzFor="name">
          {{ '角色' | translate }}
        </nz-form-label>
        <nz-form-control [nzErrorTip]="nameErrorTpl">
          <input
            nz-input
            id="name"
            class="w-350"
            formControlName="name"
            maxlength="50"
            [placeholder]="'请输入角色名称' | translate"
          />
          <ng-template #nameErrorTpl let-control>
            <ng-container *ngIf="control?.dirty && control?.errors">
              {{ '请输入角色名称' | translate }}
            </ng-container>
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <div class="transfer">
        <div class="transfer-left">
          <div class="title ant-form-item-required">{{ ' ' }}{{ '待选择' | translate }}</div>
          <div class="menu-tree-container">
            <nz-tree
              #leftTreeComponent
              [nzData]="leftTreeNodes"
              nzCheckable
              nzExpandAll
              nzMultiple
              nzCheckStrictly
              [nzCheckedKeys]="leftTreeSelectedKeys"
              (nzCheckBoxChange)="changeLeftCheck($event)"
            ></nz-tree>
          </div>
        </div>
        <div class="transfer-center">
          <button nz-button nzType="primary" [disabled]="disabledToRight" (click)="addAuth()">
            <i nz-icon nzType="right" nzTheme="outline"></i>
          </button>
          <button nz-button nzType="primary" [disabled]="disabledToLeft" (click)="removeAuth()">
            <i nz-icon nzType="left" nzTheme="outline"></i>
          </button>
        </div>
        <div class="transfer-right">
          <div class="title" translate>已选择</div>
          <div class="menu-tree-container">
            <nz-spin class="spining" *ngIf="rightTreeLoading; else rightTreeTel"></nz-spin>
            <ng-template #rightTreeTel>
              <nz-tree #rightTreeComponent [nzData]="rightTreeNodes" nzCheckable nzMultiple nzExpandAll></nz-tree>
            </ng-template>
          </div>
        </div>

        <div class="has-error">
          <div class="ant-form-item-explain" *ngIf="form.get('moduleIds')?.dirty && form.get('moduleIds')?.errors">
            {{ '请选择权限' | translate }}
          </div>
        </div>
      </div>
    </form>

    <div class="btns">
      <button type="button" nz-button (click)="reset()" [disabled]="pageMode === PageMode.Detail">
        {{ '取消' | translate }}
      </button>
      <button type="button" nz-button nzType="primary" (click)="submit()" [disabled]="pageMode === PageMode.Detail">
        {{ '确定' | translate }}
      </button>
    </div>
  </div>
</div>
