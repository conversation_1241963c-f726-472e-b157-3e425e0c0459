<div class="tab-body">
  <form nz-form [formGroup]="form">
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '小时制' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20">
        <nz-radio-group formControlName="hourTime">
          <div nz-row [nzGutter]="[16, 16]">
            <div nz-col [nzSpan]="6">
              <label nz-radio [nzValue]="HourTime.Hour24">24-{{ '小时' | translate }}</label>
            </div>
            <div nz-col [nzSpan]="6">
              <label nz-radio [nzValue]="HourTime.Hour12">12-{{ '小时' | translate }}</label>
            </div>
          </div>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '日期' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20">
        <nz-radio-group formControlName="dateFormat">
          <div nz-row [nzGutter]="[16, 16]">
            <div nz-col [nzSpan]="6" *ngFor="let item of dateFormats">
              <label nz-radio [nzValue]="item">{{ item }}</label>
            </div>
          </div>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4">
        {{ '预览' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20">
        <div>{{ date | localDate : dateTimeFormat | lowercase }}</div>
      </nz-form-control>
    </nz-form-item>

    <div nz-row class="tip">
      <div nz-col [nzSpan]="4"></div>
      <div nz-col [nzSpan]="20" translate>
        时间格式的更改同步应用于APP端.更换时间格式后,APP需要退出重新登录才能生效.
      </div>
    </div>
  </form>
</div>

<div class="tab-footer">
  <button type="button" nz-button (click)="reset()">
    {{ '取消' | translate }}
  </button>
  <button type="submit" nz-button nzType="primary" (click)="submit()">
    {{ '确定' | translate }}
  </button>
</div>
