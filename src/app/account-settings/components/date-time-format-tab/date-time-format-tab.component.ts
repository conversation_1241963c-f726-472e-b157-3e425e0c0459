import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, ApplicationRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { HourTime, dateFormats } from '@app/account-settings/models/account';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { AccountSettingService } from '@app/account-settings/services/account-setting.service';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-date-time-format-tab',
  templateUrl: './date-time-format-tab.component.html',
  styleUrls: ['./date-time-format-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateTimeFormatTabComponent implements OnInit {
  log: Logger;

  HourTime = HourTime;
  dateFormats = dateFormats;

  form: FormGroup;
  // 缓存之前用户信息,用于还原信息
  hourTimeDateFormat: {
    hourTime: HourTime;
    dateFormat: string;
  };

  saving = false;
  date = new Date();

  get hourTime(): AbstractControl {
    return this.form.get('hourTime');
  }

  get dateFormat(): AbstractControl {
    return this.form.get('dateFormat');
  }

  get dateTimeFormat(): string {
    const date = this.dateFormat.value;
    const time = this.hourTime.value === HourTime.Hour12 ? 'h:mm:ss a' : 'HH:mm:ss';
    return `${date} ${time}`;
  }

  constructor(
    public accountService: AccountService,
    public accountSettingService: AccountSettingService,
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    public appRef: ApplicationRef,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {}

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const value = this.form.getRawValue();
    const params = {
      hourTime: value.hourTime,
      dateTimeFormat: this.dateTimeFormat
    };
    this.accountService
      .setAccountSetting(params)
      .pipe(
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(res.message);
            return;
          }
          const dateTimeSpaceIndex = params.dateTimeFormat.indexOf(' ');
          accountSetting.dateFormat = params.dateTimeFormat.substring(0, dateTimeSpaceIndex);
          accountSetting.timeFormat = params.dateTimeFormat.substring(dateTimeSpaceIndex + 1);
          accountSetting.dateTimeFormat = params.dateTimeFormat;
          accountSetting.hourTime = params.hourTime;
          this.accountSettingService.changeDateTime();
          this.log.success(this.translate.instant('更新成功'));
        },
        (err) => this.log.error(err)
      );
  }

  reset() {
    this.form.reset({
      hourTime: this.hourTimeDateFormat.hourTime,
      dateFormat: this.hourTimeDateFormat.dateFormat
    });
  }

  buildForm() {
    this.hourTimeDateFormat = {
      hourTime: accountSetting.hourTime,
      dateFormat: accountSetting.dateFormat
    };
    this.form = this.formBuilder.group({
      hourTime: [this.hourTimeDateFormat.hourTime, [Validators.required]],
      dateFormat: [this.hourTimeDateFormat.dateFormat, [Validators.required]]
    });
  }
}
