import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { of, Observable } from 'rxjs';
import { finalize, switchMap, tap } from 'rxjs/operators';

import { NzUploadFile } from 'ng-zorro-antd/upload';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { Theme } from '@app/shared/models/theme';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { FileTypeService } from '@app/shared/services/file-type.service';
import { OssService } from '@app/shared/services/oss.service';
import { ThemeService } from '@app/shared/services/theme.service';
import { LogoService } from '@app/shared/services/logo.service';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-logo-theme-tab',
  templateUrl: './logo-theme-tab.component.html',
  styleUrls: ['./logo-theme-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LogoThemeTabComponent implements OnInit {
  log: Logger;

  Theme = Theme;
  selectedTheme: number = Theme.Dark; // 默认为黑色主题
  saving = false;

  form: FormGroup;
  // 缓存之前用户信息,用于还原信息
  logoTheme: { logo: string; theme: Theme };

  fileList: NzUploadFile[] = [
    {
      uid: Math.random().toString(36).substring(2),
      name: accountSetting.logo,
      url: accountSetting.logo,
      status: 'done'
    }
  ];
  maxFileSize = 1024 * 1024;

  get logo(): FormControl {
    return this.form.get('logo') as FormControl;
  }

  /**
   * 校验文件；返回值都是false,禁止自动上传
   * @param file
   * @returns boolean 或者 Observerble<boolean>
   */
  beforeUpload = (file: any) => {
    this.logo.setValue(file);
    this.logo.markAsDirty();
    this.logo.updateValueAndValidity();
    if (this.logo.valid) {
      this.fileList = [this.formatFile(file)];
    }
    return false;
  };

  constructor(
    public accountService: AccountService,
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    private domSanitizer: DomSanitizer,
    private fileTypeService: FileTypeService,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private logoService: LogoService,
    private ossService: OssService,
    private themeService: ThemeService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {}

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    let params = this.form.getRawValue();
    const uploadLogo$: Observable<string> =
      typeof params.logo === 'string' ? of(params.logo) : this.ossService.uploadByOss(params.logo, 'logo');

    this.saving = true;
    uploadLogo$
      .pipe(
        tap((url) => (params = { logo: url, theme: params.theme })),
        switchMap(() => this.accountService.setAccountSetting(params)),
        finalize(() => this.cd.markForCheck())
      )
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(res.message);
            return;
          }
          accountSetting.logo = params.logo;
          accountSetting.theme = params.theme;
          this.logoService.changeLogo(params.logo);
          this.themeService.changeTheme(accountSetting.theme);
          this.log.success(this.translate.instant('更新成功'));
        },
        (err) => this.log.error(err)
      );
  }

  resetLogo() {
    this.logo.setValue(accountSetting.defaultLogo, { onlySelf: true });
    this.accountService
      .setAccountSetting({ logo: accountSetting.defaultLogo })
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe(
        (res) => {
          if (!res.success) {
            this.log.error(res.message);
            return;
          }
          accountSetting.logo = accountSetting.defaultLogo;
          this.fileList = [
            {
              uid: Math.random().toString(36).substring(2),
              name: accountSetting.logo,
              url: accountSetting.logo,
              status: 'done'
            }
          ];
          this.logoService.changeLogo(accountSetting.logo);
          this.log.success(this.translate.instant('更新成功'));
        },
        (err) => this.log.error(err)
      );
  }

  reset() {
    this.fileList = [
      {
        uid: Math.random().toString(36).substring(2),
        name: this.logoTheme.logo,
        url: this.logoTheme.logo,
        status: 'done'
      }
    ];
    this.form.reset({
      logo: this.logoTheme.logo,
      theme: this.logoTheme.theme
    });
    this.selectedTheme = this.logoTheme.theme;
  }

  buildForm() {
    this.logoTheme = {
      logo: accountSetting.logo,
      theme: accountSetting.theme
    };
    this.form = this.formBuilder.group({
      logo: [this.logoTheme.logo, [this.validatorFile]],
      theme: [this.logoTheme.theme || Theme.Dark]
    });
    this.selectedTheme = this.logoTheme.theme || Theme.Dark;
  }

  validatorFile(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return { required: true };
    }
    const file = control.value;
    if (!file) {
      return { required: true };
    }

    const fileName = typeof file === 'string' ? file : file.name || '';
    const isJpgOrPngOrJpeg = /\.(jpg|jpeg|png)$/i.test(fileName);
    if (!isJpgOrPngOrJpeg) {
      return { notFileType: true };
    }
    if (typeof file !== 'string') {
      const isLt1M = file?.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        return { overFileSize: true };
      }
    }
    return null;
  }

  /**
   * 将File类型转换为NzUploadFile
   * @param file
   * @returns
   */
  formatFile(file: File): NzUploadFile {
    const fileItem: NzUploadFile = {
      uid: Math.random().toString(36).substring(2),
      name: file.name,
      status: 'success',
      percent: 0,
      size: file.size,
      type: file.type,
      originFileObj: file
    };

    fileItem.previewImgUrl = this.fileTypeService.isImageFileType(file.type)
      ? this.domSanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(file))
      : `/assets/media/default/img/filetype/unknow.svg`;
    return fileItem;
  }
}
