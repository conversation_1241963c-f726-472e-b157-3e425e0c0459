<div class="tab-body">
  <form nz-form [formGroup]="form">
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzFor="logo" nzRequired>
        {{ 'Logo' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20" [nzErrorTip]="pictureUrlErrorTpl">
        <nz-upload
          [(nzFileList)]="fileList"
          nzAccept=".jpg,.jpeg,.png"
          [nzMultiple]="false"
          [nzFileListRender]="fileListTpl"
          [nzBeforeUpload]="beforeUpload"
        >
          <button nz-button nzType="primary">
            <i nz-icon nzType="upload"></i>
            {{ '点击上传' | translate }}
          </button>
        </nz-upload>
        <div class="upload-tip" translate [translateParams]="{ size: '200*50', fileSize: '1M' }">
          建议logo尺寸xx，大小不超过x
        </div>
        <input type="hidden" formControlName="logo" />
        <ng-template #pictureUrlErrorTpl let-control>
          <div translate>上传失败，当前图片不符合规范，请重新上传</div>
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '预览' | translate }}
        <!-- {{selectedTheme}} -->
      </nz-form-label>
      <nz-form-control [nzSpan]="20">
        <div class="logo">
          <img *ngIf="fileList.length > 0" [src]="fileList[0].url || fileList[0].previewImgUrl" alt="" />
          <button type="button" nz-button nzType="primary" (click)="resetLogo()">{{ '重置' | translate }}</button>
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '主题' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20" nzRequired>
        <nz-radio-group formControlName="theme" (ngModelChange)="selectedTheme = $event">
          <label nz-radio [nzValue]="Theme.Dark">{{ '黑色' | translate }}</label>
          <label nz-radio [nzValue]="Theme.Light">{{ '明亮' | translate }}</label>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    <div nz-row class="tip">
      <div nz-col [nzSpan]="4"></div>
      <div nz-col [nzSpan]="20" translate>
        我们支持客户自定义系统的二级域名，并更换符合客户业务风格的登录页面，如有需要，请联系您的销售人员。
      </div>
    </div>
  </form>
</div>

<div class="tab-footer">
  <button type="button" nz-button (click)="reset()">
    {{ '取消' | translate }}
  </button>
  <button type="submit" nz-button nzType="primary" (click)="submit()">
    {{ '确定' | translate }}
  </button>
</div>

<ng-template #fileListTpl let-fileList></ng-template>
