<div class="modal-header">
  <h5 class="modal-title" translate>选择平台运营人员</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div class="account-header">
    <nz-input-group [nzSuffix]="suffixIconSearch">
      <input
        type="text"
        nz-input
        placeholder="{{ '姓名' | translate }}/{{ '邮件' | translate }}"
        (keyup.enter)="getList()"
        [(ngModel)]="name"
      />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <i nz-icon nzType="search" class="pointer" (click)="getList()"></i>
    </ng-template>
  </div>
  <nz-table #headerTable [nzData]="list" [nzPageSize]="9999999" [nzShowPagination]="false" [nzScroll]="{ y: '480px' }">
    <thead>
      <tr>
        <th [(nzChecked)]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)"></th>
        <th>{{ '姓名' | translate }}</th>
        <th>{{ '所属机构' | translate }}</th>
        <th>{{ '邮件' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of headerTable.data">
        <td [nzChecked]="setOfCheckedId.has(data.id)" (nzCheckedChange)="onItemChecked(data.id, $event)"></td>
        <td>{{ data.name }}</td>
        <td>{{ data.departmentName }}</td>
        <td>{{ data.email }}</td>
      </tr>
    </tbody>
  </nz-table>
</div>
<div class="modal-footer">
  <button type="button" nzType="default" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
