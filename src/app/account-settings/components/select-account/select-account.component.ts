import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Account } from '@app/account-settings/models/account';
import { AccountService } from '@app/account-settings/services/account.service';
import { Logger, LoggerFactory } from '@app/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-select-account',
  templateUrl: './select-account.component.html',
  styleUrls: ['./select-account.component.scss']
})
export class SelectAccountComponent implements OnInit {
  @Input() organId: string;
  @Output() action = new EventEmitter();
  log: Logger;
  list: Account[] = [];
  name: string = '';

  checked = false;
  indeterminate = false;
  setOfCheckedId = new Set<string>();
  constructor(
    private cd: ChangeDetectorRef,
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit(): void {
    this.getList();
  }
  submit() {
    console.log(this.setOfCheckedId);
    const arr: any[] = [];
    [...this.setOfCheckedId].forEach((ite) => {
      arr.push(this.list.find((it: Account) => it.id === ite));
    });
    this.action.emit(arr);
    this.activeModal.hide();
  }
  getList() {
    if (!this.organId) {
      return;
    }
    this.accountService
      .getSelectPageList(this.name.trim(), [this.organId])
      .pipe(
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          res = res || [];
          console.log(res);
          this.list = res;
        },
        () => (this.list = [])
      );
  }
  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(value: boolean): void {
    this.list.forEach((item: any) => this.updateCheckedSet(item.id, value));
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    this.checked = this.list.every((item: any) => this.setOfCheckedId.has(item.id));
    this.indeterminate = this.list.some((item: any) => this.setOfCheckedId.has(item.id)) && !this.checked;
  }
}
