<div class="organ-form">
  <div class="organ-form-header">
    <div class="title" translate>事故商机业务</div>
    <nz-switch [(ngModel)]="isOpen" (ngModelChange)="isOpenChange()"></nz-switch>
  </div>
  <div class="organ-form-body" *ngIf="isOpen">
    <nz-form-item>
      <nz-form-label [nzSpan]="8">
        {{ '客户组织联系人' | translate }}
      </nz-form-label>
      <div class="form-array-box">
        <div class="form-array" *ngFor="let item of params.linkMans; let i = index">
          <div class="form-box">
            <nz-form-item>
              <nz-form-label [nzSpan]="8" nzRequired nzFor="linkmanName">
                {{ '姓名' | translate }}
              </nz-form-label>
              <nz-form-control
                [nzSpan]="16"
                [nzValidateStatus]="getMessage('params.linkMans.' + i + '.linkmanName') ? 'error' : 'success'"
                [nzErrorTip]="linkmanNameErrorTpl"
              >
                <input
                  nz-input
                  id="linkmanName"
                  minlength="1"
                  maxlength="50"
                  zr-trim
                  [placeholder]="'请输入姓名' | translate"
                  [(ngModel)]="item.linkmanName"
                  (ngModelChange)="validateFormAll(params, true, 'params')"
                />
                <ng-template #linkmanNameErrorTpl let-control>
                  {{ getMessage('params.linkMans.' + i + '.linkmanName') }}
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item>
              <nz-form-label [nzSpan]="8" nzRequired nzFor="linkmanEmail">
                {{ '邮箱' | translate }}
              </nz-form-label>
              <nz-form-control
                [nzSpan]="16"
                [nzValidateStatus]="getMessage('params.linkMans.' + i + '.linkmanEmail') ? 'error' : 'success'"
                [nzErrorTip]="linkmanEmailErrorTpl"
              >
                <input
                  nz-input
                  id="linkmanEmail"
                  minlength="6"
                  maxlength="50"
                  zr-trim
                  [placeholder]="'请输入邮箱' | translate"
                  [(ngModel)]="item.linkmanEmail"
                  (ngModelChange)="validateFormAll(params, true, 'params')"
                />
                <ng-template #linkmanEmailErrorTpl let-control>
                  {{ getMessage('params.linkMans.' + i + '.linkmanEmail') }}
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item>
              <nz-form-label [nzSpan]="8" nzRequired nzFor="linkmanTel">
                {{ '电话' | translate }}
              </nz-form-label>
              <nz-form-control
                [nzSpan]="16"
                [nzValidateStatus]="getMessage('params.linkMans.' + i + '.linkmanTel') ? 'error' : 'success'"
                [nzErrorTip]="linkmanTelErrorTpl"
              >
                <input
                  nz-input
                  id="linkmanTel"
                  minlength="1"
                  maxlength="30"
                  zr-trim
                  [placeholder]="'请输入联系电话' | translate"
                  [(ngModel)]="item.linkmanTel"
                  (ngModelChange)="validateFormAll(params, true, 'params')"
                />
                <ng-template #linkmanTelErrorTpl let-control>
                  {{ getMessage('params.linkMans.' + i + '.linkmanTel') }}
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item>
              <nz-form-label [nzSpan]="8" nzFor="remark">
                {{ '备注' | translate }}
              </nz-form-label>
              <nz-form-control
                [nzSpan]="16"
                [nzValidateStatus]="getMessage('params.linkMans.' + i + '.remark') ? 'error' : 'success'"
                [nzErrorTip]="remarkErrorTpl"
              >
                <nz-textarea-count [nzMaxCharacterCount]="200">
                  <textarea
                    nz-input
                    id="remark"
                    minlength="0"
                    maxlength="200"
                    zr-trim
                    [placeholder]="'请输入备注' | translate"
                    [(ngModel)]="item.remark"
                    (ngModelChange)="validateFormAll(params, true, 'params')"
                  ></textarea>
                </nz-textarea-count>
                <ng-template #linkmanNameErrorTpl let-control>
                  {{ getMessage('params.linkMans.' + i + '.remark') }}
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>

          <button type="button" nz-button (click)="del(i)" *ngIf="params.linkMans.length > 1">
            {{ '删除' | translate }}
          </button>
        </div>
        <button type="submit" nz-button nzType="primary" (click)="add()" *ngIf="params.linkMans.length < 10">
          {{ '添加' | translate }}
        </button>
      </div>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="8" nzFor="operateWay">
        {{ '事故是否需要确认' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="12">
        <nz-radio-group [(ngModel)]="params.operateWay">
          <label nz-radio [nzValue]="2">{{ '是' | translate }}</label>
          <label nz-radio [nzValue]="1">{{ '否' | translate }}</label>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div class="organ-form-footer">
    <button type="button" nz-button (click)="reset()" [disabled]="pageMode === PageMode.Detail">
      {{ '取消' | translate }}
    </button>
    <button type="submit" nz-button nzType="primary" (click)="submit()" [disabled]="pageMode === PageMode.Detail">
      {{ '确定' | translate }}
    </button>
  </div>
</div>
