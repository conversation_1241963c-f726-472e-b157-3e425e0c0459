@import "../../../styles/themes/theme-value";
:host {
  display: block;
  height: 100%;
}

.organ-form {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 0 8px 8px 8px;
  overflow-y: auto;

  &-header {
    padding: 0 0 15px 0;
    display: flex;
    align-items: center;
  }

  &-body {
    flex: 1;
    height: 0;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    position: absolute;
    bottom: 0;
    right: 0;
  }
}

.title {
  position: relative;
  padding-left: 10px;
  line-height: 28px;
  color: #38476e;
  font-weight: 500;
  margin-right: 20px;

  &::before {
    width: 3px;
    height: 7px;
    content: " ";
    position: absolute;
    top: 50%;
    left: 0;
    border-radius: 1px;
    transform: translateY(-50%);
    background-color: $system-color;
  }
}

.count {
  position: absolute;
  right: 0;
  text-align: right;
  font-weight: 400;
  color: #575e72;
  line-height: 12px;
  font-size: 12px;
  background-color: #fff;
}

textarea {
  margin-top: 2px;
}

:host ::ng-deep {
  .ant-form-item-label > label {
    font-weight: 400;
    color: #575e72;
  }

  .ant-form-explain,
  .ant-form-extra {
    min-height: auto;
    font-size: 12px;
    line-height: 22px;
  }
  .ant-form-item-label {
    white-space: normal;
  }
}
.form-array-box,
.form-box {
  flex: 1;
}
.form-box {
  padding-right: 20px;
}
.form-array {
  display: flex;
  padding-right: 15px;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 7px;
  padding-top: 20px;
  margin-bottom: 10px;
}
