import { cloneDeep } from 'lodash';
import { ChangeDetectorRef, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FunctionJson, LinkMans, OpportunityType, PerformanceModel } from '@app/account-settings/models/performance';
import { PerformanceService } from '@app/account-settings/services/performance.service';
import { Logger, LoggerFactory } from '@app/core';
import { ValidateEncapsulation } from '@app/utils/class-validator';
import { TranslateService } from '@ngx-translate/core';
import { plainToClass } from 'class-transformer';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { PageMode } from '@app/shared/models/page-mode';

@Component({
  selector: 'app-performance-form',
  templateUrl: './performance-form.component.html',
  styleUrls: ['./performance-form.component.scss']
})
export class PerformanceFormComponent extends ValidateEncapsulation implements OnInit, OnChanges {
  @Input() pageMode: PageMode = PageMode.Detail;
  @Input() set organTree(value: NzTreeNodeOptions[]) {
    this._PlainErrors = {};
    if (!value || !value.length) {
      this.clearAll();
      return;
    }
    this.getDetail(value[0].key);
  }
  log: Logger;
  saving: boolean = false;
  translates: any = {
    保存失败: '',
    保存成功: '',
    获取详情失败: ''
  };
  performanceParams: PerformanceModel = new PerformanceModel();
  params: FunctionJson = new FunctionJson();
  isOpen: boolean = false;
  PageMode = PageMode;
  organId: string;

  constructor(
    private loggerFactory: LoggerFactory,
    public translate: TranslateService,
    public cd: ChangeDetectorRef,
    private performanceService: PerformanceService
  ) {
    super(translate);
    this.log = this.loggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit(): void {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.pageMode && changes.pageMode.currentValue && changes.pageMode.currentValue === PageMode.Detail) {
      this.clearAll();
    }
  }
  isOpenChange() {
    this._PlainErrors = {};
  }
  reset() {
    this.params = cloneDeep(this.performanceParams.functionJson);
    this.isOpen = this.performanceParams.isOpen;
  }
  submit() {
    if (this.saving) {
      return;
    }
    this.saving = true;
    this.validateFormAll(this.params, false, 'params').then((validateError: any) => {
      if (Object.keys(validateError).length) {
        this.saving = false;
        return;
      }
      let params;
      if (this.isOpen) {
        params = {
          ...this.performanceParams,
          functionJson: {
            ...this.params
          },
          isOpen: this.isOpen
        };
      } else {
        params = {
          ...this.performanceParams,
          isOpen: this.isOpen
        };
      }
      this.performanceService.create(params).subscribe(
        (res: any) => {
          this.saving = false;
          if (res) {
            this.performanceParams.id = res.id;
            this.performanceParams.orgId = res.orgId;
          }
          this.performanceParams.functionJson = cloneDeep(this.params);
          this.performanceParams.isOpen = this.isOpen;
          this.log.info(this.translates['保存成功']);
        },
        (error) => {
          this.log.error(this.translates['保存失败']);
          this.saving = false;
        }
      );
    });
  }
  getDetail(orgId: string) {
    this.saving = true;
    this.performanceService.detailAll(orgId).subscribe(
      (res) => {
        if (res && res.length) {
          const op = res.find((it: any) => it.businessType === OpportunityType.accident);
          if (op) {
            this.performanceParams = plainToClass(PerformanceModel, op);
            const fj = plainToClass(FunctionJson, op.functionJson);
            const lm = op.functionJson.linkMans.map((it: any) => plainToClass(LinkMans, it));
            fj.linkMans = lm;
            this.performanceParams.functionJson = cloneDeep(fj);
            this.params = cloneDeep(fj);
          } else {
            this.performanceParams = new PerformanceModel();
            this.params = new FunctionJson();
          }
        } else {
          this.performanceParams = new PerformanceModel();
          this.performanceParams.orgId = orgId;
          this.params = new FunctionJson();
        }
        this.isOpen = this.performanceParams.isOpen;
        this.cd.markForCheck();

        this.saving = false;
      },
      (err) => {
        this.log.error(this.translates['获取详情失败']);
        this.performanceParams = new PerformanceModel();
        this.performanceParams.orgId = orgId;
        this.params = new FunctionJson();
        this.isOpen = this.performanceParams.isOpen;
        this.cd.markForCheck();
        this.saving = false;
      }
    );
  }
  add() {
    this.params.linkMans.push(new LinkMans());
  }
  del(i: number) {
    this.params.linkMans.splice(i, 1);
  }
  clearAll() {
    this.performanceParams = new PerformanceModel();
    this.params = new FunctionJson();
    this.isOpen = false;
  }
}
