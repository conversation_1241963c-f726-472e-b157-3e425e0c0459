import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule, HttpClientJsonpModule } from '@angular/common/http';

import { TranslateModule } from '@ngx-translate/core';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTableModule } from 'ng-zorro-antd/table';

import { MapModule } from '@app/map/map.module';
import { LocalLibModule } from '@app/lib/local-lib/local-lib.module';

import { AccountSettingsModalComponent } from './components/account-settings-modal/account-settings-modal.component';
import { OrganizationFormComponent } from './components/organization-form/organization-form.component';
import { AccountFormComponent } from './components/account-form/account-form.component';
import { MapCityTabComponent } from './components/map-city-tab/map-city-tab.component';
import { AccountSecurityTabComponent } from './components/account-security-tab/account-security-tab.component';
import { CustomerManagementTabComponent } from './components/customer-management-tab/customer-management-tab.component';
import { RoleTabComponent } from './components/role-tab/role-tab.component';
import { AccountListComponent } from './components/account-list/account-list.component';
import { LogoThemeTabComponent } from './components/logo-theme-tab/logo-theme-tab.component';
import { DateTimeFormatTabComponent } from './components/date-time-format-tab/date-time-format-tab.component';
import { PerformanceFormComponent } from './components/performance-form/performance-form.component';
import { SelectAccountComponent } from './components/select-account/select-account.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    TranslateModule,

    NzIconModule,
    NzButtonModule,
    NzGridModule,
    NzDropDownModule,
    NzFormModule,
    NzInputModule,
    NzRadioModule,
    NzSelectModule,
    NzTreeSelectModule,
    NzDatePickerModule,
    NzUploadModule,
    NzTabsModule,
    NzTreeModule,
    NzToolTipModule,
    NzSpinModule,
    NzSwitchModule,
    NzTagModule,
    NzTableModule,

    MapModule,
    LocalLibModule
  ],
  declarations: [
    AccountSettingsModalComponent,
    OrganizationFormComponent,
    AccountFormComponent,
    MapCityTabComponent,
    AccountSecurityTabComponent,
    CustomerManagementTabComponent,
    RoleTabComponent,
    AccountListComponent,
    LogoThemeTabComponent,
    DateTimeFormatTabComponent,
    PerformanceFormComponent,
    SelectAccountComponent
  ],
  entryComponents: [AccountSettingsModalComponent]
})
export class AccountSettingsModule {}
