import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { Res } from '@app/shared/models/type';
import { Role } from '../models/role';

import { environment } from '@env/environment';
import { WebApiResultResponse } from '@app/core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class RoleService extends WebApiResultResponse {
  userId = '';

  constructor(private http: HttpClient) {
    super();
    const userIdKey = `oidc.user:${environment.odic.config.authority}:${environment.odic.config.client_id}`;
    let userId = sessionStorage.getItem(userIdKey);
    this.userId = JSON.parse(userId).profile.sub;
  }

  getPageList(roleName = '') {
    const params: any = {
      filter: {
        op: 'and',
        rules: [
          {
            field: 'applicationIds',
            op: 'in',
            datas: [environment.api.userCenterV2.applicationId]
          },
          {
            field: 'roleName',
            op: 'cn',
            data: roleName
          },
          {
            field: 'createdById',
            op: 'cn',
            data: this.userId
          }
        ]
      },
      filters: [],
      sort: [],
      pageIndex: 1,
      pageSize: 1_000_000
    };
    const url = `${environment.api.userCenterV2.origin}user/roles/paging`;
    return this.http.post<Role[]>(url, params);
  }

  create(params: Role) {
    const url = `glcrm-account-api/v1/api/role`;
    return this.http.post<Res<Role>>(url, params);
  }

  edit(params: Role) {
    const url = `glcrm-account-api/v1/api/role`;
    return this.http.put<Res<Role>>(url, params);
  }

  delete(id: string) {
    const url = `glcrm-account-api/v1/api/role/del`;
    return this.http.post<Res<string>>(url, [id]);
  }

  detail(id: string): Observable<Role> {
    const url = `${environment.api.userCenterV2.origin}user/modules/operates/tree/roles/${id}`;
    return this.http.get<Role>(url);
  }
}
