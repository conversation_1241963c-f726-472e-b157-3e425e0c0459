import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { Res } from '@app/shared/models/type';

import { WebApiResultResponse } from '@app/core/http/web-api-result-response';
import { PerformanceModel } from '../models/performance';

@Injectable({
  providedIn: 'root'
})
export class PerformanceService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  create(params: PerformanceModel) {
    const url = `glcrm-clue-opportunity-api/api/v1/businessconfigure/configure`;
    return this.http.put<Res<PerformanceModel>>(url, params);
  }

  detailAll(orgId: string): Observable<any> {
    const url = `glcrm-clue-opportunity-api/api/v1/businessconfigure/configurelist`;
    return this.http.get(url, { params: { orgId } });
  }
}
