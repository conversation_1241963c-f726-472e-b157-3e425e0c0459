import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { Res } from '@app/shared/models/type';
import { Organization, OrganVehicleNode } from '../models/organization';

import { environment } from '@env/environment';
import { WebApiResultResponse } from '@app/core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class OrganizationService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  create(params: Organization) {
    const url = `glcrm-account-api/v1/api/organization`;
    return this.http.post<Res<Organization>>(url, params);
  }

  edit(params: Organization) {
    const url = `glcrm-account-api/v1/api/organization`;
    return this.http.put<Res<string>>(url, params);
  }

  delete(id: string) {
    const url = `glcrm-account-api/v1/api/organization`;
    return this.http.delete<Res<string>>(url, { params: { id } });
  }

  detail(id: string): Observable<Organization> {
    const url = `${environment.api.userCenterV2.origin}organization/organizations/${id}`;
    return this.http.get<Organization>(url);
  }
  getSpecialistList(id: string): Observable<Res<Organization[]>> {
    const url = `glcrm-account-api/v1/api/operatorInfo/detail/${id}`;
    return this.http.get<Res<Organization[]>>(url);
  }
  saveSpecialistList(params: any) {
    const url = `glcrm-account-api/v1/api/operatorInfo/save`;
    return this.http.post<Res<any>>(url, params);
  }

  /**
   * 获取机构车辆树
   */
  getOrganVehicleTree() {
    const url = `glcrm-account-api/v1/api/organization/getOrgAndCar`;
    return this.http.get<Res<OrganVehicleNode>>(url);
  }
}
