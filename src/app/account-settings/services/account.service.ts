import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { Res } from '@app/shared/models/type';
import { Account, AccountSetting } from '../models/account';

import { environment } from '@env/environment';
import { WebApiResultResponse } from '@app/core/http/web-api-result-response';
import { accountSetting } from '../models/account-setting';

@Injectable({
  providedIn: 'root'
})
export class AccountService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  getPageList(name = '', organizationIds: string[] = []) {
    const params: any = {
      filter: {
        op: 'or',
        rules: [
          {
            field: 'organizationIds',
            op: 'in',
            datas: organizationIds
          },
          { field: 'name', op: 'cn', data: name },
          { field: 'loginName', op: 'cn', data: name }
        ]
      },
      filters: [],
      sort: [
        {
          field: 'createdAt',
          sort: 'desc'
        }
      ],
      pageIndex: 1,
      pageSize: 1_000_000
    };
    const url = `${environment.api.userCenterV2.origin}user/organizationType/users/extension/paging`;
    return this.http.post<Account[]>(url, params);
  }
  getSelectPageList(name = '', organizationIds: string[] = []) {
    const params: any = {
      filter: {
        op: 'or',
        rules: [
          {
            field: 'organizationIds',
            op: 'in',
            datas: organizationIds
          },
          { field: 'name', op: 'cn', data: name },
          //   { field: 'loginName', op: 'cn', data: name },
          { field: 'email', op: 'cn', data: name }
        ]
      },
      filters: [],
      sort: [
        {
          field: 'createdAt',
          sort: 'desc'
        }
      ],
      pageIndex: 1,
      pageSize: 1_000_000
    };
    const url = `${environment.api.userCenterV2.origin}user/organizationType/users/extension/paging`;
    return this.http.post<Account[]>(url, params);
  }

  bingRole(id: string, roleIds: string[]) {
    const url = `glcrm-account-api/v1/api/user/${id}/roleIds`;
    return this.http.post<Res<string>>(url, roleIds);
  }

  create(params: Account) {
    const url = `glcrm-account-api/v1/api/user`;
    return this.http.post<Res<Account>>(url, params);
  }

  edit(params: Account) {
    const url = `glcrm-account-api/v1/api/user`;
    return this.http.put<Res<Account>>(url, params);
  }

  delete(id: string) {
    const url = `glcrm-account-api/v1/api/user`;
    return this.http.delete<Res<string>>(url, { params: { id } });
  }

  detail(id: string): Observable<Account[]> {
    const url = `${environment.api.userCenterV2.origin}user/users/ids?list=${id}`;
    return this.http.get<Account[]>(url);
  }

  resetPwd(loginName: string) {
    const url = `glcrm-account-api/v1/api/user/resetPassword`;
    return this.http.post<Res<string>>(url, { loginName });
  }

  getAccountSetting() {
    const url = `glcrm-account-api/v1/api/user/getAccountSetting`;
    return this.http.get<Res<AccountSetting>>(url);
  }

  setAccountSetting(params: AccountSetting) {
    const url = `glcrm-account-api/v1/api/user/setAccountSetting`;
    return this.http.post<Res<AccountSetting>>(url, params);
  }

  setZone() {
    const url = `glcrm-account-api/v1/api/user/setZone?zone=${accountSetting.timeZone}`;
    return this.http.get<void>(url);
  }

  /**
   * 同步glcrm账号信息，
   * @returns
   */
  syncAccountInfo(): Observable<any> {
    const url = `glcrm-account-api/v1/api/user/userInfo`;
    return this.http.get(url);
  }
}
