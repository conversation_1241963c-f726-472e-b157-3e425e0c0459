import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';
import { CommandsMainComponent } from './partial/commands-main/commands-main.component';
import { CommandsCreateComponent } from './partial/commands-create/commands-create.component';

const routes: Routes = RouteExtensions.withHost(
  { path: '', component: CommandsMainComponent, data: { title: '指令管理' } },
  [{ path: 'new', component: CommandsCreateComponent, data: { title: '新建指令' } }]
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class CommandsManagementRoutingModule {}
