import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { WebApiResultResponse } from '@core/http/web-api-result-response';
import { OssConfig } from '@app/shared/models/oss';
import { Res } from '@app/shared/models/type';

@Injectable({
  providedIn: 'root'
})
export class CommandsManagementService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  // 获取发送指令列表
  pageCommand(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/pageCommand/${params.page}/${params.limit}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 指令条件查询
  pageByDeviceCommand(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/pageByDeviceCommand/${params.page}/${params.limit}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 条件判断是否可发送指令
  commandJudgment(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/commandJudgment`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 发送指令
  commandSend(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/commandSend`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  usrCommandSend(params: any): Observable<any> {
    // const url = `glcrm-vehicle-api/v1/api/command/customCommandSend`;
    const url = `glcrm-vehicle-api/v1/api/command/custom/send`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取8小时token
  getToken(params: any): Observable<any> {
    // tslint:disable-next-line:max-line-length
    const url = `https://uc-identity-fat.lunz.cn/connect/token`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 系统指令列表
  sysCommand(): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/mouldList`;
    return this.http.post(url, {}).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 自定义指令列表
  commandPage(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/commandMouldList${
      params.commandName ? '?commandName=' + params.commandName : ''
    }`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  deleteCommand(id: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/deleteCommandMould/${id}`;
    return this.http.delete(url);
  }

  addCommand(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/addCommandMould`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  editCommand(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/updateCommandMould`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 获取oss配置
   * @returns
   */
  getOssConfig() {
    const url = `glcrm-vehicle-api/v1/api/command/getOssProperties`;
    return this.http.get<Res<OssConfig>>(url);
  }
}
