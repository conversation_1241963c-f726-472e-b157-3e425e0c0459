import { Component, OnInit } from '@angular/core';
import { PositionManagementService } from '@app/position-management/shared/position-management.service';

@Component({
  selector: 'app-commands-main',
  templateUrl: './commands-main.component.html',
  styleUrls: ['./commands-main.component.scss']
})
export class CommandsMainComponent implements OnInit {
  shrink: boolean = false; // 是否收起侧边栏
  devices: any[];

  constructor(private positionManagementService: PositionManagementService) {}

  ngOnInit() {
    this.getDevices();
  }

  toggleShrink(shrink: boolean) {
    this.shrink = shrink;
  }

  getDevices() {
    const params: any = {
      query: '',
      status: [],
      orgId: ''
    };
    this.positionManagementService.getAllDevicesPosition(params).subscribe((res) => {
      if (!res.success) {
        return;
      }
      this.devices = res.data.filter((device: any) => device.lng && device.lat);
    });
  }
}
