import { Component, OnInit, Output, AfterViewInit, ChangeDetectorRef, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { TranslateService } from '@ngx-translate/core';

import { Order } from '../../shared/models';
import { DeviceStatus, deviceStatusInfo } from '@app/data-management/device-management/shared/models/device';

import { CommandsManagementService } from '@app/commands-management/shared/commands-management.service';
import { CommandsSendConfirmModalComponent } from '../components/commands-send-confirm-modal/commands-send-confirm-modal.component';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { CustomValidators } from 'ngx-custom-validators';

@Component({
  selector: 'app-commands-create',
  templateUrl: './commands-create.component.html',
  styleUrls: ['./commands-create.component.scss']
})
export class CommandsCreateComponent implements OnInit, AfterViewInit {
  log: Logger;
  loading = false;
  saving = false;
  @Output() action = new EventEmitter();

  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  fenceList: Array<any> = [];

  commandsTypeId: any;
  pageIndex: number = 1; // 当前页索引

  form: FormGroup;
  deviceTypeList: Array<any> = [];
  selectImei: any;

  constructor(
    private formBuilder: FormBuilder,
    private translate: TranslateService,
    private modalService: BsModalService,
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private commandsManagementService: CommandsManagementService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger('');
    this.translate.get('新建指令').subscribe((res: string) => {});
  }

  ngOnInit() {
    this.getCommadlist();
  }

  getCommadlist() {
    this.commandsManagementService.commandPage({ commandName: null }).subscribe(
      (response) => {
        this.commandsManagementService.sysCommand().subscribe(
          (res) => {
            this.deviceTypeList = [...res.data, ...response.data].map((item) => {
              item.checked = false;
              return item;
            });
          },
          (error) => console.log('系统指令列表获取设备', error)
        );
      },
      (error) => console.log('自定义列表获取设备', error)
    );
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  loadFences() {
    const params = {
      page: this.pageIndex,
      limit: 100000,
      // deviceStatus: 2,
      deviceStatus: '',
      imei: this.form.value.imei || ''
    };
    this.selectImei = null;
    this.commandsManagementService
      .pageByDeviceCommand(params)
      .pipe()
      .subscribe(
        (response) => {
          if (response.success) {
            this.fenceList = response.data.records;
            if (this.fenceList.length === 1) {
              this.selectImei = this.fenceList[0];
            }
          } else {
            console.log('请求错误', response.message);
          }
        },
        (error) => console.log('错误信息', error)
      );
  }
  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }

    if (!this.selectImei) {
      this.translate.get('请先搜索设备').subscribe((res: string) => {
        this.log.error(res);
      });
      return;
    }

    // if (this.selectedUnbound.length > 1) {
    //     this.translate.get('仅支持对一个对象发送指令，请重新选择。').subscribe((res: string) => {
    //         this.log.error(res);
    //     });
    //     return;
    // }
    const initialState = { data: this.selectImei, type: this.form.get('commandsTypeId').value };
    const modalRef: BsModalRef = this.modalService.show(CommandsSendConfirmModalComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: Order) => {
      onHidden.unsubscribe();
    });
    modalRef.content.action.subscribe((value: any) => {
      console.log(value, 2323);
      if (value) {
        // onHidden.unsubscribe();
        this.activeModal.hide();
        this.action.emit('success');
      }
    });
  }

  buildForm() {
    this.form = this.formBuilder.group({
      imei: ['', [Validators.required, CustomValidators.number]],
      commandsTypeId: [null, [Validators.required]]
    });
  }
  // 双击
  onDoubleClick(item: any) {
    this.selectImei = item;
    this.fenceList.forEach((one) => {
      one.select = false;
    });
    item.select = true;
    this.form.patchValue({
      imei: item.deviceNo
    });
  }
}
