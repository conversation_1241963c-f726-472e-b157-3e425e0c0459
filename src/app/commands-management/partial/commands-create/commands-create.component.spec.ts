import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommandsCreateComponent } from './commands-create.component';

describe('CommandsCreateComponent', () => {
  let component: CommandsCreateComponent;
  let fixture: ComponentFixture<CommandsCreateComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CommandsCreateComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommandsCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
