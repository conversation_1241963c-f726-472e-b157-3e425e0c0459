.m-form__group {
  padding-left: 20px;
  padding-right: 30px;
}

.m-form .m-form__group {
  margin-bottom: 0;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 0;
}

.col-form-label {
  text-align: right !important;
}

.text_right {
  text-align: right;
  margin: 30px;
}

.query-toolbar {
  display: grid;
  align-items: center;
}

.search-input {
  display: flex;
  flex-direction: row;
  align-items: center;

  .search-vehicle {
    width: 100%;
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    border: 1px solid #d3d6de;
    border-radius: 4px;
    margin-left: 10px;
  }
}

.show {
  height: 378px;
}

.material {
  height: 100%;
  min-height: 0 !important;
}

.querybtn {
  width: 32px;
  height: 32px;
  border: none;
  margin: 0px;
  padding: 0px;
  background: #ffffff;
  outline: none;
}

.clearbtn {
  width: 38px;
  height: 38px;
  border: none;
  margin: 0px;
  padding: 0px;
  background: #ffffff;
  outline: none;
  border: 1px solid #d3d6de;
  border-radius: 4px 4px 4px 4px;
}

h5 {
  margin-left: 6px;
  display: inline-block;
  margin-bottom: 0 !important;
}

.little-title {
  margin-left: 25px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  flex-direction: row;
}

.footer-style {
  display: flex;
  justify-content: flex-end;
}

.button-row {
  margin: 18px 60px;
}

.query-input:focus {
  box-shadow: none;
}

:host ::ng-deep {
  .card-body {
    border: 0px !important;
  }

  .dropmenu {
    display: none !important;
  }
}

.modal-body {
  width: 654px;
  max-height: calc(100vh - 120px) !important;
  padding: 20px !important;
  .ant-form-item-label {
    text-align: right !important;
  }
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  // width: 32%;
}

.ant-col-sm-14 {
  width: 60.333333%;
}

.modal-header {
  padding: 15px 20px !important;
  background: #f3f3f3 !important;
  .modal-title {
    color: #1f2733;
  }
  button {
    color: #1f2733 !important;
  }
}

.modal-footer button:first-child {
  background: #f3f3f3;
  color: #1f2733;
  border: none;
}

.imei-tabel {
  width: 100%;
  & > tr:first-child {
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 999;
    td {
      padding: 7px;
      background-color: #f2f4f7;
      color: #1f2733;
      font-weight: 600;
    }
  }
  tr td {
    padding: 5px;
  }
  tr.select td {
    background-color: #ecf2fe;
    font-weight: 600;
  }
  .imei-table-tbody {
    max-height: 320px;
    overflow-y: auto;
  }
}

.table-div {
  max-height: 356px;
  overflow: auto;
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}
