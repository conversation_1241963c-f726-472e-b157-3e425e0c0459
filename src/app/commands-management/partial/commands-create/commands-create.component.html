<div class="modal-header">
  <h5 class="modal-title">{{ '新建' | translate }} {{ '指令' | translate }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="5" [nzXs]="24" nzRequired nzFor="commandsTypeId">
        {{ '类型' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="commandIdErrorTpl">
        <nz-select
          formControlName="commandsTypeId"
          id="commandsTypeId"
          nzPlaceHolder="{{ '请选择类型' | translate }}"
          nzShowSearch
          nzAllowClear
        >
          <nz-option
            *ngFor="let option of deviceTypeList"
            [nzLabel]="option.commandName | translate"
            [nzValue]="option"
          ></nz-option>
        </nz-select>
        <ng-template #commandIdErrorTpl let-control>
          {{ '请选择类型' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="5" [nzXs]="24">
        {{ '发送方式' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24">
        <input nz-input placeholder="{{ '网络指令' | translate }}" [disabled]="true" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="5" [nzXs]="24" nzRequired nzFor="imei">
        {{ '设备号/车牌号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="imeiErrorTpl">
        <nz-input-group [nzSuffix]="suffixIconSearch">
          <input
            formControlName="imei"
            nz-input
            placeholder="{{ '请输入设备号' | translate }}"
            (keyup.enter)="loadFences()"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i nz-icon nzType="search" style="cursor: pointer" (click)="loadFences()"></i>
        </ng-template>
        <ng-template #imeiErrorTpl let-control>
          {{ '请输入正确的设备号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <div class="table-div">
      <!-- <nz-form-label style="visibility: hidden" [nzSm]="5" [nzXs]="24"></nz-form-label> -->
      <table class="imei-tabel" *ngIf="fenceList.length > 0">
        <tr>
          <td>{{ '设备号' | translate }}</td>
          <td>{{ '设备类型' | translate }}</td>
          <td>{{ '设备状态' | translate }}</td>
          <td>{{ '车牌号' | translate }}</td>
        </tr>
        <tbody class="imei-table-tbody">
          <tr *ngFor="let item of fenceList" (dblclick)="onDoubleClick(item)" [ngClass]="{ select: item.select }">
            <td>{{ item.deviceNo }}</td>
            <td>
              <span *ngIf="item.isWireLess == 0">&nbsp;{{ '有线设备' | translate }}</span>
              <span *ngIf="item.isWireLess == 1">&nbsp;{{ '无线设备' | translate }}</span>
            </td>
            <td>
              <nz-badge
                [nzColor]="deviceStatusInfo[item.deviceStatus || DeviceStatus.Invalid].backgroundColor"
                [nzText]="deviceStatusInfo[item.deviceStatus || DeviceStatus.Invalid].name | translate"
              ></nz-badge>
            </td>
            <td>{{ item.plateNumber }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nzType="default" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="submit()">
    {{ '发送' | translate }}
  </button>
</div>
