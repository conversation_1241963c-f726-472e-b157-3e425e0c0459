$gridColumnsWidth: 2% 16% 15% 18% 20% 15% 10% 2%;

:host {
  height: 100%;
  font-size: 12px;
  font-weight: 400;
}

.page-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  font-size: 12px;
  box-shadow: 0px 2px 8px 0px rgba(68, 68, 68, 0.53);
}

.page-card-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
  padding: 16px 16px 0;
}

.page-card-footer {
  padding: 16px;

  .page-card-pagination {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;

    span {
      padding-right: 5px;
    }

    button {
      height: 32px;
      padding: 0;
    }
  }
}

.condition {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &-left {
    flex: 1;
    width: 0;
  }

  &-right {
    margin-left: 16px;
    height: 32px;
    padding: 0;
  }
}

.name {
  nz-input-group {
    padding: 4px 7px;
  }
}

.page-card-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.page-card-thead {
  display: grid;
  grid-template-columns: $gridColumnsWidth;
  align-items: center;
  margin-top: 9px;
  border-radius: 4px;

  span {
    padding: 0.5rem 0;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 600;
    // color: #1f2733;
    line-height: 36px;
  }
}

.page-card-tbody {
  margin-top: 9px;
  overflow-y: scroll;
  font-size: 14px;
  color: #333;

  &::-webkit-scrollbar {
    display: none;
  }
}

.page-card-tr {
  display: grid;
  grid-template-columns: $gridColumnsWidth;
  align-items: center;
  height: 50px;

  span {
    padding: 12px 5px 12px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:hover {
    border-radius: 4px;
  }
}

.no-data {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;

  span {
    font-size: 12px;
    font-weight: 400;
  }
}

.command {
  height: 150px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.command-type-list {
  max-height: 300px;
  padding: 16px;
  overflow-y: auto;
}

::ng-deep {
  .command-type-list-popover {
    width: 160px;

    .ant-popover-inner-content {
      padding: 12px 0;
    }

    .ant-checkbox-wrapper {
      display: flex;
      align-items: center;

      .ant-checkbox + span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.query-search {
  padding: 1.25rem;
}

.vehicle_search_input {
  padding: 7px 11px;
  border-radius: 5px !important;
  margin: 10px 0 15px;
}

.list_header {
  position: relative;
}

.btn-div {
  position: absolute;
  right: 6%;
  bottom: 30px;
}

.add-command-btn {
  border: 1px solid #4777fd;
  color: #4777fd;
  i {
    margin-right: 4px;
  }
  span {
    position: relative;
    top: -5px;
  }
}

.col-md-6 nz-select {
  width: 100%;
  position: relative;
  top: 10px;
}

.all-records {
  height: 32px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  line-height: 32px;
  font-size: 14px;
  padding: 0 16px !important;
  display: inline-block;
  color: rgba(0, 0, 0, 0.65) !important;
  cursor: pointer;
  margin-right: 25px;
}

.all-records-num {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65) !important;
  display: inline-block;
  text-align: center;
  width: 120px;
  margin-right: 25px;
}
