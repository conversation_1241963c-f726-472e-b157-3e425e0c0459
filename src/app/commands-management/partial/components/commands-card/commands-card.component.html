<!-- 车辆列表卡片开始 -->
<div class="custom-table">
  <div class="m-portlet list_header">
    <!-- <nz-input-group nzSize="small" class="name-group" [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl"> -->
    <div class="form-row">
      <div class="query-search col-md-6 row">
        <div class="col-md-6">
          <nz-input-group nzSize="small" class="name-group">
            <input
              type="text"
              class="vehicle_search_input"
              [(ngModel)]="queryValue"
              nz-input
              placeholder="{{ '请输入车牌号/设备号' | translate }}"
              (keyup.enter)="getPageList(true)"
              (keyup.esc)="clear()"
            />
          </nz-input-group>
        </div>
        <div class="col-md-6">
          <nz-select
            [(ngModel)]="commandType"
            nzPlaceHolder="{{ '指令' | translate }} {{ '指令类型' | translate }}"
            nzShowSearch
            nzAllowClear
          >
            <nz-option
              *ngFor="let option of commandTypeList"
              [nzLabel]="option.label | translate"
              [nzValue]="option.value"
            ></nz-option>
          </nz-select>
        </div>
      </div>
      <div class="btn-div">
        <button nz-button nzType="primary" (click)="getPageList(true)">
          {{ '搜索' | translate }}
        </button>
        <button nz-button nzType="default" (click)="clear()">
          {{ '重置' | translate }}
        </button>
      </div>
    </div>
  </div>
  <div class="m-portlet">
    <div class="m-portlet__head">
      <div class="m-portlet__head-tools">
        <ul class="m-portlet__nav top-button">
          <li class="m-portlet__nav-item" [appApplyPermission]="'template'">
            <button
              class="add-command-btn"
              nz-button
              (click)="commandTemplates()"
              nzType="default"
              nz-tooltip
              nzTooltipTitle="{{ '自定义指令' | translate }}"
            >
              <i class="export-icon iot icon-xingzhuang1"></i>
              {{ '自定义指令' | translate }}
            </button>
          </li>
          <li class="m-portlet__nav-item" [appApplyPermission]="'add'">
            <button nz-button nzType="primary" (click)="add()">
              <span nz-icon nzType="plus" nzTheme="outline"></span>
              {{ '新建' | translate }}
            </button>
          </li>
        </ul>
      </div>
    </div>

    <div class="m-portlet__body p-0">
      <div class="page-card-table">
        <!-- 表头开始 -->
        <div class="page-card-thead">
          <span></span>
          <span translate>设备号</span>
          <span translate>车牌号</span>
          <span translate>类型</span>
          <span translate>发送指令</span>
          <span translate>接收指令</span>
          <span translate>操作人</span>
          <span></span>
        </div>
        <!-- 表头结束 -->
        <!-- 表内容开始 -->
        <div class="page-card-tbody">
          <!-- 一级 -->
          <div class="no-data" *ngIf="!pageList || pageList.length === 0">
            <img src="/assets/font/noDate.png" alt="" />
            <span translate>暂无数据</span>
          </div>
          <div class="page-card-tr" *ngFor="let item of pageList">
            <!-- 占位符 -->
            <span></span>
            <div nz-row nzAlign="middle" [nzGutter]="[5, 0]">
              <div nz-col>{{ item.deviceNo }}</div>
              <nz-badge
                nz-col
                [nzColor]="deviceStatusInfo[item?.deviceStatus || DeviceStatus.Invalid].backgroundColor"
                nz-tooltip
                nzTooltipPlacement="top"
                [nzTooltipTitle]="deviceStatusInfo[item?.deviceStatus || DeviceStatus.Invalid].name | translate"
              ></nz-badge>
            </div>
            <div nz-row nzAlign="middle" [nzGutter]="[5, 0]">
              <div nz-col>{{ item.plateNumber || '--' }}</div>
              <img
                *ngIf="item.plateNumber"
                class="img-car"
                [src]="vehicleStatusInfo[item?.status || VehicleStatus.Offline].legendIcon"
                nz-col
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ vehicleStatusInfo[item?.status || VehicleStatus.Offline].name | translate }}"
              />
            </div>
            <!-- 指令类型 1断油断电 2恢复油电 -->
            <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ item.commandName | translate }}">
              {{ item.commandName || '--' | translate }}
            </span>
            <!-- 发送指令状态 1是 0否 发送时间 -->
            <span>
              {{ (item.sendStatus === 1 ? '是' : item.sendStatus === 0 ? '否' : '--') | translate }}
              <div>
                {{ item.sendTime ? (item.sendTime | localDate : accountSetting.dateTimeFormat) : '--' }}
              </div>
            </span>
            <!-- 接收指令状态 1是 0否 接收时间 -->
            <span>
              {{ (item.receiveStatus === 1 ? '是' : item.receiveStatus === 0 ? '否' : '--') | translate }}
              <div>
                {{ item.receiveTime ? (item.receiveTime | localDate : accountSetting.dateTimeFormat) : '--' }}
              </div>
            </span>
            <!-- 操作人 -->
            <span>
              {{ item.createdBy || '--' }}
            </span>
          </div>
        </div>
        <!-- 表内容结束 -->
      </div>
      <div class="page-card-footer">
        <!-- 显示条数和翻页按钮 -->
        <div class="page-card-pagination">
          <span *ngIf="totalNumber">
            <span translate class="all-records" *ngIf="!recordsIsShow" (click)="recordsIsShow = true">共</span>
            <span class="all-records-num" *ngIf="recordsIsShow" (click)="recordsIsShow = false">{{ totalNumber }}</span>
            <!-- <span translate>条，第</span>
            {{ pageIndex }}
            {{ '/' + totalPage }}
            <span translate>页</span> -->
          </span>
          <span>
            <button nz-button nzSize="small" (click)="pageLess()" [disabled]="pageIndex === 1">
              <i nz-icon nzType="left" nzTheme="outline"></i>
            </button>
            <button nz-button nzSize="small" (click)="pageAdd()" [disabled]="pageIndex === totalPage">
              <i nz-icon nzType="right" nzTheme="outline"></i>
            </button>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="page-card-body">
    <div class="condition">
      <div class="condition-left" nz-row nzAlign="middle">
        <div class="name" nz-col nzFlex="350px">

          <ng-template #suffixIconSearch>
            <i nz-icon nzType="search" nzTheme="outline" class="pointer" (click)="getPageList(true)"></i>
          </ng-template>
          <ng-template #inputClearTpl>
            <i *ngIf="queryValue" nz-icon nzType="close" nzTheme="outline" (click)="clear()"></i>
          </ng-template>
        </div>
        <div nz-col nzFlex="16px"></div>
        <div nz-col nzFlex="auto">
          <div nz-row nzAlign="middle" [nzGutter]="[10, 10]">
            <i
              class="pointer"
              appApplyPermission="add"
              nz-col
              nz-icon
              nzType="plus-circle"
              nzTheme="outline"
              nz-tooltip
              nzTooltipTitle="{{ '新 建' | translate }}"
              (click)="add()"
            ></i>
            <span nz-col appApplyPermission="add">|</span>

            <span
              nz-col
              nz-popover
              nzPopoverTrigger="click"
              nzPopoverPlacement="bottom"
              [nzPopoverContent]="filterPopover"
              [(nzPopoverVisible)]="filterVisible"
              nzPopoverOverlayClassName="command-type-list-popover"
            >
              <img
                [src]="filterVisible ? '/assets/font/filter_selected.svg' : '/assets/font/filter.png'"
                class="pointer"
                nz-tooltip
                nzTooltipTitle="{{ '筛选' | translate }}"
              />
            </span>
            <span nz-col>|</span>
            <i
              class="pointer"
              nz-col
              nz-icon
              nzType="reload"
              nzTheme="outline"
              nz-tooltip
              nzTooltipPlacement="top"
              nzTooltipTitle="{{ '刷新' | translate }}"
              (click)="clear()"
            ></i>

            <span nz-col appApplyPermission="command_templates">|</span>
            <span nz-col appApplyPermission="command_templates">
              <img
                src="/assets/font/command.png"
                class="pointer"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '自定义指令' | translate }}"
                (click)="commandTemplates()"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div> -->
<!-- 车辆列表卡片结束 -->

<ng-template #filterPopover>
  <div nz-row [nzGutter]="[0, 16]">
    <div nz-col [nzSpan]="24" class="command-type-list">
      <nz-checkbox-wrapper nz-row [nzGutter]="[0, 8]">
        <div nz-col [nzSpan]="24" *ngFor="let item of commandTypeList">
          <span nz-checkbox [nzValue]="item.value" [(ngModel)]="item.checked">
            {{ item.label | translate }}
          </span>
        </div>
      </nz-checkbox-wrapper>
    </div>

    <div nz-col [nzSpan]="24">
      <div nz-row nzAlign="middle" nzJustify="end" [nzGutter]="[8, 0]">
        <div nz-col [nzPull]="1">
          <button nz-button nzSize="small" (click)="reset()">{{ '重置' | translate }}</button>
        </div>
        <div nz-col [nzPull]="1">
          <button nz-button nzSize="small" nzType="primary" (click)="determine()">
            {{ '确定' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
