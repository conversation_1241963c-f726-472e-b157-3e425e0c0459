import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { Logger, LoggerFactory } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { vehicleStatusInfo, VehicleStatus } from '@app/data-management/vehicle-management/shared/models/vehicle';
import { DeviceStatus, deviceStatusInfo } from '@app/data-management/device-management/shared/models/device';

import { CommandsManagementService } from '@app/commands-management/shared/commands-management.service';
import { CommandsTemplateModalComponent } from '../commands-template-modal/commands-template-modal.component';
import { CommandsCreateComponent } from '../../commands-create/commands-create.component';

@Component({
  selector: 'app-commands-card',
  templateUrl: './commands-card.component.html',
  styleUrls: ['./commands-card.component.scss']
})
export class CommandsCardComponent implements OnInit {
  @Output() speedControl: EventEmitter<any> = new EventEmitter(); // 速度控制器
  //   @Output() putawaySidebar: EventEmitter<any> = new EventEmitter();

  log: Logger;

  accountSetting = accountSetting;
  VehicleStatus = VehicleStatus;
  vehicleStatusInfo = vehicleStatusInfo;
  recordsIsShow = false;

  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  isShrink: boolean;
  queryValue: string; // 搜索框的值
  pageList: Array<any> = [];

  filterVisible: boolean = false;
  commandTypeList: Array<{ label: string; value: string; checked: boolean }> = [];
  commandTypeNameList: string[] = [];

  totalNumber = 0; // 总条数
  pageIndex = 1; // 当前页索引
  totalPage = 0; // 当前页索引
  commandType: any;

  constructor(
    private router: Router,
    private loggerFactory: LoggerFactory,
    private commandsManagementService: CommandsManagementService,
    private bsModalService: BsModalService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.getPageList();
    this.getCommandList();
  }

  getCommandList() {
    this.commandsManagementService.commandPage({ commandName: null }).subscribe((res) => {
      this.commandsManagementService.sysCommand().subscribe((res1) => {
        this.commandTypeList = [...res1.data, ...res.data].map((item) => {
          return {
            checked: false,
            label: item.commandName,
            value: item.commandName
          };
        });
      });
    });
  }

  getPageList(resetIndex?: boolean) {
    this.recordsIsShow = false;
    if (resetIndex) {
      this.pageIndex = 1;
    }
    const params = {
      page: this.pageIndex,
      limit: 10,
      imei: this.queryValue ? this.queryValue.trim() : undefined,
      types: this.commandType ? [this.commandType] : []
    };
    this.commandsManagementService
      .pageCommand(params)
      .pipe()
      .subscribe((res) => {
        if (!res.success) {
          return;
        }
        this.pageList = res.data.records;
        this.totalNumber = res.data.total;
        this.pageIndex = res.data.current;
        this.totalPage = res.data.pages;
        if (!this.totalNumber) {
          this.pageIndex = 0;
        }
      });
  }

  // 收起侧边栏
  //   putaway(type: boolean) {
  //     this.putawaySidebar.emit(type);
  //   }

  // 新建指令
  add() {
    // this.router.navigate(['/commandsManagement/new']);
    // 指令下发
    const modalRef: BsModalRef = this.bsModalService.show(CommandsCreateComponent, {
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.bsModalService.onHidden.subscribe((params: any) => {
      console.log(12312313123);
      onHidden.unsubscribe();
    });
    modalRef.content.action.subscribe((value: any) => {
      console.log(value, 2323);
      if (value) {
        this.getPageList();
      }
    });
  }

  // 刷新
  clear() {
    this.queryValue = '';
    this.reset();
    this.getPageList();
  }

  // 自定义指令
  commandTemplates() {
    this.bsModalService.show(CommandsTemplateModalComponent, {
      ignoreBackdropClick: true,
      class: 'modal-command'
    });
  }

  pageLess() {
    if (this.pageIndex === 1 || !this.pageIndex) {
      return;
    }
    this.pageIndex = this.pageIndex - 1;
    this.getPageList();
  }

  pageAdd() {
    if (this.pageIndex === this.totalPage || !this.pageIndex) {
      return;
    }
    this.pageIndex = this.pageIndex + 1;
    this.getPageList();
  }

  // 确定
  determine() {
    this.filterVisible = false;
    this.pageIndex = 1;
    this.getPageList();
  }

  // 重置
  reset() {
    this.commandType = null;
    this.determine();
  }
}
