<div class="modal-header">
  <h5 class="modal-title" translate>自定义指令</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <nz-spin nzTip="Loading..." [nzSpinning]="saving">
    <div class="modal-search">
      <div class="modal-search-input">
        <ng-template #suffixIconSearch>
          <i nz-icon nzType="search" nzTheme="outline" class="pointer" (click)="search(true)"></i>
        </ng-template>
        <ng-template #inputClearTpl>
          <i *ngIf="commandName" nz-icon nzType="close" nzTheme="outline" (click)="search()"></i>
        </ng-template>
        <nz-input-group class="vehicle_search_group" [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl">
          <input
            type="text"
            [(ngModel)]="commandName"
            nz-input
            placeholder="{{ '自定义指令名称' | translate }}"
            (keyup.enter)="search(true)"
            (keyup.esc)="search()"
          />
        </nz-input-group>
        <button nz-button (click)="search()">
          <i nz-icon nzType="redo" nzTheme="outline"></i>
        </button>
      </div>
      <button nz-button nzType="primary" (click)="new()">
        {{ '新建' | translate }}
      </button>
    </div>
    <div class="modal-content modal-grid">
      <!-- 列表 -->
      <div class="command-table">
        <ngx-datatable
          #dt
          class="material"
          [scrollbarH]="true"
          [saveState]="false"
          [rows]="commandList"
          [loadingIndicator]="loading"
          appNgxDataTable
          #scroller
          [limit]="10"
          ngxNoPageFooterWatcher
          [footer]="footer"
          [count]="currentNumber"
          style="width: 100%"
          [columnMode]="'force'"
          (activate)="selected($event)"
          [rowClass]="rowSelect"
        >
          <ngx-datatable-column
            name="{{ '自定义指令名称' | translate }}"
            [width]="260"
            prop="commandName"
            headerClass="text-left"
            cellClass="text-left"
          ></ngx-datatable-column>
          <ngx-datatable-column
            [width]="100"
            name="{{ '操作' | translate }}"
            prop="total"
            headerClass="text-center"
            cellClass="text-center"
          >
            <ng-template let-row="row" ngx-datatable-cell-template>
              <span class="margin_right" (click)="$event.stopPropagation(); edit(row)">
                <a>
                  <img src="/assets/font/edit.png" nz-tooltip nzTooltipTitle="{{ '编辑' | translate }}" />
                </a>
              </span>
              <span class="margin_right"><span>|</span></span>

              <span class="margin_right" (click)="$event.stopPropagation(); delete(row)">
                <a>
                  <img src="/assets/font/delete.png" nz-tooltip nzTooltipTitle="{{ '删除' | translate }}" />
                </a>
              </span>
            </ng-template>
          </ngx-datatable-column>
        </ngx-datatable>
        <br />
        <div class="footer-style">
          <nopage-datatable-footer
            #footer
            [currentNumber]="currentNumber"
            [totalNumber]="totalNumber"
            (nextPage)="nextPage($event)"
            (prePage)="nextPage($event)"
          ></nopage-datatable-footer>
        </div>
      </div>
      <!-- 指令 -->
      <div class="command-info">
        <div class="command-info-content">
          <div class="command-none" *ngIf="!typeCommand; else info"></div>
          <ng-template #info>
            <div class="info">
              <span class="title">{{ typeCommand + '指令' | translate }}</span>
              <form nz-form [formGroup]="form" (ngSubmit)="submit()">
                <nz-form-item>
                  <nz-form-label nzFor="commandName" [nzSm]="7" [nzXs]="24">
                    {{ '自定义指令名称' | translate }}
                  </nz-form-label>
                  <nz-form-control [nzSm]="17" [nzXs]="24" [nzErrorTip]="commandNameErrorTpl">
                    <input
                      maxlength="30"
                      type="text"
                      nz-input
                      formControlName="commandName"
                      placeholder="{{ '请输入指令名称' | translate }}"
                    />
                    <ng-template #commandNameErrorTpl let-control>
                      {{ '请输入指令名称' | translate }}
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label nzFor="commandContent" [nzSm]="7" [nzXs]="24">
                    {{ '自定义指令命令' | translate }}
                  </nz-form-label>
                  <nz-form-control [nzSm]="17" [nzXs]="24" [nzErrorTip]="commandContentErrorTpl">
                    <textarea
                      formControlName="commandContent"
                      nz-input
                      rows="9"
                      maxlength="500"
                      placeholder="{{ '请输入指令命令' | translate }}"
                    ></textarea>
                    <span class="code-span">{{ this.form.get('commandContent').value?.length || 0 }}/500</span>
                    <ng-template #commandContentErrorTpl let-control>
                      {{ '请输入指令命令' | translate }}
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </form>
            </div>
          </ng-template>
        </div>
        <div class="command-info-btn">
          <button
            *ngIf="typeCommand === '新增' || typeCommand === '编辑'"
            type="button"
            nz-button
            (click)="activeModal.hide()"
          >
            {{ '取消' | translate }}
          </button>
          <button
            *ngIf="typeCommand === '新增' || typeCommand === '编辑'"
            type="button"
            nz-button
            nzType="primary"
            (click)="submit()"
          >
            {{ '保存' | translate }}
          </button>
        </div>
      </div>
    </div>
  </nz-spin>
</div>
