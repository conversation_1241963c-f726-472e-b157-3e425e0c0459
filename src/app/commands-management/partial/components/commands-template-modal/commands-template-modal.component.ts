import { AfterViewInit, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { CommandsManagementService } from '@app/commands-management/shared/commands-management.service';
import { Di<PERSON><PERSON>, Logger, LoggerFactory } from '@app/core';
import { NgxDataTableDirective } from '@app/shared';

import { TranslateService } from '@ngx-translate/core';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-commands-template-modal',
  templateUrl: './commands-template-modal.component.html',
  styleUrls: ['./commands-template-modal.component.scss']
})
export class CommandsTemplateModalComponent implements OnInit {
  public log: Logger;

  loading = false;
  commandName: string;
  commandList: any = [];
  currentNumber = 0;
  totalNumber = 0;
  pageList: any;

  typeCommand: '新增' | '编辑' | '查看' = null;
  selectCommand: any;
  saving = false;
  form: FormGroup;
  isReset = false;
  rowSelect = (row: any) => {
    return {
      'select-command': row.commandName === this.form.get('commandName').value
    };
  };

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  constructor(
    private loggerFactory: LoggerFactory,
    private dialogs: Dialogs,
    private changeDetectorRef: ChangeDetectorRef,
    private translate: TranslateService,
    private formBuilder: FormBuilder,
    public activeModal: BsModalRef,
    private commandsManagementService: CommandsManagementService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {
    this.loadList();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  loadList() {
    this.loading = true;
    // this.footer.showTotalElements = true;
    this.commandsManagementService
      .commandPage({ commandName: this.commandName })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          this.pageList = response.data;
          this.totalNumber = this.pageList.length;
          this.nextPage({ pageIndex: 1, isReset: true });
        },
        (error) => console.log('自定义列表获取设备', error)
      );
  }

  nextPage(e: any) {
    if (this.isReset) {
      e.pageIndex = e.pageIndex === 3 ? 2 : e.pageIndex;
    }
    this.isReset = e.isReset;
    this.commandList = [...this.pageList].slice((e.pageIndex - 1) * 10);
    if (e.pageIndex * 10 === this.totalNumber) {
      this.currentNumber = 9;
      return;
    }
    this.currentNumber = this.commandList.length > 10 ? 10 : this.commandList.length;
  }

  search(isQuery?: boolean) {
    if (!isQuery) {
      this.commandName = null;
    }
    this.loadList();
  }

  new() {
    this.typeCommand = '新增';
    this.patchValue({
      commandName: null,
      commandContent: null
    });
  }

  selected(e: any) {
    if (e.type === 'click') {
      this.typeCommand = '查看';
      this.patchValue(e.row);
    }
  }

  edit(e: any) {
    this.typeCommand = '编辑';
    this.selectCommand = e;
    this.patchValue(e);
  }

  patchValue(e: any) {
    this.form.patchValue(e);
    const name = this.form.get('commandName');
    const code = this.form.get('commandContent');
    if (this.typeCommand === '查看') {
      name.disable();
      code.disable();
    } else {
      name.enable();
      code.enable();
    }
  }

  delete(e: any) {
    this.translate.get(`请确认是否删除，删除后该指令不能再被应用。`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.commandsManagementService.deleteCommand(e.id).subscribe(
            (result) => {
              if (result.success) {
                this.translate.get(`删除成功`).subscribe((res) => {
                  this.log.success(res);
                  this.init();
                });
              } else {
                this.log.error(result.message);
              }
              // this.ngxQuery.executeQuery();
            },
            (error) => {
              console.log(`删除失败，失败信息：`, error);
            }
          );
        },
        () => console.log(`取消删除列表 ${e.deviceNo}`)
      );
    });
  }

  submit() {
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (this.form.invalid) return;
    // loading
    this.saving = true;
    this.typeCommand === '新增' ? this.addCommand() : this.editCommand();
  }

  addCommand() {
    this.commandsManagementService
      .addCommand(this.form.value)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (res.success) {
            this.translate.get(`新增成功`).subscribe((res) => {
              this.log.success(res);
            });
            this.init();
          } else {
            this.translate.get(res.message).subscribe((data) => {
              this.log.success(data);
            });
            // this.log.error(res.message);
          }
        },
        (err) => console.log(err.message)
      );
  }

  editCommand() {
    this.commandsManagementService
      .editCommand({ ...this.form.value, id: this.selectCommand.id })
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (res.success) {
            this.translate.get(`更新成功`).subscribe((res) => {
              this.log.success(res);
            });
            this.init();
          } else {
            this.translate.get(res.message).subscribe((data) => {
              this.log.success(data);
            });
            // this.log.error(res.message);
          }
        },
        (err) => console.log(err.message)
      );
  }

  init() {
    this.typeCommand = null;
    this.form.reset();
    this.search();
  }

  buildForm() {
    this.form = this.formBuilder.group({
      commandName: [null, [Validators.required]],
      commandType: [2],
      commandContent: [null, [Validators.required]]
    });
  }
}
