import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommandsTemplateModalComponent } from './commands-template-modal.component';

describe('CommandsTemplateModalComponent', () => {
  let component: CommandsTemplateModalComponent;
  let fixture: ComponentFixture<CommandsTemplateModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CommandsTemplateModalComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommandsTemplateModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
