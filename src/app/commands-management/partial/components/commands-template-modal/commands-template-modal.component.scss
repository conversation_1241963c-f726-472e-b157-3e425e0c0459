.modal-body {
  margin: 0 !important;
  padding: 0 !important;
}

.modal-grid {
  display: grid;
  grid-template-columns: 447px auto;
}

.modal-search {
  height: 87px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 8px solid #f4f7fb;
}

.modal-content {
  padding: 26px 41px 26px 23px;

  .command-table {
    padding-right: 50px;
  }

  .command-info {
    display: flex;
    flex-direction: column;

    &-content {
      flex: 1;
      border-left: 1px solid #e9edf4;

      .command-none {
        background-image: url(/assets/font/noDate.png);
        background-position: center;
        background-repeat: no-repeat;
        width: 224px;
        margin: auto;
        height: 100%;
      }
    }

    &-btn {
      height: 36px;
    }
  }
}

// 搜索框
.modal-search-input {
  display: flex;
  align-items: center;

  .vehicle_search_group {
    width: 283px;
    margin-right: 16px;
  }

  button {
    padding: 0;
    font-size: 20px;
    width: 38px;
  }
}

// 指令编辑与查看
.info {
  padding-left: 19px;

  .title {
    opacity: 1;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    padding-bottom: 8px;
    display: inline-block;
    color: #2f3747;
  }

  .ant-form-item-label {
    text-align: left !important;
    color: #575e72;
  }

  textarea.ant-input {
    padding-bottom: 15px;
  }

  .code-span {
    position: absolute;
    bottom: -16px;
    right: 6px;
    font-size: 12px;
  }
}

.command-info-btn {
  text-align: right;
}

:host ::ng-deep {
  .ngx-datatable.material .datatable-body {
    max-height: 310px !important;
    overflow-x: hidden !important;
  }

  .container-fluid {
    .row {
      justify-content: end !important;
    }

    .col-6 {
      flex: 0 0 25% !important;
    }

    .dropmenu {
      display: none;
    }
  }

  // 选中行样式
  .select-command .datatable-row-center {
    background: #ecf3ff !important;
  }
}

::ng-deep {
  .modal-command {
    padding-top: 75px;
    max-width: 933px !important;
  }
}
