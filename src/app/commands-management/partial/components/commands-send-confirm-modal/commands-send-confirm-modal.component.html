<div class="modal-header">
  <h5 class="modal-title">&nbsp;</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div nz-row [nzGutter]="[0, 16]">
    <div nz-col nzSpan="24" class="t-center">
      <strong>{{ '请确认是否发送命令' | translate }}？</strong>
    </div>

    <div nz-col nzSpan="24">
      <div nz-row [nzGutter]="[8, 0]">
        <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '车牌号' | translate }}:</div>
        <div nz-col nzFlex="auto">
          {{ data.plateNumber }}
        </div>
      </div>
    </div>
    <div nz-col nzSpan="24">
      <div nz-row [nzGutter]="[8, 0]">
        <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '设备号' | translate }}:</div>
        <div nz-col nzFlex="auto">
          {{ data.deviceNo }}
        </div>
      </div>
    </div>
    <div nz-col nzSpan="24">
      <div nz-row [nzGutter]="[8, 0]">
        <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '指令类型' | translate }}:</div>
        <div nz-col nzFlex="auto" translate>
          {{ type.commandName }}
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="btnShow" class="error" translate>{{ errMsg }}</div>
</div>

<div class="modal-footer">
  <ng-container *ngIf="!btnShow">
    <button type="button" nz-button (click)="activeModal.hide()">
      {{ '取消' | translate }}
    </button>
    <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="confirm()">
      {{ '确认' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="btnShow">
    <button type="button" nz-button nzType="primary" (click)="activeModal.hide()">
      {{ '确定' | translate }}
    </button>
  </ng-container>
</div>
