import { finalize } from 'rxjs/operators';
import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { CommandsManagementService } from '../../../shared/commands-management.service';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '@env/environment';

@Component({
  selector: 'app-commands-send-confirm-modal',
  templateUrl: './commands-send-confirm-modal.component.html',
  styleUrls: ['./commands-send-confirm-modal.component.scss']
})
export class CommandsSendConfirmModalComponent implements OnInit {
  @Output() save = new EventEmitter<any>();
  @Input() data: any;
  @Input() type: any;
  @Output() action = new EventEmitter();

  listOfOption: any[];
  // 组织机构
  nodes: Array<any> = [];
  title = '新建指令';
  device: any;

  public log: Logger;
  saving = false;
  btnShow: boolean = false;
  errMsg: string;

  constructor(
    public activeModal: BsModalRef,
    private deviceService: CommandsManagementService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {}

  confirm() {
    if (this.saving === true) {
      return;
    }
    this.saving = true;
    let type: number;
    //发送指令类型(1 断油断电；2 恢复油电 3自定义指令 4 解除指令
    if (this.type.commandName === 'Restore') {
      type = 2;
    } else if (this.type.commandName === 'FCO') {
      type = 1;
    } else if (this.type.commandName === environment.commandType.releaseAlarm) {
      type = 4;
    } else {
      type = 3;
    }
    // const type = this.type.commandName === 'FCO' ? 1 : this.type.commandName === 'Restore' ? 2 : 3; //发送指令类型(1 断油断电；2 恢复油电 3自定义指令
    const params = {
      imei: this.data.deviceNo || '',
      channel: 'Web command',
      devtype: this.data.typeName || '', //设备型号
      cmdno: '1234', //流水号
      type: type,
      commandId: this.type.id,
      commandName: this.type.commandName
    };
    this.deviceService.commandJudgment(params).subscribe((respone) => {
      if (respone.success) {
        type === 3 ? this.userCommandSend() : this.commandSend(params);
      } else {
        this.errMsg = respone.message;
        this.btnShow = true;
        this.saving = false;
      }
    });
  }

  // 发送指令
  commandSend(params: any) {
    params.deviceStatus = '';
    this.deviceService
      .commandSend(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (respone) => {
          this.activeModal.hide();
          if (respone.success) {
            this.translate.get('指令发送成功!').subscribe((res: string) => {
              this.log.success(res);
            });
            this.action.emit('success');
          } else {
            this.translate.get('指令发送失败!').subscribe((res: string) => {
              this.log.error(res);
            });
          }
        },
        (error) => console.log(`请求失败信息: ${error}`)
      );
  }

  // 自定义指令
  userCommandSend() {
    const params = {
      imei: this.data.deviceNo,
      value: this.type.commandContent,
      commandId: this.type.id,
      commandName: this.type.commandName
    };
    this.deviceService
      .usrCommandSend(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (respone) => {
          this.activeModal.hide();
          if (respone.success) {
            this.translate.get('指令发送成功!').subscribe((res: string) => {
              this.log.success(res);
            });
            this.action.emit('success');
          } else {
            this.translate.get('指令发送失败!').subscribe((res: string) => {
              this.log.error(res);
            });
          }
        },
        (error) => console.log(`请求失败信息: ${error}`)
      );
  }
}
