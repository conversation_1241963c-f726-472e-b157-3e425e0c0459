import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommandsSendConfirmModalComponent } from './commands-send-confirm-modal.component';

describe('CommandsSendConfirmModalComponent', () => {
  let component: CommandsSendConfirmModalComponent;
  let fixture: ComponentFixture<CommandsSendConfirmModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CommandsSendConfirmModalComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommandsSendConfirmModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
