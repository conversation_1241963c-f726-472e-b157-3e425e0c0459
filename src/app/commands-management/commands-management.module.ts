import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { MapModule } from '@app/map/map.module';
import { CommandsManagementRoutingModule } from './commands-management-routing.module';

import { CommandsMainComponent } from './partial/commands-main/commands-main.component';
import { CommandsCreateComponent } from './partial/commands-create/commands-create.component';

import { CommandsCardComponent } from './partial/components/commands-card/commands-card.component';
import { CommandsTemplateModalComponent } from './partial/components/commands-template-modal/commands-template-modal.component';
import { CommandsSendConfirmModalComponent } from './partial/components/commands-send-confirm-modal/commands-send-confirm-modal.component';

@NgModule({
  declarations: [
    CommandsMainComponent,
    CommandsCardComponent,
    CommandsCreateComponent,
    CommandsTemplateModalComponent,
    CommandsSendConfirmModalComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    CommandsManagementRoutingModule,
    MapModule
  ],
  entryComponents: [CommandsSendConfirmModalComponent, CommandsTemplateModalComponent],
  exports: [CommonModule]
})
export class CommandsManagementModule {}
