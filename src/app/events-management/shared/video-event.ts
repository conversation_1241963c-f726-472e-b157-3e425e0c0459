import { NzNotificationRefData } from '@app/shared/models/video';

export interface VideoEventParams {
  organizationId: string;
  unitname: string;
  /** 设备号deviceNumber */
  imei: string;
  types: number[];
  // yyyy-MM-dd HH:mm:ss
  startTime: string;
  endTime: string;
}

export interface VideoEvent {
  id: string;
  date: string;
  imei: string;
  organization: string;
  unitName: string;
  urls: string[];
  videoEvents: string;
  uid?: string;
  isDownloading?: boolean;
  progress?: number;
  nzNotificationRefData?: NzNotificationRefData;
}
