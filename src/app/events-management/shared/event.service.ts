import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { WebApiResultResponse, PagingResponse } from '../../core/http/web-api-result-response';
import { QueryPageList, Res, PageList } from '@app/shared/models/type';

import { VideoEvent, VideoEventParams } from './video-event';

@Injectable({
  providedIn: 'root'
})
export class EventService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');

  constructor(private http: HttpClient) {
    super();
  }

  // 获取预警列表
  getEarlyWarningList(params: QueryPageList): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/alert/detail';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取预警列表总数
  getEarlyTotalNumber(params: any): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/alert/detail-total';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取历史预警列表
  getHistoryWarningList(params: QueryPageList): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/alert-history/alert/detail';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 围栏预警记录查询FenceId
  getHistoryFenceWarningListByFenceId(params: QueryPageList): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/fence/history/detail';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取历史预警列表总数
  getHistoryTotalNumber(params: any): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/alert-history/alert/detail-total';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取围栏历史预警列表总数FenceId
  getHistoryWarnTotalNumberByFenceId(params: any): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/fence/history/detailTotal';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 预警导出
  earlyExport(params: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = 'glcrm-alert-api/v1/api/alert/export';
    return this.http.post(url, params, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }
  // 历史预警导出
  historyExport(params: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = 'glcrm-alert-api/v1/api/alert-history/export';
    return this.http.post(url, params, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }
  // 围栏历史预警导出 FenceId
  historyFenceExportByFenceId(params: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = 'glcrm-alert-api/v1/api/fence/history/export';
    return this.http.post(url, params, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }

  // 获取围栏预警列表
  getFenceWarningList(params: QueryPageList): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/fence/detail';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取围栏预警列表总数
  getFenceWarningTotalNumber(params: any): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/fence/detailTotal';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 围栏预警导出
  fenceWarningExport(params: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = 'glcrm-alert-api/v1/api/fence/export';
    return this.http.post(url, params, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }

  /**
   * 获取视频事件分页列表
   * @param params
   * @returns
   */
  getVideoEventPageList(params: VideoEventParams) {
    const url = 'glcrm-alert-api/v1/api/alert/alertListInfo';
    return this.http.post<Res<PageList<VideoEvent>>>(url, params);
  }

  /**
   * 获取视频事件总数
   * @param params
   * @returns
   */
  getVideoEventPageTotal(params: VideoEventParams) {
    const url = 'glcrm-alert-api/v1/api/alert/alertListInfo/detail-total';
    return this.http.post<Res<number>>(url, params);
  }

  // 事件列表
  getEventList(params: QueryPageList): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/event/list';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  getEventType(): Observable<any> {
    const url = 'glcrm-alert-api/v1/api/event/drop';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 硬件预警/基础事件/视频事件:告警类型下拉列表
   * @param type 1:硬件预警; 2:基础事件,4:视频事件
   * @returns Observable<any>
   */
  getBasicAlarmTypeList(type: number = 1): Observable<any> {
    const url = `glcrm-alert-api/v1/api/alert/alertType?type=${type}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
