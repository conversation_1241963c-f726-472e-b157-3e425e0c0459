<div class="m-portlet list_header">
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [nzAsyncData]="true"
            [nzNotFoundContent]="noData"
            (nzExpandChange)="expandChange($event)"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
          <ng-template #noData>
            <div *ngIf="treeLoading" class="no-data">
              <nz-spin nzSimple></nz-spin>
            </div>
            <div *ngIf="!treeLoading" class="no-data">
              {{ '暂无数据' | translate }}
            </div>
          </ng-template>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'fenceName'" label="{{ '围栏名称' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入围栏名称' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'vehicleOwner'" label="{{ '车主名' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车主名' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'vehicleNo'" label="{{ '车牌号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车牌号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'deviceNo'" label="{{ '设备号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入设备号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field
        [name]="'fenceAlertType'"
        label="{{ '预警类型' | translate }}"
        [type]="'string'"
        [custom]="warningTypeList"
      >
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择预警类型' | translate }}"
          >
            <nz-option nzLabel="{{ '全部' | translate }}" [nzValue]="undefined"></nz-option>
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'startTimeStamp'" label="{{ '预警日期' | translate }}" [type]="'date'">
        <ng-template
          ngx-query-value-input-template
          dataType="date"
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <nz-range-picker
            [nzFormat]="accountSetting.dateFormat"
            class="w-full"
            [nzAllowClear]="false"
            [(ngModel)]="rule.datas[dataIndex]"
            appLocalNzDateTime
          ></nz-range-picker>
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-caption" [appApplyPermission]="'enclosure_export'">
      <div class="m-portlet__head-tools">
        <ul class="m-portlet__nav">
          <li class="m-portlet__nav-item">
            <button nz-button nzType="primary" (click)="export()">
              {{ '导出' | translate }}
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="m-portlet__body p-0">
    <ngx-datatable
      appNgxDataTable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="earlyWarningList"
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="loadEarlyWarning($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [columnMode]="'force'"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
    >
      <ngx-datatable-column
        [width]="160"
        name="{{ '围栏名称' | translate }}"
        prop="fenceName"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="160"
        name="{{ '车主名' | translate }}"
        prop="vehicleOwner"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="160"
        name="{{ '车牌号' | translate }}"
        prop="vehicleNo"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span style="width: 90%">
            <div class="address" [title]="row.vehicleNo">
              {{ row.vehicleNo }}
            </div>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="120"
        name="{{ '预警类型' | translate }}"
        prop="fenceAlertTypeName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span>{{ row.fenceAlertTypeName | translate }}</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="180"
        name="{{ '预警时间' | translate }}"
        prop="alertTime"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.alertTime | localDate : accountSetting.dateTimeFormat }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="500"
        name="{{ '预警地址' | translate }}"
        prop="address"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div style="width: 90%">
            <div class="address" title="{{ row.address }}">{{ row.address }}</div>
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="100"
        name="{{ '时长' | translate }}"
        prop="duration"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.duration }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="180"
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="250"
        name="{{ '设备号' | translate }}"
        prop="deviceNo"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column [width]="50" [frozenRight]="true" headerClass="datatable-header-cell-acitons text-left">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
