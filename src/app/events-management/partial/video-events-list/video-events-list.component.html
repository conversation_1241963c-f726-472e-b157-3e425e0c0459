<div class="m-portlet list_header">
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
    >
      <ngx-query-field [name]="'organizationId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="organNodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [nzAsyncData]="true"
            [nzNotFoundContent]="noData"
            (nzExpandChange)="expandChange($event)"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
          <ng-template #noData>
            <div *ngIf="treeLoading" class="no-data">
              <nz-spin nzSimple></nz-spin>
            </div>
            <div *ngIf="!treeLoading" class="no-data">
              {{ '暂无数据' | translate }}
            </div>
          </ng-template>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'unitName'" label="{{ '车牌号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车牌号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'imei'" label="{{ '设备号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入设备号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'types'" label="{{ '视频事件' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择视频事件' | translate }}"
            nzShowSearch
            (nzOpenChange)="translateWarning($event)"
          >
            <nz-option nzLabel="{{ '全部' | translate }}" [nzValue]="undefined"></nz-option>
            <nz-option
              *ngFor="let item of videoEventList"
              nzLabel="{{ item.typeName | translate }}"
              [nzValue]="item.id"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'date'" label="{{ '日期' | translate }}" [type]="'date'">
        <ng-template
          ngx-query-value-input-template
          dataType="date"
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <nz-range-picker
            class="w-full"
            nzDropdownClassName="disable-date-range-input"
            [nzAllowClear]="false"
            [nzShowTime]="{ nzFormat: accountSetting.timeFormat }"
            [nzFormat]="accountSetting.dateTimeFormat"
            [(ngModel)]="rule.datas[dataIndex]"
            (nzOnOpenChange)="toggleDateRangeModal($event, rule.datas[dataIndex])"
            appLocalNzDateTime
          ></nz-range-picker>
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>

<div class="m-portlet">
  <div class="m-portlet__body p-0">
    <ngx-datatable
      appNgxDataTable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="pageList"
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="getPageList($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [columnMode]="'force'"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
    >
      <ngx-datatable-column
        [width]="150"
        name="{{ '车牌号' | translate }}"
        prop="unitName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span style="width: 90%">
            <div class="address" [title]="row.unitName">
              {{ row.unitName }}
            </div>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="180"
        name="{{ '视频事件' | translate }}"
        prop="videoEvents"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span>
            {{ row.videoEvents | translate }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="180"
        name="{{ '日期' | translate }}"
        prop="date"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.date | localDate : accountSetting.dateTimeFormat }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="130"
        name="{{ '时速' | translate }}(km/h)"
        prop="speed"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '所属机构' | translate }}"
        prop="organization"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="250"
        name="{{ '设备号' | translate }}"
        prop="imei"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '操作' | translate }}"
        headerClass="text-left"
        cellClass="text-left"
        [frozenRight]="true"
        [width]="100"
        [minWidth]="100"
        [maxWidth]="100"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span
            (click)="openVideoModal(row)"
            nz-tooltip
            nzTooltipPlacement="top"
            nzTooltipTitle="{{ '查看视频' | translate }}"
            appApplyPermission="viewVideo"
            class="margin_right"
          >
            <a><img src="/assets/media/app/img/video/event_play.svg" /></a>
          </span>

          <span appApplyPermission="viewVideo" class="margin_right">|</span>

          <span
            *ngIf="!row.isDownloading"
            appApplyPermission="downloadVideo"
            (click)="download(row)"
            nz-tooltip
            nzTooltipPlacement="top"
            nzTooltipTitle="{{ '下载' | translate }}"
          >
            <a><img src="/assets/media/app/img/video/download_active.svg" /></a>
          </span>
          <nz-progress
            *ngIf="row.isDownloading"
            [nzPercent]="row.progress"
            nzType="circle"
            [nzWidth]="20"
          ></nz-progress>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [width]="50"
        [minWidth]="50"
        [maxWidth]="50"
        [frozenRight]="true"
        headerClass="datatable-header-cell-acitons text-left"
      >
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        [checkTurnPage]="turnPage.bind(this)"
        (getTotal)="getTotal()"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
