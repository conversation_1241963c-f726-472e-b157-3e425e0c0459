import {
  Component,
  OnInit,
  After<PERSON>iew<PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { combineLatest, Observable, of, Subscription, timer } from 'rxjs';
import { finalize, switchMap, throttleTime } from 'rxjs/operators';

import { BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { format } from 'date-fns';
import { saveAs } from 'file-saver';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';

import { QueryTemplate } from '@app/shared/models/type';
import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { DownloadService, DownloadFile } from '@app/shared/services/download.service';
import { ZipService } from '@app/shared/services/zip.service';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { TranslateHelperService } from '@app/shared/services/translate-helper.service';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { VideoEventParams, VideoEvent } from '@app/events-management/shared/video-event';
import { EventService } from '@app/events-management/shared/event.service';
import { CacheHistoryVideoDownloadService } from '@app/video-management/shared/cache-history-video-download.service';
import { VideoEventsMediaModalComponent } from '../video-events-media-modal/video-events-media-modal.component';
import { LanguageMonitorService } from '@app/shared/services/language-monitor.service';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-video-events-list',
  templateUrl: './video-events-list.component.html',
  styleUrls: ['./video-events-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoEventsListComponent implements OnInit, AfterViewInit, OnDestroy {
  log: Logger;
  translates = {
    下载失败: '',
    下载成功: '',
    暂无数据: ''
  };

  loading = false;
  accountSetting = accountSetting;
  treeLoading = true;
  // 组织机构
  organNodes: Array<any> = [];
  videoEventList: any[] = [];

  pageList: Array<any> = [];
  currentNumber = 10;
  totalNumber = 0;

  event: any;

  langChange = new Subscription();
  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'organizationId', op: 'eq' },
          { field: 'unitName', op: 'cn' },
          { field: 'imei', op: 'cn' },
          { field: 'types', op: 'eq' },
          { field: 'date', op: 'cn' }
        ],
        groups: []
      }
    }
  ];
  mode: QueryMode = QueryMode.plainCollapse;
  params: VideoEventParams;

  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  constructor(
    private cacheHistoryVideoDownloadService: CacheHistoryVideoDownloadService<VideoEvent>,
    private cd: ChangeDetectorRef,
    private downloadService: DownloadService,
    private eventService: EventService,
    private LoggerFactory: LoggerFactory,
    private modalService: BsModalService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private translateHelperService: TranslateHelperService,
    private languageMonitorService: LanguageMonitorService,
    private zipService: ZipService,
    private accountService: AccountService
  ) {
    this.log = this.LoggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {
    this.getVideoEventTypes();
    // this.getUserAllOrganizations();
    this.getUserInfo();
    this.langChange = this.languageMonitorService.langChange$.subscribe(() => {
      // this.refreshData();
      this.translateWarning(true);
    });
  }

  ngAfterViewInit() {
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.langChange.unsubscribe();
  }

  refreshData() {
    this.getPageList(this.event);
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.organNodes = res;
      this.treeLoading = false;
    });
  }

  loadProperty(page: any): VideoEventParams {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'date' && rule.data) {
        map['startTime'] = rule.data[0].getTime();
        map['endTime'] = rule.data[1].getTime();
      } else if (rule.field === 'types' && rule.data) {
        map['types'] = [rule.data];
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  getPageList(event: any) {
    this.event = event;
    this.footer.showTotalElements = true;
    this.params = this.loadProperty(event.page);
    this.loading = true;
    this.eventService
      .getVideoEventPageList(this.params)
      .pipe(
        finalize(() => {
          this.currentNumber = this.pageList.length;
          this.loading = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          if (!res || !res.success || !res.data || !Array.isArray(res?.data?.records)) {
            this.pageList = [];
            this.log.error(res?.message || this.translate.instant('获取失败'));
            return;
          }
          this.pageList = res.data.records.map((item: VideoEvent) => {
            item.uid = `eventVideo-${item.unitName}-${item.imei}-${item.id}`;
            item.isDownloading = false;
            item.progress = 0;
            const progressItem = this.cacheHistoryVideoDownloadService.downloadVideoList.find(
              (nzNotificationRefData) => {
                if (nzNotificationRefData && nzNotificationRefData.data) {
                  const cacheItem = nzNotificationRefData.data as VideoEvent;
                  return cacheItem.uid === item.uid;
                }
                return false;
              }
            );
            if (progressItem) {
              item = progressItem.data as VideoEvent;
            }
            return item;
          });
        },
        (err) => {
          this.pageList = [];
          this.log.error(err || this.translate.instant('获取失败'));
        }
      );
  }

  getTotal() {
    this.eventService
      .getVideoEventPageTotal(this.params)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.code === '200') {
          this.totalNumber = res.data;
        }
      });
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  openVideoModal(videoEvent: VideoEvent) {
    const initialState = { videoEvent };
    this.modalService.show(VideoEventsMediaModalComponent, {
      initialState,
      class: 'modal-lg modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe(() => {
      onHidden.unsubscribe();
    });
  }

  download(item: VideoEvent) {
    console.log('232323111', item);
    if (!Array.isArray(item.urls)) {
      this.log.warn(this.translates['暂无数据']);
      return;
    }
    item.isDownloading = true;
    item.progress = 0;
    item.nzNotificationRefData = this.cacheHistoryVideoDownloadService.add(item);
    of(item.urls)
      .pipe(switchMap((urlList) => this.getDownloadProgress(urlList)))
      .subscribe((res: Array<DownloadFile>) => {
        if (!Array.isArray(res) || res.length < 1) {
          item.isDownloading = false;
          item.progress = 0;
          if (item.nzNotificationRefData) {
            this.cacheHistoryVideoDownloadService.remove(item.nzNotificationRefData);
          }
          this.log.error(this.translates['下载失败']);
          this.cd.markForCheck();
          return;
        }
        const isDownloaded = res.every((item) => item.type === 'Done');
        // ------- 下载完成 -------
        if (isDownloaded) {
          item.isDownloading = false;
          item.progress = 0;
          if (item.nzNotificationRefData) {
            this.cacheHistoryVideoDownloadService.remove(item.nzNotificationRefData);
          }
          this.cd.markForCheck();
          const isAllfail = res.every((progressItem) => !progressItem.data); // 所有下载都失败（其中一个成功算成功）
          if (isAllfail) {
            this.log.error(this.translates['下载失败']);
            return;
          }
          const files = res.filter((item) => item.data).map((item) => item.data);
          if (files.length === 1) {
            saveAs(files[0].file, files[0].fileName);
            this.log.success(this.translates['下载成功']);
            return;
          }
          this.zipService.zip(files).subscribe((blob) => {
            saveAs(blob, `${item.unitName}_${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}.zip`);
            this.log.success(this.translates['下载成功']);
          });
          return;
        }
        // -------- 下载进度 ---------
        // 计算多个文件总的下载进度
        let progress = 0;
        res.forEach((progressItem) => {
          progress += progressItem.progress;
        });
        item.progress = Math.floor(progress / res.length);
        this.cd.markForCheck();
      });
  }

  getDownloadProgress(urlList: string[]): Observable<Array<DownloadFile>> {
    if (urlList.length === 0) {
      return of([]);
    }
    const list$ = urlList.map((url) => {
      let lastIndex = url.lastIndexOf('/') + 1;
      return this.downloadService.getBlobFileByUrl(url, url.substring(lastIndex));
    });
    return combineLatest(list$).pipe(throttleTime(1000, undefined, { leading: true, trailing: true }));
  }

  toggleDateRangeModal(isOpen: boolean, dates: [Date, Date]) {
    if (!isOpen && dates) {
      const startDate = dates[0];
      const endDate = dates[1];
      // nz-date-range bug 开始时间会大于结束时间
      if (endDate.getTime() < startDate.getTime()) {
        timer(30)
          .pipe(finalize(() => this.cd.markForCheck()))
          .subscribe(() => {
            dates = [endDate, startDate];
          });
      }
    }
    // modal弹窗是异步
    timer(300).subscribe(() => {
      document
        .querySelectorAll('.disable-date-range-input input.ant-calendar-input')
        .forEach((item) => item.setAttribute('disabled', 'disabled'));
    });
  }

  translateWarning(event: boolean) {
    if (event) {
      this.getVideoEventTypes();
    }
  }

  /**
   * 获取视频事件类型列表
   */
  getVideoEventTypes() {
    this.videoEventList = [];
    this.eventService
      .getBasicAlarmTypeList(4)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (!res.success) {
          return;
        }
        res.data = Array.isArray(res.data) ? res.data : [];
        this.translateHelperService.translateList(res.data, 'typeName');
        this.videoEventList = res.data;
      });
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService
        .getUserChildrenOrganizations(id)
        .pipe(finalize(() => this.cd.markForCheck()))
        .subscribe((res) => {
          if (organizationId) {
            // 超管获取子级
            resolve(res[0].children);
          } else {
            // 非超管直接获取子级组织机构树
            this.organNodes = res;
          }
          this.treeLoading = false;
        });
    });
  }
}
