import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetector<PERSON>ef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { startOfDay, addDays } from 'date-fns';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate } from '@app/shared/models/type';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { TranslateHelperService } from '@app/shared/services/translate-helper.service';
import { EventService } from '@app/events-management/shared/event.service';
import { DeviceEventsHistoryComponent } from '../device-events-history/device-events-history.component';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { LanguageMonitorService } from '@app/shared/services/language-monitor.service';
import { Subscription } from 'rxjs';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';

declare const $: any;

@Component({
  selector: 'app-device-events-list',
  templateUrl: './device-events-list.component.html',
  styleUrls: ['./device-events-list.component.scss']
})
export class DeviceEventsListComponent implements OnInit, AfterViewInit, OnDestroy {
  accountSetting = accountSetting;
  time: Date | null = null;
  rangeDate: any = null;

  log: Logger;
  loading = false;

  // 组织机构
  organizationValue: string;
  nodes: Array<any> = [];

  title: string = '预警管理列表';
  earlyWarningList: Array<any>;
  selectedList: Array<any> = [];
  visibleColumns: string[] = [];
  notHideColumns: string[] = [];
  isShowCollapse = false;

  warningTypeList: Array<any> = [];

  currentNumber = 10;
  totalNumber: number;

  langChange = new Subscription();
  // isShowActions = true;
  treeLoading = true;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'vehicleOwner', op: 'cn' },
          { field: 'vehicleNo', op: 'cn' },
          { field: 'vin', op: 'cn' },
          { field: 'deviceNo', op: 'cn' },
          { field: 'alertType', op: 'eq' },
          { field: 'startTimeStamp', op: 'cn' }
        ],
        groups: []
      }
    }
  ];

  mode: QueryMode = QueryMode.plainCollapse;
  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  public datatable: any;
  event: any;

  constructor(
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private translateHelperService: TranslateHelperService,
    private referenceDataService: ReferenceDataService,
    private eventService: EventService,
    private languageMonitorService: LanguageMonitorService,
    private changeDetectorRef: ChangeDetectorRef,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    // this.getUserAllOrganizations();
    this.getUserInfo();
    this.getBasicAlarmTypeList();
    this.langChange = this.languageMonitorService.langChange$.subscribe(() => {
      this.refreshData();
      // this.table = this.table.;
      // this.isShowActions = false;
      // this.table = this.table;
      // this.isShowActions = true;
    });
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  ngOnDestroy() {
    this.langChange.unsubscribe();
  }

  refreshData() {
    this.loadEarlyWarning(this.event);
  }

  // 解决Edge浏览器下，ngx-datatable组件header处有'null'空值的现象
  removeHeaderNull() {
    $('.datatable-header-cell-label').each(function () {
      if ($(this).text() === 'null') {
        $(this).remove();
      }
    });
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
        this.treeLoading = false;
      });
    });
  }

  onSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selectedList = event.selected;
    }
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'startTimeStamp' && rule.data) {
        map['startTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['endTimeStamp'] = addDays(startOfDay(rule.data[1]), 1).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  loadEarlyWarning(event: any) {
    this.event = event;
    this.footer.showTotalElements = true;
    this.datatable = event.datatable;
    this.loading = true;
    this.selectedList.length = 0;
    const params = this.loadProperty(event.page);
    this.eventService
      .getEarlyWarningList(params)
      .pipe(
        finalize(() => {
          this.currentNumber = this.earlyWarningList.length;
          this.loading = false;
        })
      )
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.earlyWarningList = [];
            this.log.error(res?.message || this.translate.instant('获取失败'));
            return;
          }
          res.data = res?.data || [];
          this.earlyWarningList = res.data.map((item: any) => {
            if (typeof item.lng === 'number' && typeof item.lat === 'number') {
              item.address = `${item.address}(${item.lng},${item.lat})`;
            }
            return item;
          });
        },
        (err) => {
          this.earlyWarningList = [];
          this.log.error(err || this.translate.instant('获取失败'));
        }
      );
  }

  getTotal() {
    const params = this.loadProperty(this.event.page);
    this.eventService
      .getEarlyTotalNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response.code === '200') {
          this.totalNumber = response.data;
        }
      });
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  historyRecordModal(row: any) {
    const initialState = { data: row };
    this.modalService.show(DeviceEventsHistoryComponent, {
      initialState,
      class: 'modal-lg modal-lg-custom'
    });
  }

  export() {
    const params = this.loadProperty(this.event.page);
    this.eventService
      .earlyExport(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.ok) {
          const link = document.createElement('a');
          const blob = new Blob([response.body], { type: 'application/zip' });
          link.setAttribute('href', window.URL.createObjectURL(blob));
          this.translate.get('最新硬件预警记录').subscribe((res) => {
            link.setAttribute('download', res + '-' + new Date().getTime() + '.xlsx');
          });

          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.refreshData();
        }
      });
  }
  // 折叠展开工具栏
  showCollapse() {
    this.isShowCollapse = !this.isShowCollapse;
    this.ngxQuery.showCollapse(this.isShowCollapse);
  }

  translateWarning(event: boolean) {
    if (event) {
      this.getBasicAlarmTypeList();
    }
  }

  getBasicAlarmTypeList() {
    this.warningTypeList = [];
    this.eventService.getBasicAlarmTypeList().subscribe((res) => {
      if (!res.success) {
        return;
      }
      const list = res.data;
      this.translateHelperService.translateList(list, 'typeName');
      // this.warningTypeList = [...this.warningTypeList, ...list];
      this.warningTypeList = [...list];
    });
  }
}
