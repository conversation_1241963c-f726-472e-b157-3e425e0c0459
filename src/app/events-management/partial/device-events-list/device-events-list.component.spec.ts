import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DeviceEventsListComponent } from './device-events-list.component';

describe('DeviceEventsListComponent', () => {
  let component: DeviceEventsListComponent;
  let fixture: ComponentFixture<DeviceEventsListComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [DeviceEventsListComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DeviceEventsListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
