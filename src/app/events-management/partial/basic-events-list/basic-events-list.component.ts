import { Component, OnInit, After<PERSON>iew<PERSON>nit, ViewChild, ChangeDetector<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { startOfDay, addDays } from 'date-fns';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { QueryTemplate } from '@app/shared/models/type';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { EventService } from '@app/events-management/shared/event.service';
import { LanguageMonitorService } from '@app/shared/services/language-monitor.service';
import { Subscription } from 'rxjs';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';

declare const $: any;

@Component({
  selector: 'app-basic-events-list',
  templateUrl: './basic-events-list.component.html',
  styleUrls: ['./basic-events-list.component.scss']
})
export class BasicEventsListComponent implements OnInit, AfterViewInit, OnDestroy {
  log: Logger;
  loading = false;
  treeLoading = true;
  accountSetting = accountSetting;
  // 组织机构
  organizationValue: string;
  nodes: Array<any> = [];

  title: string = '事件列表';
  eventList: Array<any> = [];
  visibleColumns: string[] = [];
  notHideColumns: string[] = [];
  isShowCollapse = false;

  eventTypeList: any[] = [];

  currentNumber = 10;
  totalNumber: number;
  public datatable: any;
  event: any;

  langChange = new Subscription();

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'vehicleOwner', op: 'cn' },
          { field: 'vehicleNo', op: 'cn' },
          { field: 'vin', op: 'cn' },
          { field: 'deviceNo', op: 'cn' },
          { field: 'eventType', op: 'eq' },
          { field: 'startTimeStamp', op: 'cn' }
        ],
        groups: []
      }
    }
  ];

  mode: QueryMode = QueryMode.plainCollapse;
  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  constructor(
    private loggerFactory: LoggerFactory,
    private referenceDataService: ReferenceDataService,
    private eventService: EventService,
    private changeDetectorRef: ChangeDetectorRef,
    private languageMonitorService: LanguageMonitorService,
    private translate: TranslateService,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.getEventList();
    // this.getUserAllOrganizations();
    this.getUserInfo();
    this.langChange = this.languageMonitorService.langChange$.subscribe(() => {
      this.refreshData();
    });
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  ngOnDestroy() {
    this.langChange.unsubscribe();
  }

  // 解决Edge浏览器下，ngx-datatable组件header处有'null'空值的现象
  removeHeaderNull() {
    $('.datatable-header-cell-label').each(function () {
      if ($(this).text() === 'null') {
        $(this).remove();
      }
    });
  }

  // 事件分类
  getEventList() {
    this.eventService.getEventType().subscribe((res) => {
      if (res && res.success) {
        this.eventTypeList = res?.data || [];
      }
    });
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
        this.treeLoading = false;
      });
    });
  }

  refreshData() {
    this.loadEarlyWarning(this.event);
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'startTimeStamp' && rule.data) {
        map['startDate'] = startOfDay(rule.data[0]).getTime();
        map['endDate'] = addDays(startOfDay(rule.data[1]), 1).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  loadEarlyWarning(event: any) {
    this.event = event;
    this.footer.showTotalElements = true;
    const params = this.loadProperty(event.page);
    this.datatable = event.datatable;
    this.loading = true;
    this.eventService
      .getEventList(params)
      .pipe(
        finalize(() => {
          this.currentNumber = this.eventList.length;
          this.loading = false;
        })
      )
      .subscribe(
        (res) => {
          if (!res || !res.success || !res.data) {
            this.eventList = [];
            this.totalNumber = 0;
            this.log.error(res?.message || this.translate.instant('获取失败'));
            return;
          }
          res.data.records = res?.data?.records || [];
          this.eventList = res.data.records.map((item: any) => {
            if (typeof item.lng === 'number' && typeof item.lat === 'number') {
              item.address = `${item.address}(${item.lng},${item.lat})`;
            }
            return item;
          });
          this.totalNumber = res.data.total || 0;
        },
        (err) => {
          this.eventList = [];
          this.totalNumber = 0;
          this.log.error(err || this.translate.instant('获取失败'));
        }
      );
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  // export() {
  //   const params = this.loadProperty(this.event.page);
  //   params.language=window.localStorage.getItem('lang') === 'zh-CN' ? 'cn' : 'en';
  //   this.eventService
  //     .fenceWarningExport(params)
  //     .pipe(finalize(() => (this.loading = false)))
  //     .subscribe(
  //       (response: any) => {
  //         if (response.ok) {
  //           const link = document.createElement('a');
  //           const blob = new Blob([response.body], { type: 'application/zip' });
  //           link.setAttribute('href', window.URL.createObjectURL(blob));
  //           this.translate.get('事件记录').subscribe((res: string) => {
  //             link.setAttribute(
  //               'download',
  //               res + '-' + new Date().getTime() + '.xlsx'
  //             );
  //           });

  //           link.style.visibility = 'hidden';
  //           document.body.appendChild(link);
  //           link.click();
  //           document.body.removeChild(link);
  //           this.refreshData();
  //         } else {
  //           console.log('下载失败。', response.msg)
  //         }
  //       },
  //       (error) => {
  //         console.log('下载失败。', error)
  //       }
  //     );
  // }

  // 折叠展开工具栏
  showCollapse() {
    this.isShowCollapse = !this.isShowCollapse;
    this.ngxQuery.showCollapse(this.isShowCollapse);
  }
}
