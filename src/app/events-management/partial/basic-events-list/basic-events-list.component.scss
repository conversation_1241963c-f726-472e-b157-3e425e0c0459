.interval {
  display: grid;
  grid-template-columns: 80% 20%;
  width: 100%;
  padding-top: 15px;
}

:host ::ng-deep .float-right {
  // display: none !important;
  position: absolute;
  right: -20%;
  top: -50px;
}
.function-button {
  margin-top: 18px;
  margin-left: 30px;
}

.show-collapse {
  display: inline-block;
}
.unFold {
  margin-left: 10px;
  color: #3574fa;
  font-size: 14px;
  height: 22px;
  font-weight: 400;
  line-height: 22px;
}
.ngx-style {
  margin-bottom: 20px;
}

.m-dropdown__wrapper {
  top: 70px !important;
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  /* flex-direction: column; */
  height: 50px;
  position: static !important;
  /* width: 15%; */
  /* margin-left: 80%; */
  /* right: 0; */
  justify-content: flex-end;
}

:host ::ng-deep .col-6 {
  position: static;
}

.stick {
  width: 1px;
  height: 14px;
  background: #ced5e5;
  display: inline-block;
}

.enable-span {
  width: 28px;
  height: 22px;
  font-size: 14px;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #575e72;
  line-height: 22px;
}
.address {
  display: block;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.search-date {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  border: 1px solid #d3d6de;
  border-radius: 4px;
}
.query-input {
  width: 273px;
  height: 32px;
  background: #ffffff;
  border: none;
}
.date-logo {
  margin-right: 5px;
}
:host ::ng-deep .m-portlet__head-caption {
  display: flex !important;
  flex-direction: row-reverse;
  align-content: center;
  height: 100%;
}
::ng-deep.ant-calendar-picker-input {
  height: 38px;
}

.no-data {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
