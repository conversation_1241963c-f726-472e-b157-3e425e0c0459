import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DeviceEventsHistoryComponent } from './device-events-history.component';

describe('DeviceEventsHistoryComponent', () => {
  let component: DeviceEventsHistoryComponent;
  let fixture: ComponentFixture<DeviceEventsHistoryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [DeviceEventsHistoryComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DeviceEventsHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
