<div class="modal-header">
  <h5 class="modal-title" translate>历史预警</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="m-portlet header">
    <div class="interval">
      <ngx-query
        [hidden]="false"
        [datePickerReadonly]="false"
        [columnNumber]="2"
        class="full-screen no-header"
        #ngxQuery
        [queryTemplates]="queryTemplates"
        [showModeButtons]="true"
        [mode]="mode"
        [showPlainCollapseToolBar]="false"
      >
        <ngx-query-field [name]="'startEndTime'" [label]="''" [type]="'date'">
          <ng-template
            ngx-query-value-input-template
            dataType="date"
            let-rules="rules"
            let-rule="rule"
            let-dataIndex="dataIndex"
            let-placeholder="placeholder"
          >
            <nz-range-picker
              class="w-full"
              [nzFormat]="accountSetting.dateFormat"
              [nzAllowClear]="false"
              (ngModelChange)="changeDate()"
              [(ngModel)]="rule.datas[dataIndex]"
              appLocalNzDateTime
            ></nz-range-picker>
          </ng-template>
        </ngx-query-field>
        <ngx-query-field
          [name]="'alertType'"
          label="{{ '预警类型' | translate }}"
          [type]="'string'"
          [custom]="warningTypeList"
        >
          <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
            <nz-select
              class="w-full"
              [(ngModel)]="rule.datas[dataIndex]"
              nzPlaceHolder="{{ '请选择最新预警类型' | translate }}"
              nzShowSearch
              (ngModelChange)="ngxQuery.executeQuery()"
            >
              <nz-option nzLabel="{{ '全部' | translate }}" [nzValue]="undefined"></nz-option>
              <nz-option
                *ngFor="let item of warningTypeList"
                nzLabel="{{ item.typeName | translate }}"
                [nzValue]="item.id"
              ></nz-option>
            </nz-select>
          </ng-template>
        </ngx-query-field>
      </ngx-query>
    </div>

    <span style="margin-top: 20px">
      <button nz-button nzType="primary" (click)="export()">
        {{ '导出' | translate }}
      </button>
    </span>
  </div>
  <div class="m-portlet">
    <div class="body_header">
      <div>
        <span class="body_header_title" translate>车主名</span>
        <span class="body_header_text">{{ data.vehicleOwner }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>车牌号</span>
        <span class="body_header_text">{{ data.vehicleNo }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>车架号</span>
        <span class="body_header_text">{{ data.vin }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>设备号</span>
        <span class="body_header_text">{{ data.deviceNo }}</span>
      </div>
      <div>
        <span class="body_header_title" translate>设备型号</span>
        <span class="body_header_text">{{ data.deviceType }}</span>
      </div>
    </div>
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        appNgxDataTable
        (select)="onSelect($event)"
        [rows]="historyWarningList"
        [saveState]="false"
        [loadingIndicator]="loading"
        [selected]="selectedList"
        [selectionType]="'checkbox'"
        [ngxQuery]="ngxQuery"
        (loadValue)="loadHistoryWarning($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [columnMode]="'force'"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
      >
        <ngx-datatable-column
          [width]="80"
          name="{{ '预警类型' | translate }}"
          prop="alertTypeName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            {{ row.alertTypeName | translate }}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [width]="80"
          name="{{ '预警时间' | translate }}"
          prop="alertTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            {{ row.alertTime | localDate : accountSetting.dateTimeFormat }}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [width]="200"
          name="{{ '预警地址' | translate }}"
          prop="address"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div style="width: 90%">
              <div class="address" [title]="row.address">{{ row.address }}</div>
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [width]="130"
          name="{{ '时速' | translate }}(km/h)"
          prop="speed"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column maxWidth="50" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <img src="/assets/font/refresh_17.png" (click)="refreshData()" style="margin-top: 10px; cursor: pointer" />
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
