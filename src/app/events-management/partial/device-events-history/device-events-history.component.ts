import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';
import { timer } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { startOfDay, addDays } from 'date-fns';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { TranslateService } from '@ngx-translate/core';

import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { QueryTemplate } from '@app/shared/models/type';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { EventService } from '@app/events-management/shared/event.service';
import { TranslateHelperService } from '@app/shared/services/translate-helper.service';

declare const $: any;

@Component({
  selector: 'app-device-events-history',
  templateUrl: './device-events-history.component.html',
  styleUrls: ['./device-events-history.component.scss']
})
export class DeviceEventsHistoryComponent implements OnInit, AfterViewInit {
  @Input() data: any;

  log: Logger;
  loading = false;
  title: string = '历史预警列表';
  accountSetting = accountSetting;

  historyWarningList: Array<any>;
  selectedList: Array<any> = [];
  notHideColumns: string[] = [];
  isShowCollapse = false;
  currentNumber = 10;
  totalNumber: number;
  template: any;
  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'startEndTime', op: 'cn' },
          { field: 'alertType', op: 'cn' }
        ],
        groups: []
      }
    }
  ];

  warningTypeList: Array<any> = [];

  mode: QueryMode = QueryMode.plainCollapse;
  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  startEndTime: any;
  warningType: any;
  public datatable: any;
  event: any;

  constructor(
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private translateHelperService: TranslateHelperService,
    private eventService: EventService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.queryTemplates[0].template.rules[1].data = this.data && this.data.alertType ? this.data.alertType : '';
    this.getBasicAlarmTypeList();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  // 解决Edge浏览器下，ngx-datatable组件header处有'null'空值的现象
  removeHeaderNull() {
    $('.datatable-header-cell-label').each(function () {
      if ($(this).text() === 'null') {
        $(this).remove();
      }
    });
  }

  searchTime() {
    this.loadHistoryWarning(this.event);
  }

  onSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selectedList = event.selected;
    }
  }

  refreshData() {
    this.ngxQuery.resetQueryTemplate();
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'startEndTime' && rule.data) {
        map['startTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['endTimeStamp'] = addDays(startOfDay(rule.data[1]), 1).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.deviceNo = this.data.deviceNo;
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  changeDate() {
    timer().subscribe(() => this.ngxQuery.executeQuery());
  }

  loadHistoryWarning(event: any) {
    this.event = event;
    this.footer.showTotalElements = true;
    const params = this.loadProperty(event.page);
    this.datatable = event.datatable;
    this.loading = true;
    this.selectedList.length = 0;
    this.eventService
      .getHistoryWarningList(params)
      .pipe(
        finalize(() => {
          this.currentNumber = this.historyWarningList.length;
          this.loading = false;
        })
      )
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.historyWarningList = [];
            this.log.error(res?.message || this.translate.instant('获取失败'));
            return;
          }
          res.data = res?.data || [];
          this.historyWarningList = res.data.map((item: any) => {
            if (typeof item.lng === 'number' && typeof item.lat === 'number') {
              item.address = `${item.address}(${item.lng},${item.lat})`;
            }
            return item;
          });
        },
        (err) => {
          this.historyWarningList = [];
          this.log.error(err || this.translate.instant('获取失败'));
        }
      );
  }

  getTotal() {
    const params = this.loadProperty(this.event.page);
    this.eventService
      .getHistoryTotalNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response) => {
        if (response.code === '200') {
          this.totalNumber = response.data;
        }
      });
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  export() {
    const params = this.loadProperty(this.event.page);

    this.eventService
      .historyExport(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((response: any) => {
        if (response.ok) {
          const link = document.createElement('a');
          const blob = new Blob([response.body], { type: 'application/zip' });
          link.setAttribute('href', window.URL.createObjectURL(blob));
          this.translate.get('硬件预警历史记录').subscribe((res: string) => {
            link.setAttribute('download', res + '-' + new Date().getTime() + '.xlsx');
          });
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.refreshData();
        }
      });
  }

  getBasicAlarmTypeList() {
    this.eventService.getBasicAlarmTypeList().subscribe((res) => {
      if (!res.success) {
        return;
      }
      const list = res.data;
      this.translateHelperService.translateList(list, 'typeName');
      this.warningTypeList = [...this.warningTypeList, ...list];
    });
  }
}
