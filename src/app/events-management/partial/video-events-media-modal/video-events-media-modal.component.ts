import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { timer } from 'rxjs';
import { BsModalRef } from 'ngx-bootstrap/modal';
import flvjs from 'flv.js';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';
import { VideoEvent } from '@app/events-management/shared/video-event';

@Component({
  selector: 'app-video-events-media-modal',
  templateUrl: './video-events-media-modal.component.html',
  styleUrls: ['./video-events-media-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoEventsMediaModalComponent implements OnInit, OnDestroy {
  @Input()
  get videoEvent(): VideoEvent | undefined {
    return this._videoEvent;
  }
  set videoEvent(val: VideoEvent | undefined) {
    this.getFileList(val);
    this._videoEvent = val;
  }

  log: Logger;
  flvPlayers: any[] = [];

  fileListLoading = false;
  imgFileList: string[] = [];
  audioFileList: string[] = [];
  videoFileList: string[] = [];

  _videoEvent: VideoEvent | undefined;

  constructor(public activeModal: BsModalRef, private loggerFactory: LoggerFactory) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {}

  ngOnDestroy(): void {
    this.destoryVideo();
  }

  getFileList(videoEvent: VideoEvent | undefined) {
    if (!videoEvent) {
      return;
    }
    videoEvent.urls = Array.isArray(videoEvent.urls) ? videoEvent.urls : [];
    videoEvent.urls = videoEvent.urls.map((url: string) => {
      return url.startsWith('http') ? url : `${window.location.protocol}//${url}`;
    });
    this.videoFileList = videoEvent.urls.filter((url) => /\.(avi|mp4|h264)$/i.test(url));
    this.imgFileList = videoEvent.urls.filter((url) => /\.(jpg|jpeg|png|gif)$/i.test(url));
    this.audioFileList = videoEvent.urls.filter((url) => /\.(mp3)$/i.test(url));
    videoEvent.urls = [...this.videoFileList, ...this.imgFileList, ...this.audioFileList];

    if (this.videoFileList.length < 1) {
      return;
    }
    timer(300).subscribe(() =>
      this.videoFileList.forEach((url: string, index: number) => {
        if (this.isMp4Video(url)) {
          this.initFlvPlayer(url, index);
        }
      })
    );
  }

  initFlvPlayer(url: string, index: number) {
    try {
      const flvPlayer = flvjs.createPlayer(
        { type: 'MP4', url, isLive: false, hasAudio: true, hasVideo: true },
        {
          enableStashBuffer: true,
          autoCleanupSourceBuffer: true,
          autoCleanupMaxBackwardDuration: 5,
          autoCleanupMinBackwardDuration: 1,
          stashInitialSize: 32,
          fixAudioTimestampGap: false
        }
      );
      const videoEl: HTMLVideoElement = document.querySelector(`.multiVideo${index}`);
      flvPlayer.attachMediaElement(videoEl);
      flvPlayer.load();
      this.flvPlayers.push(flvPlayer);
    } catch (error) {
      console.error(error);
    }
  }

  isMp4Video(url: string = ''): boolean {
    return /\.(mp4)$/i.test(url);
  }

  destoryVideo() {
    this.flvPlayers.forEach((item) => {
      if (!item) {
        return;
      }
      item.unload();
      item.pause();
      item.detachMediaElement();
      item.destroy();
    });
    this.flvPlayers = [];
  }
}
