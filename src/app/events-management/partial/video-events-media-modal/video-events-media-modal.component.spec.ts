import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VideoEventsMediaModalComponent } from './video-events-media-modal.component';

describe('VideoEventsMediaModalComponent', () => {
  let component: VideoEventsMediaModalComponent;
  let fixture: ComponentFixture<VideoEventsMediaModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [VideoEventsMediaModalComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VideoEventsMediaModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
