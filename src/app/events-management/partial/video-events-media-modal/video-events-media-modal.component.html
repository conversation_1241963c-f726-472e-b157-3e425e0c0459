<div class="modal-header">
  <h5 class="modal-title">
    <ng-container *ngIf="videoEvent">{{ videoEvent.unitName }}-{{ videoEvent.videoEvents | translate }}</ng-container>
  </h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <div class="modal-container" *ngIf="videoEvent && videoEvent.urls.length > 0; else noDataTpl">
    <!-- 视频 -->
    <!-- disablePictureInPicture 禁止使用画中画；controlsList="nodownload noplaybackrate"隐藏播放速度和下载 -->
    <ng-container *ngFor="let url of videoFileList; let index = index">
      <video
        *ngIf="isMp4Video(url); else noVideoTpl"
        class="{{ 'half multiVideo' + index }}"
        controls
        controlsList="nodownload noplaybackrate"
        disablePictureInPicture
      ></video>
    </ng-container>
    <!-- 图片 -->
    <img *ngFor="let url of imgFileList" [src]="url" class="half" />

    <!-- 录音 -->
    <audio
      *ngFor="let url of audioFileList; let index = index"
      class="half"
      [src]="url"
      [autoplay]="index === 0"
      controls="controls"
    ></audio>
  </div>
</div>

<ng-template #noDataTpl>
  <div class="no-data">
    <img src="/assets/font/noDate.png" />
    <span class="no-data-title" translate>暂无数据</span>
  </div>
</ng-template>

<ng-template #noVideoTpl>
  <div class="no-video" translate>不支持播放</div>
</ng-template>
