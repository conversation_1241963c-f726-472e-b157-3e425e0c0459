<div class="tabs" *ngIf="tabIndex > -1">
  <div class="tab-header">
    <button
      *ngFor="let item of tabs; let i = index"
      [appApplyPermission]="item.appApplyPermission"
      [class.active]="i === tabIndex"
      (click)="changeTabIndex(i)"
    >
      {{ item.name | translate }}
    </button>
  </div>

  <ng-container *ngComponentOutlet="tabs[tabIndex]?.component"></ng-container>
</div>

<!-- <ng-template #noData>
  <div class="no-data m-portlet" translate>
    <img src="/assets/font/noDate.png" alt="" />
    暂无数据
  </div>
</ng-template> -->
