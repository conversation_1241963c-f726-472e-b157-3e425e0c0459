import { AfterViewInit, Component, OnInit } from '@angular/core';

import { ApplyPermissionService } from '@app/shared/services/apply-permission.service';
import { DeviceEventsListComponent } from '../device-events-list/device-events-list.component';
import { FenceEventsListComponent } from '../fence-events-list/fence-events-list.component';
import { BasicEventsListComponent } from '../basic-events-list/basic-events-list.component';
import { VideoEventsListComponent } from '../video-events-list/video-events-list.component';

@Component({
  selector: 'app-events-management',
  templateUrl: './events-management.component.html',
  styleUrls: ['./events-management.component.scss']
})
export class EventsManagementComponent implements OnInit, AfterViewInit {
  tabIndex = -1;
  tabs: Array<any> = [
    { name: '硬件预警1', appApplyPermission: 'deviceAlerts', component: DeviceEventsListComponent },
    { name: '围栏预警1', appApplyPermission: 'geofenceAlerts', component: FenceEventsListComponent },
    { name: '基础事件1', appApplyPermission: 'basicEvents', component: BasicEventsListComponent },
    { name: '视频事件1', appApplyPermission: 'videoEvents', component: VideoEventsListComponent }
  ];

  constructor(private applyPermissionService: ApplyPermissionService) {}

  ngOnInit() {
    // this.updateTabs();
    // if (this.tabs.length > 0) {
    //   this.changeTabIndex(0);
    // }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabs();
      this.changeTabIndex(this.tabs.length ? 0 : -1);
    }, 200);
  }

  changeTabIndex(i: number) {
    this.tabIndex = i;
  }

  updateTabs() {
    const widgets = this.applyPermissionService.getPageWidgets();
    this.tabs = this.tabs.filter((item) => {
      return widgets.some((widget) => widget.id === item.appApplyPermission && widget.authorised);
    });
  }
}
