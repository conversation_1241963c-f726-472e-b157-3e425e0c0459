import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { SharedModule } from '../shared/shared.module';
import { EventsManagementRoutingModule } from './events-management-routing.module';

import { EventsManagementComponent } from './partial/events-management/events-management.component';
import { DeviceEventsListComponent } from './partial/device-events-list/device-events-list.component';
import { FenceEventsListComponent } from './partial/fence-events-list/fence-events-list.component';
import { BasicEventsListComponent } from './partial/basic-events-list/basic-events-list.component';
import { VideoEventsListComponent } from './partial/video-events-list/video-events-list.component';

import { DeviceEventsHistoryComponent } from './partial/device-events-history/device-events-history.component';
import { VideoEventsMediaModalComponent } from './partial/video-events-media-modal/video-events-media-modal.component';

@NgModule({
  declarations: [
    BasicEventsListComponent,
    VideoEventsListComponent,
    EventsManagementComponent,
    DeviceEventsListComponent,
    DeviceEventsHistoryComponent,
    VideoEventsMediaModalComponent,
    FenceEventsListComponent
  ],
  imports: [
    FormsModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    CommonModule,
    EventsManagementRoutingModule,
    NzDatePickerModule
  ],
  entryComponents: [DeviceEventsHistoryComponent, VideoEventsMediaModalComponent]
})
export class EventsManagementModule {}
