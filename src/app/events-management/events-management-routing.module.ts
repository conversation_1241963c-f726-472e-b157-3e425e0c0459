import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';

import { EventsManagementComponent } from './partial/events-management/events-management.component';

const routes: Routes = RouteExtensions.withHost(
  { path: '', component: EventsManagementComponent, data: { title: '事件管理' } },
  []
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class EventsManagementRoutingModule {}
