<div class="layout data-rule" *ngIf="tabIndex > -1">
  <!-- *ngIf="tabIndex > -1" -->
  <div class="m-portlet rule-menu">
    <ul>
      <li
        *ngFor="let item of tabs; let i = index"
        [appApplyPermission]="item.appApplyPermission"
        [class.active]="tabIndex === i"
        (click)="changeTabIndex(i)"
      >
        <i [ngClass]="item.icon"></i>
        <a>{{ item.name | translate }}</a>
      </li>
    </ul>
  </div>

  <div class="right-body">
    <router-outlet></router-outlet>
  </div>
</div>
