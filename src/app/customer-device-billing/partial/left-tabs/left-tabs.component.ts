import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApplyPermissionService } from '@app/shared/services/apply-permission.service';

@Component({
  selector: 'app-left-tabs',
  templateUrl: './left-tabs.component.html',
  styleUrls: ['./left-tabs.component.scss']
})
export class LeftTabsComponent implements OnInit, AfterViewInit {
  tabIndex: number = 0;
  tabs: Array<any> = [
    {
      id: 'customerBillingPlan',
      name: '客户账户管理',
      appApplyPermission: 'account_list_tab',
      icon: 'iot icon-a-bianzu121'
    },
    {
      id: 'deviceBillingPlan',
      name: '设备计费管理',
      appApplyPermission: 'billing_list_tab',
      icon: 'iot icon-a-bianzu13'
    }
  ];

  constructor(
    private router: Router,
    private applyPermissionService: ApplyPermissionService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {}

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabs();
      this.changeTabIndex(this.tabs.length ? 0 : -1);
    }, 200);
  }

  updateTabs() {
    const widgets = this.applyPermissionService.getPageWidgets();
    this.tabs = this.tabs.filter((item) => {
      return widgets.some((widget) => widget.id === item.appApplyPermission && widget.authorised);
    });
  }

  changeTabIndex(i: number) {
    this.tabIndex = i;
    const url = this.tabs[i]?.id;
    this.router.navigate([`${url}`], {
      relativeTo: this.activatedRoute
    });
  }
}
