import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RouteExtensions } from '@app/core';
import { LeftTabsComponent } from './partial/left-tabs/left-tabs.component';
// import { AccountListComponent } from '@app/customer-billing-plan/partial/account-list/account-list.component';

const routes: Routes = RouteExtensions.withHost(
  {
    path: '',
    component: LeftTabsComponent,
    data: { title: '客户设备计费管理' },
    children: [
      {
        path: 'customerBillingPlan',
        loadChildren: '@app/customer-billing-plan/customer-billing-plan.module#CustomerBillingPlanModule'
      },
      {
        path: 'deviceBillingPlan',
        loadChildren: '@app/device-billing-plan/device-billing-plan.module#DeviceBillingPlanModule'
      }
    ]
  },
  []
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerDeviceBillingRoutingModule {}
