import { Component, OnInit, AfterViewInit, OnDestroy, ChangeDetectionStrategy, Input, ViewChild } from '@angular/core';
import { Subscription, timer } from 'rxjs';

import { Feature, MapBrowserEvent, Overlay } from 'ol';
import LayerVector from 'ol/layer/Vector';
import { Vector } from 'ol/source';
import { Style, Icon } from 'ol/style';
import { Point } from 'ol/geom';

import { Coordinate } from 'ol/coordinate';
import OverlayPositioning from 'ol/OverlayPositioning';
import { ZRMap } from 'zr-map-ol';

import { getVehicleCustomIcon } from '@app/data-management/vehicle-management/shared/models/vehicle';
import { MapService } from '@app/map/services/map.service';
import { SwitchMapService } from '@app/map/services/switch-map.service';
import { DeviceInfoWindowComponent } from '../device-info-window/device-info-window.component';

@Component({
  selector: 'app-device-info-map',
  templateUrl: './device-info-map.component.html',
  styleUrls: ['./device-info-map.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceInfoMapComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() isShowToolbar = true;
  @Input() isShowDeviceStatusLegend = true;
  @Input() updatePosition: boolean = false;

  @Input()
  set device(val: any) {
    this._device = val;
    if (!val || !val.lng || !val.lat) {
      return;
    }
    this.setMarker();
    if (!this.deviceInfoWindow) {
      this.initDeviceInfoWindow();
    }
    this.openDeviceInfoWindow();
  }

  get device(): any {
    return this._device;
  }

  mapService: MapService;
  map: ZRMap;
  marker: Feature = new Feature();
  markerSource = new Vector();
  markerLayer = new LayerVector({
    source: this.markerSource
  });

  deviceInfoWindow: Overlay;
  deviceInfoWindowSubscription: Subscription;
  @ViewChild(DeviceInfoWindowComponent) deviceInfoWindowComponent: DeviceInfoWindowComponent;

  _device: any;

  constructor(private switchMapService: SwitchMapService) {}

  ngOnInit() {}

  ngAfterViewInit(): void {
    this.deviceInfoWindowSubscription = this.deviceInfoWindowComponent.closed.subscribe(() => {
      this.closeDeviceInfoWindow();
    });
    timer(0).subscribe(() => this.initMap());
  }

  ngOnDestroy(): void {
    this.deviceInfoWindowSubscription.unsubscribe();
    this.map.setTarget(null);
  }

  initMap() {
    this.mapService = new MapService();
    this.mapService.initMapConfig(this.mapService.mapConfig);
    this.map = this.mapService.initMap({ target: 'device-info-map' });
    this.map.setMapType(this.switchMapService.mapType as any);
    this.map.addLayer(this.markerLayer);
    this.initDeviceInfoWindow();
    this.map.on('singleclick', this.onMapClick.bind(this));
    this.map.on('pointermove', this.onMapPointerMove.bind(this));
  }

  onMapClick(e: MapBrowserEvent) {
    if (!this.map) {
      return;
    }
    const features = this.map.getFeaturesAtPixel(e.pixel);
    if (features.includes(this.marker)) {
      this.openDeviceInfoWindow();
    }
  }

  onMapPointerMove(e: MapBrowserEvent) {
    if (!this.map) {
      return;
    }
    let cursor = '';
    const features = this.map.getFeaturesAtPixel(e.pixel);
    const feature = features[0] as Feature;
    if (feature) {
      cursor = feature.get('cursor') || cursor;
    }
    this.map.getTargetElement().style.cursor = cursor;
  }

  setMarker() {
    if (!this.map || !this.device || !this.device.lng || !this.device.lat) {
      return;
    }
    const coordinate: Coordinate = [this.device.lng, this.device.lat];
    const style = new Style({
      image: new Icon({
        src: getVehicleCustomIcon('default-map', this.device.vehicleStatus),
        scale: 0.4
      })
    });
    this.marker.setGeometry(new Point(coordinate));
    this.marker.setStyle(style);
    this.marker.set('cursor', 'pointer');
    if (!this.markerSource.hasFeature(this.marker)) {
      this.markerSource.addFeature(this.marker);
    }
  }

  initDeviceInfoWindow() {
    if (!this.map || !this.device) {
      return;
    }
    this.deviceInfoWindowComponent.setDevice(this.device, this.updatePosition);
    this.deviceInfoWindow = new Overlay({
      element: this.deviceInfoWindowComponent.elementRef.nativeElement,
      autoPan: { animation: { duration: 250 } },
      positioning: OverlayPositioning.TOP_CENTER
    });
    this.map.addOverlay(this.deviceInfoWindow);
    this.deviceInfoWindow.setPosition(undefined);
  }

  openDeviceInfoWindow() {
    if (!this.map || !this.device) {
      return;
    }
    if (!this.updatePosition) {
      const isOpen = this.deviceInfoWindow.getPosition();
      if (isOpen) {
        return;
      }
    }

    const point = this.marker.getGeometry() as Point;
    const coordinate = point.getCoordinates();
    this.deviceInfoWindowComponent.setDevice(this.device, this.updatePosition);
    this.deviceInfoWindow.setPosition(coordinate);
    this.map.getView().setCenter(coordinate);
    // const size = (this.marker.getStyle() as Style).getImage().getSize();
    const offset = [-25, -30];
    // // 图片是异步获取
    // if (size) {
    //   offset[1] = -size[1] / 2;
    // }
    this.deviceInfoWindow.setOffset(offset);
  }

  closeDeviceInfoWindow() {
    if (this.deviceInfoWindow) {
      this.deviceInfoWindow.setPosition(undefined);
    }
  }
}
