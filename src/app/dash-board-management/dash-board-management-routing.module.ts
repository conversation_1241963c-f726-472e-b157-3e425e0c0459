import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';

import { InformationBoardComponent } from './partial/information-board/information-board.component';

const routes: Routes = RouteExtensions.withHost(
  { path: '', component: InformationBoardComponent, data: { title: '数据看板' } },
  []
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class DashBoardManagementRoutingModule {}
