import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { BehaviorSubject, Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { WebApiResultResponse } from '@core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class InformationBoardService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  // 获取看版的车辆和设备信息统计
  getVehicleAndDeviceStatistical() {
    const url = 'glcrm-report-api/v1/api/report/getVehicleAndDeviceStatistical';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取看版的预警和事件统计
  getWarningAndEventStatistical() {
    const url = 'glcrm-report-api/v1/api/report/getWarningAndEventStatistical';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取车辆设备状态
  pageCommand(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/pageCommand/${params.page}/${params.limit}`;
    // const url = `http://*************:7080/v1/api/command/pageCommand/${params.page}/${params.limit}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
