export interface Fence {
  fencePoints: any[];
  endDate: string;
  beginDate: string;
  id?: string;
  beginDateTime: string;
  deviceId: [string];
  districtId: number;
  enabled: boolean;
  endDateTime: string;
  fenceAlertType: number;
  fenceArea: number;
  fenceDescription: string;
  fenceName: string;
  fencePolygons: [
    {
      lat: string;
      lng: string;
    }
  ];
  fenceType: number;
  fenceShape: number;
  orgId: string;
  fenceSpecialJson: any;
}
