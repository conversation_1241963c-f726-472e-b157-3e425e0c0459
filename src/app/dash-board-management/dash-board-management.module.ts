import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { DashBoardManagementRoutingModule } from './dash-board-management-routing.module';

import { InformationBoardComponent } from './partial/information-board/information-board.component';

@NgModule({
  declarations: [InformationBoardComponent],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    DashBoardManagementRoutingModule
  ],
  entryComponents: []
})
export class DashBoardManagementModule {}
