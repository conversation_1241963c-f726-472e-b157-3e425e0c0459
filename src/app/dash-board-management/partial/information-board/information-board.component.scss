.information-board {
  background-color: rgba(5, 17, 39, 1);
  color: #fff;
  height: 100%;
  padding-bottom: 20px;

  .today {
    opacity: 1;
    font-size: 14px;
    font-weight: 500;
    // font-family: SourceHanSansCN-Bold;
    // font-family: "Noto Sans SC";
    font-weight: 700;
    line-height: 60px;
    margin-left: 50px;
    color: #bec3d1;
    text-shadow: 0px 1px 2px 0px #b2b9e4, inset;
  }

  .icontext {
    margin: 0 50px;
    display: flex;
    flex-direction: row;

    .lefticon {
      padding: 14px;
      width: 462px;
      height: 462px;
      background: linear-gradient(180deg, rgba(20, 33, 55, 1), rgba(2, 24, 63, 0.4));

      .textcolor {
        color: #ffffff;
      }
    }

    .righticon {
      flex: 1;

      .righticon_top {
        height: 222px;
        color: black;
        display: flex;
        flex-direction: row;

        .offline_car {
          margin-left: 50px;
          width: 50%;
          height: 222px;
          // background:linear-gradient(180deg, rgba(14, 51, 120, 0.6),rgba(2, 24, 63, 0.6));
          background: linear-gradient(180deg, rgba(20, 33, 55, 1), rgba(2, 24, 63, 0.4));
          color: #ffffff;

          .offline_car_title {
            // width: 376px;
            width: 100%;
            height: 42px;
            background-image: url(../../../../assets/font/title.png);
            background-repeat: no-repeat;
            background-size: contain;

            ul {
              height: 180px;
              overflow-y: scroll;

              li {
                height: 36px;
                line-height: 36px;
                font-size: 14px;
                font-family: "Noto Sans SC";
                font-weight: 400;
              }

              li:nth-child(2n) {
                // background: linear-gradient(180deg,rgba(20, 33, 55, 1.0),rgba(2, 24, 63, 0.4));
                // background: linear-gradient(90deg,rgba(189, 208, 237, 0.1), rgba(5, 17, 39, 0.1));\

                background: linear-gradient(
                  to right,
                  rgba(5, 17, 39, 0.1),
                  rgba(189, 208, 237, 0.1),
                  rgba(5, 17, 39, 0.1)
                );
              }
            }
          }
        }
      }

      .righticon_bot {
        margin-top: 30px;
        margin-left: 50px;
        height: 210px;
        // background: linear-gradient(180deg, rgba(14, 51, 120, 0.4),rgba(2, 24, 63, 0.4));
        background: linear-gradient(180deg, rgba(20, 33, 55, 1), rgba(2, 24, 63, 0.4));
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        .dashboard {
          width: 202px;
          height: 202px;
          background-image: url(../../../../assets/font/circle.png);
          background-repeat: no-repeat;
          background-size: contain;
          text-align: center;

          .total {
            font-size: 50px;
            font-family: DS-Digital;
            font-style: italic;
            line-height: 202px;
            color: #ffffff;
            text-align: center;
            position: relative;

            .total_name {
              width: 100%;
              position: absolute;
              left: 0;
              bottom: 0;
              font-size: 12px;
              font-weight: 400;
              line-height: 36px;
            }
          }
        }

        .dashboard_bar {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }

        .echarts_bar {
          flex: 1.5;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          // margin:0  50px;
        }
      }
    }
  }

  .footer_list {
    margin: 30px 50px 0 50px;
    display: flex;
    flex-direction: row;

    .car_list {
      flex: 1;
      height: 222px;
      // background:linear-gradient(180deg, rgba(14, 51, 120, 0.6),rgba(2, 24, 63, 0.6));
      // border-image: linear-gradient(208deg, rgba(37, 50, 73, 1.0),rgba(172, 202, 255, 1.0));
      background: linear-gradient(180deg, rgba(20, 33, 55, 1), rgba(2, 24, 63, 0.4));
      color: #ffffff;

      .carlist_title {
        width: 100%;
        height: 42px;
        background-image: url(../../../../assets/font/head.png);
        background-repeat: no-repeat;
        background-size: contain;

        ul {
          height: 180px;

          li {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            font-family: "Noto Sans SC";
            font-weight: 400;
          }

          li:nth-child(2n) {
            // background: linear-gradient(90deg,rgba(189, 208, 237, 0.1), rgba(5, 17, 39, 0.1));
            background: linear-gradient(to right, rgba(5, 17, 39, 0.1), rgba(189, 208, 237, 0.1), rgba(5, 17, 39, 0.1));
          }

          .list_li {
            flex: 1;
            text-align: center;
          }

          .list_li_m {
            flex: 2;
            text-align: center;
          }

          .list_li_header {
            flex: 1.5;
            text-align: center;
          }

          .list_li_c {
            flex: 3;
            text-align: center;
          }
        }
      }
    }
  }

  .li_top {
    color: rgba(255, 182, 81, 1);
    font-weight: bold;
  }

  ul::-webkit-scrollbar {
    display: none;
  }

  .textcolor {
    font-size: 18px;
    color: #fff;
    font-weight: 500;
  }

  .ml_36 {
    margin-left: 36px;
  }

  .title_t {
    font-size: 18px;
    font-family: Alibaba-PuHuiTi-B;
    line-height: 42px;
    color: #ffffff;
    text-align: center;
  }

  .li-f {
    display: flex;
    flex-direction: row;
  }

  .span_li {
    flex: 1;
    text-align: center;
  }

  .fontsy {
    font-family: "Noto Sans SC";
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .list_item_show {
    width: 100%;
    height: 180px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .noDate {
      background-image: url(../../../../assets/font/noDate.png);
      background-position: center;
      background-repeat: no-repeat;
      width: 200px;
      height: 120px;
    }

    .no_text {
      font-size: 12px;
    }
  }

  .radius {
    border-radius: 4px;
    border: 2px solid #979797;
    border-image: linear-gradient(180deg, rgba(14, 51, 120, 0.6), rgba(2, 24, 63, 0.6)) 1;
    clip-path: inset(0 0 round 5px);

    // border: 2px solid #979797;
    // border-image-source: radial-gradient( rgba(14, 51, 120, 0.1),rgba(2, 24, 63, 0.1));
    // border-image-slice: 1;
    // clip-path: inset(0 0 round 5px);
  }
}
