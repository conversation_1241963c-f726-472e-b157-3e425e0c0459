import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild } from '@angular/core';
import { Subscription, timer } from 'rxjs';

import { EChartOption } from 'echarts';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { InformationBoardService } from '@app/dash-board-management/shared/service/information-board.service';

@Component({
  selector: 'app-information-board',
  templateUrl: './information-board.component.html',
  styleUrls: ['./information-board.component.scss']
})
export class InformationBoardComponent implements OnInit, AfterViewInit, OnDestroy {
  log: Logger;

  accountSetting = accountSetting;
  loading = false;
  deviceStatisticalVo: any;
  vehicleStatisticalVo: any;

  chartOption2: EChartOption = {};
  chartOption: EChartOption = {};

  noSignalVoList: Array<any> = [];

  blockedVoList: Array<any> = []; //断油断电
  warning: Array<any> = []; //硬件预警
  events: Array<any> = []; //基础事件
  fenceWarning: Array<any> = []; //围栏预警
  todayTime = new Date();

  deviceList: Array<any> = [];
  vehicleList: Array<any> = [];
  sldata: Array<any> = [];

  private shower: Subscription;

  constructor(private informationBoardService: InformationBoardService, private translate: TranslateService) {
    this.translate.onLangChange.subscribe(() => {
      this.clearRefreshInterval();
      this.refreshData();
    });
  }

  ngOnInit() {
    this.vehicleStatisticalVo = {
      vehicleTotal: 0
    };
  }

  ngAfterViewInit() {
    this.refreshData();
  }

  ngOnDestroy() {
    this.clearRefreshInterval();
  }

  // 每30秒刷新一次
  refreshData() {
    this.shower = timer(0, 30_000).subscribe(() => this.getlist());
  }

  clearRefreshInterval() {
    if (this.shower) {
      this.shower.unsubscribe();
    }
  }

  // 请求数据
  getlist() {
    this.getVehicleAndDeviceStatistical();
    this.getWarningAndEventStatistical();
  }

  // 获取看版的车辆和设备信息统计
  getVehicleAndDeviceStatistical() {
    this.informationBoardService
      .getVehicleAndDeviceStatistical()
      .pipe()
      .subscribe((response) => {
        this.noSignalVoList = response.data.noSignalVo || [];
        this.blockedVoList = response.data.blockedVo || [];

        // echarts1
        this.deviceStatisticalVo = response.data.deviceStatisticalVo || {};
        this.initChart();

        // echarts2
        this.vehicleStatisticalVo = response.data.vehicleStatisticalVo || {};
        this.initChart2();
      });
  }

  // 获取看版的预警和事件统计
  getWarningAndEventStatistical() {
    this.informationBoardService
      .getWarningAndEventStatistical()
      .pipe()
      .subscribe((response) => {
        this.fenceWarning = response.data.fenceWarning.slice(0, 5) || [];
        this.warning = response.data.warning.slice(0, 5) || [];
        this.events = response.data.events.slice(0, 5) || [];
      });
  }

  initChart() {
    if (window.localStorage.getItem('lang') === 'en-US') {
      this.deviceList = [
        { value: this.deviceStatisticalVo.offlineTotal || 0, name: 'Offline' },
        { value: this.deviceStatisticalVo.onlineTotal || 0, name: 'Online' },
        { value: this.deviceStatisticalVo.invalidTotal || 0, name: 'Invalid' }
      ];
    }

    if (window.localStorage.getItem('lang') === 'zh-CN') {
      this.deviceList = [
        { value: this.deviceStatisticalVo.offlineTotal || 0, name: '离线' },
        { value: this.deviceStatisticalVo.onlineTotal || 0, name: '在线' },
        { value: this.deviceStatisticalVo.invalidTotal || 0, name: '未启用' }
      ];
    }

    if (window.localStorage.getItem('lang') === 'es-MX') {
      this.deviceList = [
        { value: this.deviceStatisticalVo.offlineTotal || 0, name: 'Offline' },
        { value: this.deviceStatisticalVo.onlineTotal || 0, name: 'En línea' },
        { value: this.deviceStatisticalVo.invalidTotal || 0, name: 'aún no activado' }
      ];
    }

    this.chartOption = {
      title: {
        text: this.deviceStatisticalVo.deviceTotal || 0,
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#fff',
          rich: {
            name: {
              fontFamily: 'PingFangSC-Regular',
              fontSize: 14,
              color: 'rgba(0,0,0,0.45)',
              lineHeight: 22
            },
            value: {
              fontFamily: 'HelveticaNeue',
              fontSize: 30,
              color: 'rgba(0,0,0,0.85)',
              lineHeight: 38
            }
          }
        }
      },
      tooltip: {
        trigger: 'item'
      },
      color: ['rgba(0, 130, 255, 1.0)', 'rgba(51, 240, 240, 1.0)', 'rgba(178, 205, 255, 1.0)'],
      legend: {
        icon: 'circle',
        right: '0%',
        bottom: '0%',
        data: this.deviceList,
        textStyle: {
          color: '#fff'
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['35%', '55%'],
          label: {
            formatter: '{b|{b}：}{c} ',
            rich: {
              b: {
                color: '#4C5058',
                fontSize: 14,
                lineHeight: 33
              }
            },
            normal: {
              textStyle: {
                color: '#ffffff'
              }
            }
          },
          labelLine: {
            show: true
          },
          data: this.deviceList
        }
      ]
    };
  }

  initChart2() {
    if (window.localStorage.getItem('lang') === 'en-US') {
      this.vehicleList = [
        { value: this.vehicleStatisticalVo.movingTotal || 0, name: 'Moving' }, // 行驶中
        { value: this.vehicleStatisticalVo.staticTotal || 0, name: 'Stop' }, //停车
        { value: this.vehicleStatisticalVo.noSignalTotal || 0, name: 'Offline' }, // 无信号
        { value: this.vehicleStatisticalVo.blockedTotal || 0, name: 'FCO' } //断油断电
      ];
      this.sldata = ['Moving', 'Stop', 'Offline', 'FCO'];
    }

    if (window.localStorage.getItem('lang') === 'zh-CN') {
      this.vehicleList = [
        { value: this.vehicleStatisticalVo.movingTotal || 0, name: '行驶中' }, // 行驶中
        { value: this.vehicleStatisticalVo.staticTotal || 0, name: '停车' }, // 停车
        { value: this.vehicleStatisticalVo.noSignalTotal || 0, name: '无信号' }, // 无信号
        { value: this.vehicleStatisticalVo.blockedTotal || 0, name: '断油断电' } //断油断电
      ];
      this.sldata = ['行驶中', '停车', '无信号', '断油断电'];
    }

    if (window.localStorage.getItem('lang') === 'es-MX') {
      this.vehicleList = [
        { value: this.vehicleStatisticalVo.movingTotal || 0, name: 'moviendo' }, // 行驶中
        { value: this.vehicleStatisticalVo.staticTotal || 0, name: 'detener el vehículo' }, // 停车
        { value: this.vehicleStatisticalVo.noSignalTotal || 0, name: 'sin señal' }, // 无信号
        { value: this.vehicleStatisticalVo.blockedTotal || 0, name: 'corte de petróleo y electricidad' } //断油断电
      ];
      this.sldata = ['moviendo', 'detener el vehículo', 'sin señal', 'corte de petróleo y electricidad'];
    }

    this.chartOption2 = {
      title: {
        show: false
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        show: false,
        bottom: '5%', //图例距离整个容器底部的距离
        left: 'center' //图例距离整个容器左边
      },
      grid: {
        // left: '10%',
        // right: '10%',
        top: '5px',
        bottom: '0',
        containLabel: true,
        backgroundColor: 'rgba(108, 128, 151, 1.0)'
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],

        axisTick: {
          // 刻度
          show: false // 不显示刻度线
        },
        axisLine: {
          // 设置轴线
          show: false,
          lineStyle: {
            color: '#d0deee'
          }
        },
        splitLine: {
          // 去除背景网格线
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#d0deee'
          }
        },
        axisLabel: {
          show: false
        }
      },
      yAxis: [
        {
          type: 'category',
          data: this.sldata,
          axisTick: {
            // 刻度
            show: false // 不显示刻度线
          },
          axisLine: {
            lineStyle: {
              color: '#d0deee'
            }
          }
        },
        {
          type: 'category',
          data: this.vehicleList,
          axisTick: {
            // 刻度
            show: false // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
            lineStyle: {
              color: '#d0deee'
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          data: this.vehicleList,
          barWidth: 20,
          itemStyle: {
            // 柱状图的背景色
            normal: {
              // color:'rgba(88, 147, 229, 0.5)',
              color: {
                type: 'linear',
                colorStops: [
                  {
                    offset: 0,

                    color: 'rgba(88, 147, 229, 0.1)'
                  },
                  {
                    offset: 0.7,
                    color: 'rgba(88, 147, 229, 0.7)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(88, 147, 229, 1.0)' // 100% 处的颜色
                  }
                ]
              }
            },
            fontSize: '12'
          }
        }
      ]
    };
  }
}
