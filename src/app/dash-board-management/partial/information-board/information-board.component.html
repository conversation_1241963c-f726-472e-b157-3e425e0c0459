<div class="information-board">
  <div class="today">{{ '今天' | translate }} {{ todayTime | localDate : accountSetting.dateFormat }}</div>

  <div class="icontext">
    <div class="lefticon radius">
      <div class="textcolor">{{ '终端设备' | translate }}</div>
      <div echarts [options]="chartOption" class="demo-chart"></div>
    </div>
    <div class="righticon">
      <div class="righticon_top">
        <div class="offline_car radius">
          <div class="offline_car_title title_t">
            <div class="textcolor">{{ '无信号车辆' | translate }}</div>
            <ul *ngIf="noSignalVoList && noSignalVoList.length != 0">
              <li *ngFor="let item of noSignalVoList" class="li-f">
                <span class="span_li fontsy" [title]="item.ownerName">{{ item.ownerName }}</span>
                <span class="span_li">{{ item.plateNumber }}</span>
              </li>
            </ul>
            <div class="list_item_show" *ngIf="!noSignalVoList || noSignalVoList.length === 0">
              <div class="noDate"></div>
              <div class="no_text" translate>暂无数据</div>
            </div>
          </div>
        </div>
        <div class="offline_car radius">
          <div class="offline_car_title title_t">
            <div class="textcolor">{{ '断油断电车辆' | translate }}</div>
            <div *ngIf="blockedVoList && blockedVoList.length != 0">
              <ul>
                <li *ngFor="let item of blockedVoList" class="li-f">
                  <span class="span_li">{{ item.ownerName }}</span>
                  <span class="span_li">{{ item.plateNumber }}</span>
                </li>
              </ul>
            </div>
            <div class="list_item_show" *ngIf="!blockedVoList || blockedVoList.length === 0">
              <div class="noDate"></div>
              <div class="no_text" translate>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <div class="righticon_bot radius">
        <div class="dashboard_bar">
          <div class="dashboard">
            <div class="total">
              {{ vehicleStatisticalVo.vehicleTotal || 0 }}
              <span class="total_name">
                {{ '车辆总数' | translate }}
              </span>
            </div>
          </div>
        </div>
        <div class="echarts_bar">
          <div
            echarts
            [options]="chartOption2"
            class="demo-chart"
            id="myChart"
            style="width: 392px; height: 162px"
          ></div>
        </div>
      </div>
    </div>
  </div>
  <div class="footer_list">
    <div class="car_list radius">
      <div class="carlist_title title_t">
        <div class="textcolor">{{ '围栏预警1' | translate }} Top5</div>
        <div *ngIf="fenceWarning && fenceWarning.length != 0">
          <ul>
            <li *ngFor="let item of fenceWarning; let i = index" class="li-f">
              <div [class]="i === 0 || i === 1 || i === 2 ? 'li_top list_li_header' : 'list_li_header'">
                TOP{{ i + 1 }}
              </div>
              <!-- <span class="list_li_c">{{ item.countAlertTypeName }}</span> -->
              <!-- 围栏报警类型:0 进围栏报警;1 出围栏报警 -->
              <span class="list_li" translate>
                {{ item.countAlertType == 0 ? '进围栏' : item.countAlertType == 1 ? '出围栏' : '' }}
              </span>
              <span class="list_li">{{ item.num }}</span>
            </li>
          </ul>
        </div>
        <div class="list_item_show" *ngIf="!fenceWarning || fenceWarning.length === 0">
          <div class="noDate"></div>
          <div class="no_text" translate>暂无数据</div>
        </div>
      </div>
    </div>
    <div class="car_list ml_36 radius">
      <div class="carlist_title title_t">
        <div class="textcolor">{{ '硬件预警1' | translate }} Top5</div>
        <div *ngIf="warning && warning.length != 0">
          <ul>
            <li *ngFor="let item of warning; let i = index" class="li-f">
              <div class="list_li">
                <span [class]="i === 0 || i === 1 || i === 2 ? 'li_top' : ''">TOP{{ i + 1 }}</span>
              </div>
              <div class="list_li_m" translate>{{ item.countAlertTypeName }}</div>
              <div class="list_li">{{ item.num }}</div>
            </li>
          </ul>
        </div>
        <div class="list_item_show" *ngIf="!warning || warning.length === 0">
          <div class="noDate"></div>
          <div class="no_text" translate>暂无数据</div>
        </div>
      </div>
    </div>
    <div class="car_list ml_36 radius">
      <div class="carlist_title title_t">
        <div class="textcolor">{{ '基础事件1' | translate }} Top5</div>

        <div *ngIf="events && events.length != 0">
          <ul>
            <li *ngFor="let item of events; let i = index" class="li-f">
              <span class="list_li">
                <span [class]="i === 0 || i === 1 || i === 2 ? 'li_top' : ''">TOP{{ i + 1 }}</span>
              </span>
              <span class="list_li_m" translate>{{ item.countAlertTypeName }}</span>
              <span class="list_li">{{ item.num }}</span>
            </li>
          </ul>
        </div>
        <div class="list_item_show" *ngIf="!events || events.length === 0">
          <div class="noDate"></div>
          <div class="no_text" translate>暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</div>
