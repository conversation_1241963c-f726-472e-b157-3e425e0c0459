import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';
import { ReportsManagementRoutingModule } from './reports-management-routing.module';

import { ReportTabsComponent } from './partial/report-tabs/report-tabs.component';
import { DriveReportComponent } from './partial/drive-report/drive-report.component';
import { TravelReportComponent } from './partial/travel-report/travel-report.component';
import { StopsReportComponent } from './partial/stops-report/stops-report.component';
import { SpeedingReportComponent } from './partial/speeding-report/speeding-report.component';
import { CustomReportComponent } from './partial/custom-report/custom-report.component';

import { CustomReportFormModalComponent } from './partial/component/custom-report-form-modal/custom-report-form-modal.component';
import { CustomReportViewModalComponent } from './partial/component/custom-report-view-modal/custom-report-view-modal.component';
import { ReportTemplateComponent } from './partial/component/report-template/report-template.component';

@NgModule({
  declarations: [
    ReportTabsComponent,
    DriveReportComponent,
    TravelReportComponent,
    StopsReportComponent,
    SpeedingReportComponent,
    CustomReportComponent,
    CustomReportFormModalComponent,
    CustomReportViewModalComponent,
    ReportTemplateComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    SharedModule,
    ReportsManagementRoutingModule
  ],
  entryComponents: [CustomReportFormModalComponent, CustomReportViewModalComponent]
})
export class ReportsManagementModule {}
