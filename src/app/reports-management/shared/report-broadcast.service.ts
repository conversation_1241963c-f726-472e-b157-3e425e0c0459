import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

interface Message {
  type: string;
  value: any;
}

/**
 * 报告广播服务
 * 用于报告页面之间的tab切换和查询参数传递
 */
@Injectable({
  providedIn: 'root'
})
export class ReportBroadcastService {
  private subject = new Subject<Message>();

  // 发布广播
  broadcast(message: Message) {
    this.subject.next(message);
  }

  // 订阅广播
  getMessages(): Observable<Message> {
    return this.subject.asObservable();
  }
}
