import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { WebApiResultResponse } from '../../core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class ReportService extends WebApiResultResponse {
  tabList: Array<any> = [];
  constructor(private http: HttpClient) {
    super();
  }

  // 获取获取车辆详细信息
  getVehicleDetails(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/searchByVehicleData/`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取行程报告
  getDriveList(params: any): Observable<any> {
    const url = 'glcrm-report-api/v1/api/report/driving';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取行程报告
  getTripList(params: any): Observable<any> {
    const url = 'glcrm-report-api/v1/api/report/travel';
    // const url = 'http://*************:7010/v1/api/report/travel';

    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取停留报告
  getStayList(params: any): Observable<any> {
    const url = 'glcrm-report-api/v1/api/report/historyStay';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取超速报告
  getSpeedingList(params: any): Observable<any> {
    const url = 'glcrm-report-api/v1/api/report/overspeed';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
