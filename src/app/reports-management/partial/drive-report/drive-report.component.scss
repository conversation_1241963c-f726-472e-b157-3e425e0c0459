:host ::ng-deep {
  display: block;

  // .ant-table-body {
  //     max-height: initial !important;
  //     height: calc(100vh - 420px);
  // }

  // .ant-table-tbody>tr>td {
  //     border-bottom: none !important;
  // }
}

.table-list {
  padding: 10px;
  overflow-x: auto;
  overflow-y: hidden;
}

.address-time {
  width: 300px;
  padding: 16px;

  span {
    text-align: left;
    line-height: 14px;
  }

  .ellipsis {
    color: #999999;
  }
}

.content-normal,
td {
  color: #575e72;
}

th {
  background: #f4f7fb !important;
  font-weight: 600 !important;
}

.m-portlet {
  box-shadow: none;
}
