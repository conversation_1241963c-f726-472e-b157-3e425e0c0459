<app-report-template (onSearch)="getReport($event)">
  <div class="table-list">
    <nz-table
      #fixedTable
      [nzLoading]="loading"
      [nzData]="reportList"
      [nzFrontPagination]="false"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '1150px', y: 'calc(100vh - 510px)' }"
      [nzNoResult]="noResultTpl"
    >
      <thead>
        <tr>
          <th nzWidth="200px">
            {{ '日期' | translate }}
          </th>
          <th nzWidth="120px">
            {{ '总里程' | translate }}
          </th>
          <th nzWidth="120px">
            {{ '行程次数' | translate }}
          </th>
          <th nzWidth="180px">
            {{ '行程时长' | translate }}
          </th>
          <th nzWidth="120px">
            {{ '停留次数' | translate }}
          </th>
          <th nzWidth="180px">
            {{ '停留时长' | translate }}
          </th>
          <th nzWidth="180px" nzRight>
            {{ '操作' | translate }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="let reportItem of fixedTable.data; let odd = odd"
          [ngStyle]="{ background: odd ? '#F4F7FB' : '#FFFFFF' }"
        >
          <td style="padding: 0">
            <div class="address-time">
              <div class="content-normal">
                {{ reportItem.reportDate | localDate : accountSetting.dateFormat }}
              </div>
            </div>
          </td>
          <td>{{ reportItem.totalDistance }}km</td>
          <td>{{ reportItem.tripCount }}</td>
          <td>{{ reportItem.drivingDuration }}</td>
          <td>{{ reportItem.stopCount }}</td>
          <td>{{ reportItem.stopDuration }}</td>
          <td>
            <a>行程明细</a>
            <a>停留明细</a>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</app-report-template>

<ng-template #noResultTpl>
  <nz-empty class="no-data" nzNotFoundImage="/assets/font/noDate.png" [nzNotFoundContent]="contentTpl">
    <ng-template #contentTpl>
      <span translate>暂无数据</span>
    </ng-template>
  </nz-empty>
</ng-template>
