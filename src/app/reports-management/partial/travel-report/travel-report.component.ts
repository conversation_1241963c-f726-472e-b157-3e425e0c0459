import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { Logger, LoggerFactory } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { Language } from '@app/account-settings/models/account';

import { VehicleInfo } from '@app/reports-management/shared/models/report';
import { ReportService } from '@app/reports-management/shared/report.service';

@Component({
  selector: 'app-travel-report',
  templateUrl: './travel-report.component.html',
  styleUrls: ['./travel-report.component.scss']
})
export class TravelReportComponent implements OnInit {
  log: Logger;
  accountSetting = accountSetting;
  Language = Language;

  loading = false;
  reportList: Array<any> = [];

  constructor(private loggerFactory: LoggerFactory, private dataReportService: ReportService) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {}

  getReport(vehicleInfo: VehicleInfo | undefined) {
    if (!vehicleInfo) {
      this.reportList = [];
      return;
    }
    const params = {
      deviceNo: vehicleInfo.deviceNo,
      startTime: vehicleInfo.startTime,
      endTime: vehicleInfo.endTime,
      paging: {
        pageIndex: 1,
        pageSize: 10000
      }
    };
    this.loading = true;
    this.dataReportService
      .getTripList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (res) => {
          this.reportList = [];
          if (!res || !res.success) {
            return;
          }
          res.data = res.data || [];
          this.reportList = res.data.map((item: any) => {
            item.mile = parseFloat(item.mile).toFixed(1);
            item.averageSpeed = Math.round(parseFloat(item.averageSpeed));
            item.maxSpeed = Math.round(parseFloat(item.maxSpeed));
            return item;
          });
        },
        (err) => (this.reportList = [])
      );
  }
}
