import { AfterViewInit, Component, OnInit } from '@angular/core';
import { trigger, transition, useAnimation } from '@angular/animations';
import { flyInAnimation } from '@app/shared/animations';

import { ApplyPermissionService } from '@app/shared/services/apply-permission.service';

import { DriveReportComponent } from '../drive-report/drive-report.component';
import { TravelReportComponent } from '../travel-report/travel-report.component';
import { StopsReportComponent } from '../stops-report/stops-report.component';
import { SpeedingReportComponent } from '../speeding-report/speeding-report.component';
import { CustomReportComponent } from '../custom-report/custom-report.component';

@Component({
  selector: 'app-report-tabs',
  templateUrl: './report-tabs.component.html',
  styleUrls: ['./report-tabs.component.scss'],
  animations: [trigger('flyIn', [transition('* => *', useAnimation(flyInAnimation))])]
})
export class ReportTabsComponent implements OnInit, AfterViewInit {
  tagIndex: number = -1;
  tags: Array<any> = [
    {
      name: '行程报告',
      icon: 'iot icon-a-TravelReport4',
      appApplyPermission: 'report_drive',
      component: DriveReportComponent
    },
    {
      name: '行程明细',
      icon: 'iot icon-a-TravelReport4',
      appApplyPermission: 'report_trip',
      component: TravelReportComponent
    },
    {
      name: '停留明细',
      icon: 'iot icon-a-TravelReport3',
      appApplyPermission: 'report_stay',
      component: StopsReportComponent
    },
    {
      name: '超速报告',
      icon: 'iot icon-a-TravelReport2',
      appApplyPermission: 'report_speeding',
      component: SpeedingReportComponent
    },
    {
      name: '自定义报告',
      icon: 'iot icon-a-TravelReport',
      appApplyPermission: 'report_custom',
      component: CustomReportComponent,
      className: 'menu-hei'
    }
  ];

  constructor(private applyPermissionService: ApplyPermissionService) {}

  ngOnInit() {
    // this.updateTabs();
    // if (this.tags.length > 0) {
    //   this.changeTabIndex(0);
    // }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabs();
      this.changeTabIndex(this.tags.length ? 0 : -1);
    }, 200);
  }

  changeTabIndex(i: number) {
    this.tagIndex = i;
  }

  updateTabs() {
    const widgets = this.applyPermissionService.getPageWidgets();
    this.tags = this.tags.filter((item) => {
      return widgets.some((widget) => widget.id === item.appApplyPermission && widget.authorised);
    });
  }
}
