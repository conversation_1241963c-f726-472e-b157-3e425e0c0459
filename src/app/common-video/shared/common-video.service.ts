import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { WebApiResultResponse } from '@core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class CommonVideoService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');
  constructor(private http: HttpClient) {
    super();
  }

  // 添加POI
  addPOI(params: any): Observable<any> {
    const url = 'glcrm-track-api/v1/api/poi/add';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 删除POI
  delPOI(id: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/poi/del?id=${id}`;
    return this.http.post(url, id).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取单个兴趣点
  getPOI(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/poi/poi?id=${params.id}&vehicleId=${params.vehicleId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 编辑POI
  editPOI(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/poi/edit`;
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取兴趣点列表
  POIpage(params: any): Observable<any> {
    // tslint:disable-next-line:max-line-length
    const url = `glcrm-track-api/v1/api/poi/page?pageIndex=${params.pageIndex}&pageSize=${params.pageSize}&vehicleId=${params.vehicleId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取单个兴趣点
  POIsingle(id: any): Observable<any> {
    // tslint:disable-next-line:max-line-length
    const url = `glcrm-track-api/v1/api/poi/poi?id=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 车辆信息列表
  vehicleAndDevice(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/vehicleAndDevice`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取车辆详情
  vehicleDetail(id: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取实时轨迹
  getRealTiemTrack(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/track/realTime/track`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取全部设备定位信息
  getAllDevicesPosition(params: any): Observable<any> {
    // const url = `glcrm-vehicle-api/v1/api/vehicle/vehicleAndDevice/${params.pageIndex}/${params.pageSize}`;
    const url = `glcrm-track-api/v1/api/position/queryVehicleLocation`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备定位信息
  equipmentPosition(id: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/device?imei=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取多个设备定位
  batchPosition(arr: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/batch-devices?imeiList=${arr}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 全局定位
  globalPosition(): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/all-devices`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备绑定的围栏列表
  getFenceListByDeviceId(id: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/fencePage/viFenceDeviceDetail?deviceId=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 单个定位监控围栏详情查询
  fenceDetail(params: any): Observable<any> {
    const url = `glcrm-fence-api/v1/api/fencePage/detail?fenceId=${params}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取历史预警列表
  getAlertList(params: any): Observable<any> {
    const url = `glcrm-alert-api/v1/api/fence/detail/${params}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取设备轨迹
  getTrack(params: any): Observable<any> {
    // const url = `glcrm-track-api/v1/api/track/getSrcTrackByIMEI`;
    const url = `glcrm-track-api/v1/api/track/getSrcTrackByWirelessIMEI`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备报警的轨迹点
  getAlertTrack(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/track/getPointAlert`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备轨迹点（包含报警点）
  getLocations(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/track/getWirelessPoint`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 导出某个时间段的轨迹点
  exportLocations(entity: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = `glcrm-track-api/v1/api/track/export`;
    return this.http
      .post(url, entity, {
        headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
        responseType: 'blob',
        observe: 'response'
      })
      .pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 定位信息-获取总条数
  getAllVehicle(params: any): Observable<any> {
    // tslint:disable-next-line:max-line-length
    const url = `glcrm-vehicle-api/v1/api/vehicle/vehicleAndDeviceCount`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取8小时token
  getToken(params: any): Observable<any> {
    // tslint:disable-next-line:max-line-length
    const url = `https://uc-identity-fat.lunz.cn/connect/token`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取分享得token
  getShareToken(params: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/getToken`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取报警
  getAlert(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/isAlert/${params.vehicleID}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 获取设备事件轨迹报警点
   * @param deviceNo
   * @param queryTime
   * @param type
   */
  getDeviceEventTrackPointList(deviceNo: string, queryTime: Array<{ start: number; end: number }>, types: number[]) {
    const params = {
      deviceNo,
      queryTime,
      types
    };
    const url = `glcrm-alert-api/v1/api/alert-history/alert/selectTrackAlertList`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 获取围栏事件轨迹报警点
   * @param deviceNo
   * @param queryTime
   * @param types
   * @returns
   */
  getGeoFenceEventTrackPointList(deviceNo: string, queryTime: Array<{ start: number; end: number }>, types: number[]) {
    const params = {
      deviceNo,
      queryTime,
      types
    };
    const url = `glcrm-alert-api/v1/api/fence/trackAlertList`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 获取基本事件轨迹报警点
   * @param deviceNo
   * @param queryTime
   * @param types
   * @returns
   */
  getBaseEventTrackPointList(deviceNo: string, queryTime: Array<{ start: number; end: number }>, types: number[]) {
    const params = {
      deviceNo,
      queryTime,
      types
    };
    const url = `glcrm-alert-api/v1/api/event/getTrackEventList`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
