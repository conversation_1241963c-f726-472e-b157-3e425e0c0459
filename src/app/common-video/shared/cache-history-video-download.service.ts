import { Injectable } from '@angular/core';

import { NzNotificationService } from 'ng-zorro-antd/notification';
import { TranslateService } from '@ngx-translate/core';
import { NzNotificationRefData } from '@app/shared/models/video';

@Injectable({
  providedIn: 'root'
})
export class CacheHistoryVideoDownloadService<T> {
  downloadVideoList: NzNotificationRefData[] = [];

  constructor(private notification: NzNotificationService, private translate: TranslateService) {}

  add(data: T): NzNotificationRefData {
    const item = this.notification.blank(
      '',
      this.translate.instant('文件正在下载中，请不要在浏览器中关闭本系统，下载完成后，请在浏览器的下载中心查阅文件'),
      { nzDuration: 0, nzPlacement: 'bottomRight', nzStyle: { background: '#ffb822', color: '#fff', 'padding-top': 0 } }
    ) as NzNotificationRefData;
    item.data = data;
    this.downloadVideoList.push(item);
    item.onClose.subscribe((isUserClose) => (isUserClose ? this.close(item) : null));
    return item;
  }

  /**
   * 关闭弹窗,并删除当前视频信息
   * @param item
   */
  remove(item: NzNotificationRefData) {
    const index = this.close(item);
    if (index > -1) {
      this.downloadVideoList.splice(index, 1);
    }
  }

  /**
   * 用户关闭下载提醒弹窗,不删除当前视频信息,保留进度
   */
  close(item: NzNotificationRefData): number | undefined {
    const index = this.downloadVideoList.findIndex((v) => v.messageId === item.messageId);
    if (index === -1) {
      return index;
    }
    this.notification.remove(item.messageId);
    return index;
  }
}
