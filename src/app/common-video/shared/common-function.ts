import CryptoJS from 'crypto-js';
/**
 *
 *
 * @export
 * @param {string} value 加密字符
 * @return {*}  {string} 解密后字符
 */
export function dryptoFun(value: string): string {
  const key = CryptoJS.enc.Utf8.parse('DFaV92uNFdrc0R3HbMSnxw==');
  const iv = CryptoJS.enc.Utf8.parse('1954682168745975');
  const tmpAES = CryptoJS.AES.decrypt(value, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Utf8.stringify(tmpAES);
}
