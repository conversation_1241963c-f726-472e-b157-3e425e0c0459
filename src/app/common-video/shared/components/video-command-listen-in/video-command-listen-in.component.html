<div class="waveWrapper waveAnimation">
  <div class="waveWrapperInner bgMiddle">
    <div class="wave waveMiddle"></div>
  </div>
</div>

<div class="container">
  <div class="water1"></div>
  <div class="water2"></div>
  <div class="water3"></div>
  <div class="water4"></div>
  <div class="white">
    <img src="/assets/media/app/img/video-command/listen-in.svg" />
  </div>
</div>

<div class="info">
  <div>
    {{ vehicle.plateNumber }}
  </div>

  <nz-select
    nzShowSearch
    [nzPlaceHolder]="'请选择通道号' | translate"
    [nzDisabled]="[VideoConnectStatus.Sending, VideoConnectStatus.SendingSuccessful].includes(connectStatus)"
    [(ngModel)]="channel"
    (ngModelChange)="changeChannel()"
  >
    <nz-option *ngFor="let item of channels" [nzValue]="item.channel" [nzLabel]="item.camera"></nz-option>
  </nz-select>

  <div *ngIf="connectStatus === VideoConnectStatus.Sending" class="gray_text6" translate>指令发送中，请耐心等待…</div>

  <ng-container *ngIf="connectStatus === VideoConnectStatus.SendingSuccessful">
    <div>{{ dateTime }}</div>
    <div class="c-999" translate>通话时长</div>
  </ng-container>
  <div *ngIf="connectStatus === VideoConnectStatus.Timeout" class="c-666" translate>长时间未接通，指令下发失败</div>
</div>

<div class="btns">
  <button
    *ngIf="connectStatus === VideoConnectStatus.Initial"
    nz-button
    nzType="primary"
    [nzLoading]="this.loading"
    (click)="changVoiceStatus(VideoConnectStatus.Sending)"
  >
    {{ '开始' | translate }}
  </button>
  <button
    *ngIf="connectStatus === VideoConnectStatus.Sending"
    nz-button
    (click)="changVoiceStatus(VideoConnectStatus.Timeout)"
  >
    {{ '取消' | translate }}
  </button>
  <button
    *ngIf="connectStatus === VideoConnectStatus.SendingSuccessful"
    nz-button
    nzDanger
    (click)="changVoiceStatus(VideoConnectStatus.Initial)"
  >
    {{ '结束' | translate }}
  </button>
  <ng-container *ngIf="connectStatus === VideoConnectStatus.Timeout">
    <button nz-button nzType="primary" (click)="changVoiceStatus(VideoConnectStatus.Sending)">
      {{ '重新发送' | translate }}
    </button>

    <button nz-button (click)="changVoiceStatus(VideoConnectStatus.Initial)">
      <!-- 即结束 -->
      {{ '取消' | translate }}
    </button>
  </ng-container>
</div>
<video id="audio-player" hidden></video>
