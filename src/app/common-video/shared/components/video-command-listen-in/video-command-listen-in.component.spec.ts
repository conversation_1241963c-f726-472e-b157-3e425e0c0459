import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VideoCommandListenInComponent } from './video-command-listen-in.component';

describe('VideoCommandListenInComponent', () => {
  let component: VideoCommandListenInComponent;
  let fixture: ComponentFixture<VideoCommandListenInComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [VideoCommandListenInComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VideoCommandListenInComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
