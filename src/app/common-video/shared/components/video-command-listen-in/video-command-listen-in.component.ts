import { Component, Input, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Observable, of } from 'rxjs';
import { catchError, map, finalize } from 'rxjs/operators';

import flvjs from 'flv.js';

import { TranslateService } from '@ngx-translate/core';
import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { VideoConnectStatus, VehicleMonitor, LiveControl, Camera } from '@app/shared/models/video';
import { VideoApiService } from '@app/shared/services/video-api.service';

@Component({
  selector: 'app-video-command-listen-in',
  templateUrl: './video-command-listen-in.component.html',
  styleUrls: ['./video-command-listen-in.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoCommandListenInComponent implements OnInit {
  @Input() vehicle: VehicleMonitor;

  log: Logger;
  translates = {
    请选择通道号: ''
  };

  flv_URL: string; // wss url
  VideoConnectStatus = VideoConnectStatus;
  connectStatus = this.VideoConnectStatus.Initial;

  dateTimeInterval: any; // Interval
  dateTime: string; // 计时器

  loading: boolean = false;
  player: any;

  channel: string;

  get channels(): Camera[] {
    return this.vehicle && Array.isArray(this.vehicle.deviceCameraVos) ? this.vehicle.deviceCameraVos : [];
  }

  constructor(
    private cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private videoApiService: VideoApiService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnDestroy(): void {
    if (this.connectStatus === this.VideoConnectStatus.SendingSuccessful && this.channel) {
      this.stopIntercom();
      this.channel = undefined;
    }
    this.clearDateTimeInterval();
    this.destroyPlayer();
    this.connectStatus = VideoConnectStatus.Initial;
    this.cd.markForCheck();
  }

  changeChannel() {
    this.changVoiceStatus(VideoConnectStatus.Initial);
  }

  changVoiceStatus(connectStatus: VideoConnectStatus) {
    if (!this.channel && connectStatus !== this.VideoConnectStatus.Initial) {
      this.log.warn(this.translates['请选择通道号']);
      return;
    }
    this.connectStatus = connectStatus;
    switch (connectStatus) {
      case this.VideoConnectStatus.Initial:
        this.stopIntercom();
        this.clearDateTimeInterval();
        this.destroyPlayer();
        break;
      case this.VideoConnectStatus.Sending:
        this.startIntercom().subscribe(() => {
          if (this.connectStatus !== this.VideoConnectStatus.Sending) {
            return;
          }
          if (!this.flv_URL) {
            this.changVoiceStatus(this.VideoConnectStatus.Timeout);
            return;
          }
          this.initPlayer();
          this.changVoiceStatus(this.VideoConnectStatus.SendingSuccessful);
        });
        break;
      case this.VideoConnectStatus.SendingSuccessful:
        this.clearDateTimeInterval();
        this.startDateTimeInterval();
        break;
      case this.VideoConnectStatus.Timeout:
        break;
    }
    this.cd.markForCheck();
  }

  initPlayer() {
    this.player = flvjs.createPlayer(
      {
        type: 'flv',
        url: this.flv_URL,
        isLive: true,
        hasAudio: true,
        hasVideo: true
      },
      { enableStashBuffer: false, autoCleanupSourceBuffer: true }
    );
    const video = document.getElementById('audio-player') as HTMLVideoElement;
    video.autoplay = true;
    this.player.attachMediaElement(video);
    this.player.load();
    this.player.volume = 1;
    this.player.muted = false;
    this.player.on(flvjs.Events.LOADING_COMPLETE, () => {
      this.changVoiceStatus(VideoConnectStatus.Timeout);
    });
    this.player.on(flvjs.Events.ERROR, (errType: any, errDetail: any) => {
      // errType是 NetworkError时，
      // 对应errDetail有：Exception、HttpStatusCodeInvalid、ConnectingTimeout、EarlyEof、UnrecoverableEarlyEof
      // errType是 MediaError时，对应errDetail是MediaMSEError
      console.error('flvjs.Events.ERROR', errType, errDetail);
    });
  }

  destroyPlayer() {
    if (this.player) {
      this.player.pause();
      this.player.unload();
      this.player.detachMediaElement();
      this.player.destroy();
    }
    this.player = null;
  }

  // 计时器
  startDateTimeInterval() {
    let sec = 0;
    this.dateTimeInterval = setInterval(() => {
      sec++;
      let date = new Date(0, 0);
      date.setSeconds(sec);
      let h = date.getHours(),
        m = date.getMinutes(),
        s = date.getSeconds();
      this.dateTime = `${h}:${m}:${s}`;
      this.cd.markForCheck();
    }, 1000);
  }

  clearDateTimeInterval() {
    this.dateTimeInterval && clearInterval(this.dateTimeInterval);
    this.dateTimeInterval = null;
    this.dateTime = '';
  }

  startIntercom(): Observable<void> {
    this.loading = true;
    return this.videoApiService.startPlayLiveOpen(this.vehicle.deviceNumber, this.channel, 1).pipe(
      map((res) => {
        if (!res || !res.success || !res.data || !res.data.wssFlv || !res.data.httpsFlv) {
          this.flv_URL = '';
          throw new Error('获取flv_URL地址失败');
        }
        this.flv_URL = res.data.wssFlv || res.data.httpsFlv;
        return;
      }),
      finalize(() => (this.loading = false)),
      catchError(() => of(null))
    );
  }

  stopIntercom() {
    this.videoApiService.liveControl(this.vehicle.deviceNumber, this.channel, LiveControl.CloseAudioVideo).subscribe();
  }
}
