import { Component, OnInit, ChangeDetectionStrategy, Input, ViewChild } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { VehicleMonitor, VideoConnectStatus } from '@app/shared/models/video';

import { VideoCommandSnapPhotoComponent } from '../video-command-snap-photo/video-command-snap-photo.component';
import { VideoCommandIntercomComponent } from '../video-command-intercom/video-command-intercom.component';
import { VideoCommandListenInComponent } from '../video-command-listen-in/video-command-listen-in.component';

enum VideoCommandType {
  SnapPhone,
  Intercom,
  ListenIn
}

@Component({
  selector: 'app-video-command-modal',
  templateUrl: './video-command-modal.component.html',
  styleUrls: ['./video-command-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoCommandModalComponent implements OnInit {
  @Input() vehicle: VehicleMonitor;

  VideoCommandType = VideoCommandType;
  nzSelectedIndex: VideoCommandType = VideoCommandType.SnapPhone;

  @ViewChild(VideoCommandIntercomComponent) videoCommandIntercomComponent: VideoCommandIntercomComponent;
  @ViewChild(VideoCommandListenInComponent) videoCommandListenInComponent: VideoCommandListenInComponent;

  constructor(public activeModal: BsModalRef) {}

  ngOnInit() {}

  changeTabs(e: any) {
    const index: VideoCommandType = e.index;
    if (index === this.nzSelectedIndex) {
      return;
    }
    this.nzSelectedIndex = index;
    switch (this.nzSelectedIndex) {
      case VideoCommandType.SnapPhone:
        this.videoCommandIntercomComponent.ngOnDestroy();
        this.videoCommandListenInComponent.ngOnDestroy();
        break;
      case VideoCommandType.Intercom:
        this.videoCommandListenInComponent.ngOnDestroy();
        break;
      case VideoCommandType.ListenIn:
        this.videoCommandIntercomComponent.ngOnDestroy();
        break;
      default:
        break;
    }
  }
}
