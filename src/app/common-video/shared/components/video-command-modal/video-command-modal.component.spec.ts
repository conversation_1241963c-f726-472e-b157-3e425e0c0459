import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VideoCommandModalComponent } from './video-command-modal.component';

describe('VideoCommandModalComponent', () => {
  let component: VideoCommandModalComponent;
  let fixture: ComponentFixture<VideoCommandModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [VideoCommandModalComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VideoCommandModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
