import { Component, OnInit, Input, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { of, timer } from 'rxjs';
import { catchError, finalize, map, switchMap } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { Camera, SnapParams, VehicleMonitor } from '@app/shared/models/video';
import { VideoApiService } from '@app/shared/services/video-api.service';

@Component({
  selector: 'app-video-command-snap-photo',
  templateUrl: './video-command-snap-photo.component.html',
  styleUrls: ['./video-command-snap-photo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoCommandSnapPhotoComponent implements OnInit {
  @Input() vehicle: VehicleMonitor;

  log: Logger;
  translates = {
    '指令发送成功!': '',
    '指令发送失败!': ''
  };

  form: FormGroup;
  saving = false;

  get channels(): Camera[] {
    return this.vehicle && Array.isArray(this.vehicle.deviceCameraVos) ? this.vehicle.deviceCameraVos : [];
  }

  constructor(
    public activeModal: BsModalRef,
    public cd: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private loggerFactory: LoggerFactory,
    private videoApiService: VideoApiService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnInit() {}

  submit() {
    if (this.saving) {
      return;
    }
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const params: SnapParams = {
      imei: this.vehicle.deviceNumber,
      channelId: this.form.getRawValue().channel,
      brightness: 255,
      chroma: 255,
      contrast: 127,
      quality: 10,
      resolution: 255,
      saturation: 127
    };
    this.videoApiService
      .snap(params)
      .pipe(
        map((res) => {
          if (res && res.success) {
            this.log.success(this.translates['指令发送成功!']);
            return true;
          }
          // throw new Error('指令发送失败!');
          throw new Error(res.message);
        }),
        catchError(() => {
          this.log.error(this.translates['指令发送失败!']);
          return of(false);
        }),
        switchMap((isSuccess) => (isSuccess ? timer(2 * 60 * 1000) : timer(0))),
        finalize(() => {
          this.saving = false;
          this.cd.markForCheck();
        })
      )
      .subscribe();
  }

  reset() {
    this.form.reset();
  }

  buildForm() {
    this.form = this.formBuilder.group({
      channel: [null, [Validators.required]]
    });
  }
}
