<div class="snap-form">
  <div class="snap-form-body">
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="6" nzRequired nzFor="channel">
          {{ '通道号' | translate }}
        </nz-form-label>
        <nz-form-control [nzSpan]="14" [nzErrorTip]="errorTpl">
          <nz-select formControlName="channel" nzAllowClear nzShowSearch [nzPlaceHolder]="'请选择通道号' | translate">
            <nz-option *ngFor="let item of channels" [nzValue]="item.channel" [nzLabel]="item.camera"></nz-option>
          </nz-select>
          <ng-template #errorTpl let-control>
            {{ '请选择通道号' | translate }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>
      <ul>
        <li translate>每次只能抓拍一张照片，发送指令后，请2分钟后再次发送</li>
        <li translate>请在视频事件模块查看抓拍的照片</li>
      </ul>
    </form>
  </div>

  <div class="snap-form-footer">
    <button type="button" nz-button (click)="reset()">{{ '取消' | translate }}</button>
    <button type="submit" nz-button nzType="primary" [disabled]="saving" (click)="submit()">
      {{ '发送指令' | translate }}
    </button>
  </div>
</div>
