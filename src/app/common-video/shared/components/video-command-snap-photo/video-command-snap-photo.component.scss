:host {
  display: block;
  height: 100%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

.snap-form {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-body {
    flex: 1;
    height: 0;
    overflow-y: auto;

    ul {
      width: 450px;
      margin: 20px auto 0;
      background: #fffbeb;
      border-radius: 4px;
      padding: 10px 26px;
    }

    li {
      margin-bottom: 10px;
      list-style: decimal;
      font-size: 14px;
      font-weight: 500;
      color: #c17c00;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    padding: 20px;
    gap: 8px;
  }
}

:host ::ng-deep {
  .ant-form-item-label > label {
    font-weight: 400;
    color: #575e72;
  }

  .ant-form-explain,
  .ant-form-extra {
    min-height: auto;
    font-size: 12px;
    line-height: 22px;
  }

  .ant-select-selection__rendered {
    line-height: 24px;
  }
}
