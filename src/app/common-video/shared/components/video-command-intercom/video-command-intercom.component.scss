.container {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0px auto;
  font-family: PingFangSC, PingFangSC-Medium;

  .water1 {
    -webkit-animation: wateranimate 12s 9s ease-out infinite;
    animation: wateranimate 12s 9s ease-out infinite;
  }

  .water2 {
    -webkit-animation: wateranimate 12s 6s ease-out infinite;
    animation: wateranimate 12s 6s ease-out infinite;
  }

  .water3 {
    -webkit-animation: wateranimate 12s 3s ease-out infinite;
    animation: wateranimate 12s 3s ease-out infinite;
  }

  .water4 {
    -webkit-animation: wateranimate 12s 0s ease-out infinite;
    animation: wateranimate 12s 0s ease-out infinite;
  }

  .water1,
  .water2,
  .water3,
  .water4 {
    padding: 20%;
    position: absolute;
    left: 30%;
    top: 30%;
    border: 1px solid rgba(192, 200, 255, 1);
    box-shadow: 0 0 120px 30px rgba(192, 200, 255, 1) inset;
    border-radius: 100%;
    z-index: 1;
    opacity: 0;
  }

  .white {
    padding: 20%;
    position: absolute;
    left: 30%;
    top: 30%;
    border: 1px solid rgba(255, 255, 255, 1);
    box-shadow: 0 0 120px 30px rgba(255, 255, 255, 1) inset;
    border-radius: 100%;
    z-index: 2;
    opacity: 1;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

@-webkit-keyframes wateranimate {
  0% {
    -webkit-transform: scale(1);
    opacity: 0.5;
  }

  100% {
    -webkit-transform: scale(3);
    opacity: 0;
  }
}

@keyframes wateranimate {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0.5;
  }

  100% {
    -webkit-transform: scale(3);
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes move_wave {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }

  50% {
    transform: translateX(-25%) translateZ(0) scaleY(0.55);
  }

  100% {
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}

.waveWrapper {
  overflow: hidden;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 100px;
  margin: auto;
}

.waveWrapperInner {
  position: absolute;
  width: 100%;
  overflow: hidden;
  height: 80px;
  top: 90px;
}

.bgMiddle {
  z-index: 1;
  opacity: 0.75;
}

.wave {
  position: absolute;
  left: 0;
  width: 200%;
  height: 100%;
  background-repeat: repeat no-repeat;
  background-position: 0 bottom;
  transform-origin: center bottom;
  background-image: url("/assets/media/app/img/video-command/waveline.svg");
}

.waveMiddle {
  background-size: 50% 120px;
}

.waveAnimation .waveMiddle {
  animation: move_wave 10s linear infinite;
}

.info {
  margin: 20px 0 10px;
  line-height: 25px;
  text-align: center;
  font-weight: 500;
  color: #333333;

  .c-999 {
    font-weight: 400;
    color: #999999;
  }

  .c-666 {
    font-weight: 400;
    color: #666666;
  }
}

.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px 20px;

  button {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}
