import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VideoCommandIntercomComponent } from './video-command-intercom.component';

describe('VideoCommandIntercomComponent', () => {
  let component: VideoCommandIntercomComponent;
  let fixture: ComponentFixture<VideoCommandIntercomComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [VideoCommandIntercomComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VideoCommandIntercomComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
