import { Component, Input, OnDestroy, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Observable, empty, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { VideoConnectStatus, VehicleMonitor, Camera, LiveControl } from '@app/shared/models/video';
import { VideoApiService } from '@app/shared/services/video-api.service';

@Component({
  selector: 'app-video-command-intercom',
  templateUrl: './video-command-intercom.component.html',
  styleUrls: ['./video-command-intercom.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VideoCommandIntercomComponent implements OnInit, OnDestroy {
  @Input() vehicle: VehicleMonitor;

  log: Logger;
  translates = {
    请选择通道号: ''
  };

  flv_URL: string; // wss url
  VideoConnectStatus = VideoConnectStatus;
  connectStatus = VideoConnectStatus.Initial;

  dateTime: string; // 计时器
  dateTimeInterval: number; // Interval

  ws: WebSocket;
  pcmPlayer: any; // 播放设备传来的录音
  recorder: any; // 用来录音
  type = 'pcm';
  bitRate = 16; // 采样位数，支持 8 或 16，默认是16
  sampleRate = 8000; // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  realTimeSendTryChunk: any = null;

  constructor(
    private cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private videoApiService: VideoApiService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngOnDestroy(): void {
    if (this.connectStatus === this.VideoConnectStatus.SendingSuccessful) {
      this.stopIntercom();
    }
    this.clearDateTimeInterval();
    this.destroyWebsockt();
    this.destroyRecorder();
    this.clearPcmPlayer();
    this.connectStatus = VideoConnectStatus.Initial;
    this.cd.markForCheck();
  }

  changeChannel() {
    this.changVoiceStatus(VideoConnectStatus.Initial);
  }

  changVoiceStatus(connectStatus: VideoConnectStatus) {
    this.connectStatus = connectStatus;
    switch (connectStatus) {
      case this.VideoConnectStatus.Initial:
        this.stopIntercom();
        this.clearDateTimeInterval();
        this.destroyWebsockt();
        this.destroyRecorder();
        this.clearPcmPlayer();
        break;
      case this.VideoConnectStatus.Sending:
        this.startIntercom().subscribe(() => {
          if (this.connectStatus !== this.VideoConnectStatus.Sending) {
            return;
          }
          if (!this.flv_URL) {
            this.changVoiceStatus(this.VideoConnectStatus.Timeout);
            return;
          }
          this.initWebsocket();
        });
        break;
      case this.VideoConnectStatus.SendingSuccessful: // wecsoket连接成功开始录音和播放接收到设备的声音
        this.initRecorder();
        this.initPcmPlayer();
        this.clearDateTimeInterval();
        this.startDateTimeInterval();
        break;
      case this.VideoConnectStatus.Timeout: // websocket连接失败或者flv_URL地址获取失败
        this.destroyWebsockt();
        break;
    }
    this.cd.markForCheck();
  }

  // 初始化PCMPlayer
  initPcmPlayer() {
    this.clearPcmPlayer();
    this.pcmPlayer = Recorder.BufferStreamPlayer({
      play: true,
      realtime: true,
      decode: false, // 原始就是pcm,不需要解码
      sampleRate: this.sampleRate,
      onInputError: (errMsg: string, inputIndex: string) =>
        console.log('initPcmPlayer:onInputError', '第' + inputIndex + '次的音频片段input输入出错: ' + errMsg, 1),
      onUpdateTime: () => console.log('initPcmPlayer:onUpdateTime', 'audio is on playing ...'),
      onPlayEnd: () => {
        if (!this.pcmPlayer.isStop) {
          console.log('initPcmPlayer:onInputEronPlayEndror', '没有可播放的数据了，缓冲中 或者 已播放完成');
        }
      },
      transform: (
        pcm: ArrayBuffer,
        sampleRate: number,
        True: (pcm: any, sampleRate: number) => void,
        False: (errMsg: string) => void
      ) => {
        True(new Int16Array(pcm), sampleRate); // 将ws接收的数据转为int16array
        False('pcmPlayer transform fail!!!');
      }
    });
    this.pcmPlayer.start(
      () => console.log('initPcmPlayer:this.pcmPlayer.start success', 'stream已打开[' + this.type + ']，正在播放中'),
      (err: any) => console.log('initPcmPlayer:this.pcmPlayer.start fail', '开始失败：' + err)
    );
  }

  // 清空pcmplay
  clearPcmPlayer() {
    if (this.pcmPlayer) {
      this.pcmPlayer.stop();
    }
    this.pcmPlayer = null;
  }

  // 前端到设备 录音
  initRecorder() {
    this.recorder = Recorder({
      type: this.type,
      bitRate: this.bitRate,
      sampleRate: this.sampleRate,
      // 录音实时回调，大约1秒调用12次本回调，buffers为开始到现在的所有录音pcm数据块(16位小端LE)
      onProcess: (buffers: ArrayBuffer, powerLevel: number, duration: number, sampleRate: number) => {
        this.readyChat(buffers, sampleRate);
      },
      audioTrackSet: {
        deviceId: '',
        groupId: '',
        autoGainControl: true, // 自动增益控制
        echoCancellation: true, // 回声消除
        noiseSuppression: true // 噪声抑制
      }
    });

    this.recorder.open(
      () => {
        this.realTimeSendTryChunk = null;
        // 此处可以立即开始录音，但不建议这样编写，因为open是一个延迟漫长的操作，通过两次用户操作来分别调用open和start是推荐的最佳流程
        this.recorder.start();
      },
      (msg: string, isUserNotAllow: boolean) =>
        console.log('initRecorder this.recorder.open fail!!', 'recorder.open() fail!!!!')
    );
  }

  // 前端向设备发送语音
  readyChat(buffers: any, bufferSampleRate: number) {
    if (!this.ws) {
      return;
    }
    let pcm: any[] = [];
    let pcmSampleRate = 0;
    let chunk = null;
    if (buffers.length > 0) {
      //借用SampleData函数进行数据的连续处理，采样率转换是顺带的，得到新的pcm数据
      chunk = Recorder.SampleData(buffers, bufferSampleRate, this.sampleRate, this.realTimeSendTryChunk);
      //清理已处理完的缓冲数据，释放内存以支持长时间录音，最后完成录音时不能调用stop，因为数据已经被清掉了
      for (let i = this.realTimeSendTryChunk ? this.realTimeSendTryChunk.index : 0; i < chunk.index; i++) {
        buffers[i] = null;
      }
      this.realTimeSendTryChunk = chunk;
      pcm = chunk.data;
      pcmSampleRate = chunk.sampleRate;
      if (pcmSampleRate != this.sampleRate) {
        //除非是onProcess给的bufferSampleRate低于testSampleRate
        throw new Error('不应该出现pcm采样率' + pcmSampleRate + '和需要的采样率' + this.sampleRate + '不一致');
      }
    }
    this.webSocketSend(pcm);
  }

  // 结束录音
  destroyRecorder() {
    if (this.recorder) {
      this.recorder.stop(
        (blob: any, duration: number) => console.log('destroyRecorder', blob, duration),
        (errMsg: string) => console.log('destroyRecorder', errMsg)
      );
      this.recorder.close();
    }
    this.recorder = null;
  }

  // ws初始化
  initWebsocket() {
    this.ws = new WebSocket(this.flv_URL);
    this.ws.binaryType = 'arraybuffer';
    this.ws.onopen = this.webSocketOpen.bind(this);
    this.ws.onmessage = this.webSocketMessage.bind(this);
    this.ws.onerror = this.webSocketError.bind(this);
    this.ws.onclose = (e) => {
      console.log('ws.onclose', e);
      // this.connectStatus === VideoConnectStatus.SendingSuccessful 表示对讲非主动断开,需重连
      const connectStatus =
        this.connectStatus === VideoConnectStatus.SendingSuccessful
          ? VideoConnectStatus.Timeout
          : VideoConnectStatus.Initial;
      this.changVoiceStatus(connectStatus);
      this.ws = null;
    };
  }

  destroyWebsockt() {
    if (this.ws) {
      this.ws.close();
    }
    this.ws = null;
  }

  webSocketOpen(event: any) {
    this.changVoiceStatus(this.VideoConnectStatus.SendingSuccessful);
    console.log('webSocketOpen', event);
  }

  // webSocket发送消息 前端 - 设备
  webSocketSend(data: any) {
    // this.pcmPlayer.input(data); // 测试播放音频
    if (this.ws && data.length > 0) {
      this.ws.send(data);
    }
  }

  //接受服务器消息 设备-前端
  webSocketMessage(event: any) {
    if (!this.pcmPlayer) {
      return;
    }
    // PCMPlayer 播放
    try {
      const buffer = event.data as ArrayBuffer;
      console.log('webSocketMessage', buffer);
      if (buffer.byteLength % 2 === 0) {
        this.pcmPlayer.input(buffer);
      }
    } catch (error) {
      this.log.error(`websocket数据有误`);
    }
  }

  //连接建立失败重连
  webSocketError(e: any) {
    console.log('ws.onerror', e);
    console.log('webSocketError', '连接失败了，正在重连!');
    this.changVoiceStatus(this.VideoConnectStatus.Timeout);
  }

  // 计时器
  startDateTimeInterval() {
    let sec = 0;
    this.dateTimeInterval = window.setInterval(() => {
      sec++;
      let date = new Date(0, 0);
      date.setSeconds(sec);
      let h = date.getHours(),
        m = date.getMinutes(),
        s = date.getSeconds();
      this.dateTime = `${h}:${m}:${s}`;
      this.cd.markForCheck();
    }, 1000);
  }

  clearDateTimeInterval() {
    this.dateTimeInterval && window.clearInterval(this.dateTimeInterval);
    this.dateTimeInterval = null;
    this.dateTime = '';
  }

  startIntercom(): Observable<void> {
    return this.videoApiService.startPlayLiveOpen(this.vehicle.deviceNumber, '36', 1, 2).pipe(
      map((res) => {
        if (!res || !res.success || !res.data || !res.data.wssFlv || !res.data.httpsFlv) {
          this.flv_URL = '';
          this.log.error(res.message);
          throw new Error('获取flv_URL地址失败');
        }
        this.flv_URL = res.data.wssFlv || res.data.httpsFlv;
        return;
      }),
      catchError(() => of(null))
    );
  }

  stopIntercom() {
    this.videoApiService.liveControl(this.vehicle.deviceNumber, '36', LiveControl.CloseIntercom).subscribe();
  }
}
