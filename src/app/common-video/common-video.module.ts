import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';

import { VideoTabsComponent } from './partial/video-tabs/video-tabs.component';
import { LiveVideoComponent } from './partial/live-video/live-video.component';
import { HistoryVideoComponent } from './partial/history-video/history-video.component';
import { VideoCommandModalComponent } from './shared/components/video-command-modal/video-command-modal.component';
import { VideoCommandSnapPhotoComponent } from './shared/components/video-command-snap-photo/video-command-snap-photo.component';
import { VideoCommandIntercomComponent } from './shared/components/video-command-intercom/video-command-intercom.component';
import { VideoCommandListenInComponent } from './shared/components/video-command-listen-in/video-command-listen-in.component';
import { CommonVideoRoutingModule } from './common-video-routing.module';

@NgModule({
  declarations: [
    VideoTabsComponent,
    LiveVideoComponent,
    HistoryVideoComponent,
    VideoCommandModalComponent,
    VideoCommandSnapPhotoComponent,
    VideoCommandIntercomComponent,
    VideoCommandListenInComponent
  ],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, TranslateModule, SharedModule, CommonVideoRoutingModule],
  entryComponents: [VideoCommandModalComponent]
})
export class CommonVideoModule {}
