import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';

import { VideoTabsComponent } from './partial/video-tabs/video-tabs.component';

const routes: Routes = [
  { path: 'live-video', component: VideoTabsComponent, data: { title: '实时视频' } },
  { path: 'playblack', component: VideoTabsComponent, data: { title: '历史视频' } }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class CommonVideoRoutingModule {}
