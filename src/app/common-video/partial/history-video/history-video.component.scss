:host {
  display: flex;
  width: 100%;
  height: 100%;
}

.history-left {
  flex: 1;
  height: 100%;

  .video-item {
    width: 100%;
    height: 100%;
    padding: 8px;
  }

  .video-item-content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    border: 1px solid #e6e8ec;
    border-radius: 3px;
  }

  .video-item-title {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 3px 5px;
    color: #a6abb4;

    h6 {
      margin-right: 3px;
      font-size: 12px;
      color: #091832;
    }

    span {
      margin-right: 3px;
      color: #9da3ad;
    }

    p {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.history-right {
  width: 340px;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px 8px 8px 0;

  .history-right-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 14px;
    font-size: 14px;
    color: #454c62;
    background-color: #e9edf4;
    border-radius: 4px;
  }

  .history-right-body {
    flex: 1;
    max-height: calc(100vh - 160px);
    padding-top: 8px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .spining {
      padding-top: 30px;
    }
  }

  .item-container {
    margin-bottom: 8px;
    border-radius: 4px;
    border: 1px solid #fff;
    overflow: hidden;
    cursor: pointer;

    &:hover {
      border-color: #95a0b6;

      .item-header {
        background-color: #95a0b6;
        color: #fff;
      }
    }

    &.active {
      border-color: #353f52;

      .item-header {
        background-color: #353f52;
        color: #fff;
      }
    }

    .item-header {
      padding: 4px 12px;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      background: #f4f7fb;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .item-body {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 12px;
      color: #454c62;

      &-left {
        flex: 1;
        margin-right: 8px;
      }

      &-right {
        width: 45px;

        img {
          width: 35px;
        }
      }
    }

    .info {
      display: flex;
      align-items: center;
      padding: 8px 0;

      &-label {
        min-width: 70px;
      }

      &-value {
        flex: 1;
      }
    }
  }
}

.no-video {
  flex: 1;
  height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-image: url(/assets/media/app/img/video/bg.png);
  background-size: 100% 100%;
  color: #99a0ac;

  i {
    font-size: 25px;
    margin-bottom: 8px;
  }

  p {
    font-size: 12px;
  }
}

.no-data {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 210px;
  }
}
