import { Component, OnInit, ChangeDetectorRef, OnDestroy, Input, AfterViewInit } from '@angular/core';
import { timer, Observable, of, combineLatest } from 'rxjs';
import { catchError, throttleTime, finalize, map, mergeMap, retryWhen, switchMap, tap } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { DownloadFile, DownloadService } from '@app/shared/services/download.service';
import { VideoApiService } from '@app/shared/services/video-api.service';
import {
  HistoryVideo,
  HistroyVideoUrlParams,
  UploadControlParams,
  UploadProgressVideoParams,
  UploadVideoParams,
  VideoInfo
} from '@app/shared/models/video';
import { CacheHistoryVideoDownloadService } from '@app/common-video/shared/cache-history-video-download.service';
import { sumBy } from 'lodash';

import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-history-video',
  templateUrl: './history-video.component.html',
  styleUrls: ['./history-video.component.scss']
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class HistoryVideoComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input()
  get videoInfo(): VideoInfo | undefined {
    return this._videoInfo;
  }
  set videoInfo(val: VideoInfo | undefined) {
    this.playVideoInfo = undefined;
    this._videoInfo = val;
    this.getVideoList();
  }

  log: Logger;
  accountSetting = accountSetting;
  translates = {
    下载失败: '',
    下载成功: '',
    暂无数据: '',
    通道号: '',
    '有正在下载的视频,应答流水号': '',
    '网关路由未找到设备路由(设备未注册)': ''
  };

  loading = false;
  videoList: HistoryVideo[] = [];
  playVideoInfo: VideoInfo | undefined;

  _videoInfo: VideoInfo | undefined;

  constructor(
    private cacheHistoryVideoDownloadService: CacheHistoryVideoDownloadService<HistoryVideo>,
    private cd: ChangeDetectorRef,
    private downloadService: DownloadService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private nzMessage: NzMessageService,
    private videoApiService: VideoApiService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  ngAfterViewInit(): void {
    this.cd.detectChanges();
  }

  ngOnDestroy() {}

  changePlayVideoInfo(item: HistoryVideo) {
    if (this.playVideoInfo && this.playVideoInfo.uid === item.uid) {
      this.playVideoInfo = undefined;
      return;
    }
    this.playVideoInfo = {
      ...this.videoInfo,
      uid: item.uid,
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration
    };
  }

  /**
   * 获取视频列表
   * 每5秒轮训一次，最多6次也就是半分钟
   */
  getVideoList() {
    if (!this.videoInfo) {
      this.loading = false;
      this.videoList = [];
      return;
    }
    const params: HistroyVideoUrlParams = {
      imei: this.videoInfo.deviceNumber,
      channel: this.videoInfo.channel,
      startTime: this.videoInfo.startTime,
      endTime: this.videoInfo.endTime,
      rate: 0,
      saveType: 0,
      type: 3
    };
    this.loading = true;
    this.videoList = [];
    this.videoApiService
      .getHistroyVideoListOpen(params)
      .pipe(
        map((res) => {
          console.log(res);
          if (!res || res.code !== '00000' || !res.data) {
            this.translate.get(res.msg).subscribe((data: string) => {
              this.nzMessage.create('warning', data);
            });
            throw this.translates['暂无数据'];
          }
          return res.data.medias;
        }),
        catchError(() => {
          return of([]);
        }),
        finalize(() => {
          this.loading = false;
          this.cd.markForCheck();
        })
      )
      .subscribe((vidoeList: HistoryVideo[]) => {
        this.videoList = vidoeList.map((item: HistoryVideo) => {
          item.uid = `histroyVideo-${this.videoInfo.plateNumber}-${this.videoInfo.channel}-${item.startTime}`;
          item.title = `${this.translates['通道号']}${this.videoInfo.channel}`;
          item.isDownloading = false;
          item.progress = 0;
          item.start = item.startTime;
          item.end = item.endTime;
          /** 时长,单位毫秒秒 */
          item.duration = Number(item.endTime) - Number(item.startTime);
          const millisecond = item.duration % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
          const hours = Math.floor(millisecond / (3600 * 1000));
          const minutes = Math.floor((millisecond % (3600 * 1000)) / (60 * 1000));
          const seconds = Math.round(((millisecond % (3600 * 1000)) % (60 * 1000)) / 1000);
          item.time = `${hours}h ${minutes}min ${seconds}s`;
          const progressItem = this.cacheHistoryVideoDownloadService.downloadVideoList.find((nzNotificationRefData) => {
            if (nzNotificationRefData.data) {
              const cacheItem = nzNotificationRefData.data as HistoryVideo;
              return cacheItem.uid === item.uid;
            }
            return false;
          });
          if (progressItem) {
            item = progressItem.data as HistoryVideo;
            console.log('progressItem', item, progressItem.data);
          }
          return item;
        });
      });
  }

  download(e: Event, item: HistoryVideo) {
    e.stopPropagation();
    item.isDownloading = true;
    item.progress = 0;
    const params: UploadVideoParams = {
      imei: this.videoInfo.deviceNumber,
      channel: item.channel,
      startTime: item.startTime,
      endTime: item.endTime,
      type: 3,
      rate: 0,
      saveType: 0,
      condition: 7,
      warnTagFlag: 0
    };
    this.videoApiService
      .uploadVideoOpen(params)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe(
        (res: any) => {
          if (!res || !res.data) {
            item.isDownloading = false;
            item.progress = 0;
            if (item.nzNotificationRefData) {
              this.cacheHistoryVideoDownloadService.remove(item.nzNotificationRefData);
            }
            this.log.error(
              res.msg && res.msg.indexOf('有正在下载的视频,应答流水号') > -1
                ? this.translates['有正在下载的视频,应答流水号']
                : this.translates['下载失败']
            );
            return;
          }
          item.nzNotificationRefData = this.cacheHistoryVideoDownloadService.add(item);
          setTimeout(() => {
            this.getProgress(item);
          }, 3000);
        },
        (err) => {
          item.isDownloading = false;
          item.progress = 0;
        }
      );
  }

  getProgress(item: any) {
    this.getUploadProgress(item)
      .pipe(switchMap((urlList: string[]) => this.getDownloadProgress(urlList)))
      .subscribe((res: Array<DownloadFile>) => {
        if (!Array.isArray(res) || res.length < 1) {
          item.isDownloading = false;
          item.progress = 0;
          if (item.nzNotificationRefData) {
            this.cacheHistoryVideoDownloadService.remove(item.nzNotificationRefData);
          }
          this.log.error(this.translates['下载失败']);
          this.cd.markForCheck();
          return;
        }
        console.log('下载', res);
        const isDownloaded = res.every((progressItem) => progressItem.type === 'Done');
        // ------- 下载完成 -------
        if (isDownloaded) {
          item.isDownloading = false;
          item.progress = 0;
          if (item.nzNotificationRefData) {
            this.cacheHistoryVideoDownloadService.remove(item.nzNotificationRefData);
          }
          const isAllfail = res.every((progressItem) => !progressItem.data); // 所有下载都失败（其中一个成功算成功）
          isAllfail ? this.log.error(this.translates['下载失败']) : this.log.success(this.translates['下载成功']);
          this.cd.markForCheck();
          return;
        }
        // -------- 下载进度 ---------
        // 计算多个文件总的下载进度
        let progress = 0;
        res.forEach((progressItem) => {
          progress += progressItem.progress;
        });
        // 下载进度占50%
        item.progress = Math.floor(progress / res.length / 2 + 50);
        console.log('下载进度', progress, item.progress, this.videoList);
        this.cd.markForCheck();
      });
  }

  /**
   * 获取视频上传进度
   * @param item
   * @returns
   */
  getUploadProgress(item: HistoryVideo): Observable<string[]> {
    let perRetryTime = 5000; // 每次请求的延迟时间5秒
    const maxReryTime = 60; // 进度一直不变,尝试60次,即5分钟后，则失败,返回空数组
    let retryTime = 0;
    let currProgress = 0;
    let currFileSize = 0; // 接口返回得大小
    const totalSize = item.fileSize; // 列表得文件大小
    const params: UploadProgressVideoParams = {
      imei: this.videoInfo.deviceNumber,
      channel: item.channel,
      startTime: item.startTime,
      endTime: item.endTime,
      type: 3
    };
    return this.videoApiService.getUploadProgressOpen(params).pipe(
      map((res) => {
        if (!res || !res.data || retryTime >= maxReryTime) {
          return [];
        }
        if (Array.isArray(res.data.urls)) {
          item.progress = 1;
          // 获取到url,上传完成
          return res.data.urls;
        }
        // let progress = 0
        // 前端计算进度值 用列表得值/proxy接口返回得全部得size
        const currentFileSize = sumBy(res.data?.files, 'fileSize');
        if (currentFileSize != 0) {
          res.data.progress = Number((currentFileSize / totalSize).toFixed(2));
        } else {
          res.data.progress = 0; // 0.99;
        }
        // 计算上传进度超过了100%，可以强制设置为99%，直至result为true或urls返回资源链接
        // 或者进度正好是1 但是url还没有值也是0.99
        if (res.data.progress > 1 || (res.data.progress === 1 && !res.data.urls)) {
          res.data.progress = 0.99;
        }
        if (res.data.result !== null) {
          if (res.data.result) {
            // 成功了
            res.data.progress = 1;
          } else {
            // 失败了
            res.data.progress = 0;
            return [];
          }
        }

        // 获取上传进度,进度占50%
        item.progress = Math.floor((res.data.progress * 100) / 2);
        item.progress = item.progress > 50 ? 50 : item.progress;
        console.log(' res.data.progress', res.data.progress, 'item.progress:', item.progress);
        if (currProgress === res.data.progress) {
          // 如何size一直增大 就一直重复调用
          if (currFileSize > 0 && this.getSize(res.data?.files) > currFileSize) {
            retryTime = 0;
            currProgress = res.data.progress;
          } else {
            retryTime = retryTime + 1;
          }
        } else {
          retryTime = 0;
          currProgress = res.data.progress;
        }
        currFileSize = this.getSize(res.data?.files);
        this.cd.markForCheck();
        throw res.data.progress;
      }),
      retryWhen((err$) => {
        return err$.pipe(
          mergeMap((progress, i) => {
            // 进度是否一直不变
            return timer(perRetryTime);
          })
        );
      })
    );
  }

  /**计算size */
  getSize(files: any[]) {
    return files !== null ? sumBy(files, 'fileSize') : 0;
  }

  /**
   * 获取视频下载进度
   * @param urlList
   * @returns
   */
  getDownloadProgress(urlList: string[]): Observable<Array<DownloadFile>> {
    urlList = urlList.filter((url) => url.startsWith('https') || url.startsWith('http'));
    if (urlList.length === 0) {
      return of([]);
    }
    const list$ = urlList.map((url) => {
      const lastIndex = url.lastIndexOf('/') + 1;
      return this.downloadService.getBlobFileByUrl(url, url.substring(lastIndex), true);
    });
    return combineLatest(list$).pipe(throttleTime(1000, undefined, { leading: true, trailing: true }));
  }

  /**
   * 控制文件上传
   * @param control 0:暂停 1:继续 2:取消
   */
  controlUpload(control: 0 | 1 | 2 = 2) {
    const params: UploadControlParams = {
      control: '2',
      imei: this.videoInfo.deviceNumber,
      packageNo: 0
    };
    this.videoApiService.controlUploadOpen(params).subscribe();
  }
}
