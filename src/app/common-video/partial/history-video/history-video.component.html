<div class="history-left">
  <div class="video-item video-item-1">
    <div class="video-item-content">
      <div class="video-item-title">
        <!-- <h6 *ngIf="videoInfo">{{ videoInfo.plateNumber || '' }}</h6> -->
        <!-- <span>-</span> -->
        <!-- <p *ngIf="videoInfo">{{ videoInfo.camera || '' }}</p> -->
      </div>
      <app-open-video *ngIf="playVideoInfo; else noVideo" [isLive]="false" [videoInfo]="playVideoInfo"></app-open-video>
    </div>
  </div>
</div>
<div class="history-right">
  <div class="history-right-header">
    <div class="title" translate>录像列表</div>
    <div class="total">
      <span translate>总共</span>
      ：
      <span class="text">{{ videoList.length }}</span>
    </div>
  </div>

  <div class="history-right-body">
    <nz-spin class="spining" *ngIf="loading; else listTpl"></nz-spin>
    <ng-template #listTpl>
      <div class="no-data" *ngIf="videoList.length == 0">
        <img src="/assets/font/noDate.png" />
        <span class="no-data-title" translate>暂无数据</span>
      </div>
      <div
        class="item-container"
        *ngFor="let item of videoList; let index = index"
        [ngClass]="{ active: playVideoInfo && item.uid === playVideoInfo.uid }"
        (click)="changePlayVideoInfo(item)"
      >
        <div class="item-header">
          {{ item.title || '-' }}
        </div>
        <div class="item-body">
          <div class="item-body-left">
            <div class="info">
              <div class="info-label">{{ '时长' | translate }}：</div>
              <div class="info-value">
                {{ item.time ? item.time : '--' }}
              </div>
            </div>
            <div class="info">
              <div class="info-label">{{ '日期' | translate }}：</div>
              <div class="info-value">
                {{ item.start | localDate : accountSetting.dateTimeFormat }}--{{
                  item.end | localDate : accountSetting.dateTimeFormat
                }}
              </div>
            </div>
          </div>
          <div class="item-body-right">
            <img
              src="/assets/media/app/img/video/download_active.svg"
              (click)="download($event, item)"
              *ngIf="!item.isDownloading"
            />
            <nz-progress
              *ngIf="item.isDownloading"
              [nzPercent]="item.progress"
              nzType="circle"
              [nzWidth]="40"
            ></nz-progress>
          </div>
        </div>
      </div>
    </ng-template>
  </div>
</div>

<ng-template #noVideo>
  <div class="no-video">
    <i nz-icon nzType="play-circle" nzTheme="fill"></i>
    <p translate>当前暂无视频播放</p>
  </div>
</ng-template>
