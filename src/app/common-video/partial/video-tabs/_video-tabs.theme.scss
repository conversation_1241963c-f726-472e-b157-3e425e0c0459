@mixin app-video-tabs-theme($theme) {
  $is-dark-theme: map-get($theme, is-dark);
  $primary: map-get($theme, primary);
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);

  app-video-tabs {
    .video-container {
      .video-content {
        background: #fff;
        box-shadow: 0px 4px 12px 0px rgba(15, 23, 59, 0.08);
      }
    }

    .video-aside {
      background: if($is-dark-theme, mat-color($background, card), mat-color($background, background));
      box-shadow: 3px 0px 4px 0px rgba(20, 20, 37, 0.32);

      &-header {
        border-bottom: 1px solid if($is-dark-theme, #525967, #d9dfec);
      }

      &-footer {
        border-top: 8px solid if($is-dark-theme, mat-color($background, background), mat-color($background, hover));
      }
    }

    .tabs {
      .tab {
        border-bottom: 2px solid if($is-dark-theme, mat-color($background, card), mat-color($background, background));
        color: mat-color($foreground, text);

        &.active {
          border-color: mat-color($primary);
          color: mat-color($primary);
        }

        &:hover {
          color: mat-color($primary);
        }
      }
    }

    .search {
      .organ {
        color: mat-color($foreground, text);
        background-color: if($is-dark-theme, mat-color($background, background), mat-color($background, hover));
      }
    }

    .status-item {
      border: 1px solid if($is-dark-theme, mat-color($background, background), mat-color($background, hover));
      background: if($is-dark-theme, mat-color($background, background), mat-color($background, hover));

      &-info {
        .status-text {
          color: mat-color($foreground, secondary-text);
        }

        .status-count {
          color: mat-color($foreground, text);
        }
      }

      &-selected {
        background: mat-color($primary);

        &:after {
          border-top: 1px solid mat-color($primary, default-contrast);
          border-left: 1px solid mat-color($primary, default-contrast);
        }
      }

      &.active,
      &:hover {
        background: if($is-dark-theme, #6d7486, mat-color($background, hover));
        border-color: mat-color($primary);

        .status-text {
          color: mat-color($foreground, text);
        }
      }
    }

    .unit {
      .refresh {
        i {
          color: mat-color($foreground, text);
        }
      }
    }

    .list {
      &-header {
        background: if($is-dark-theme, mat-color($background, background), mat-color($background, hover));
      }

      &-item {
        color: mat-color($foreground, text);

        &.expand {
          background: if($is-dark-theme, #4a5469, mat-color($background, hover));
        }

        &-body {
          border-top: 1px solid if($is-dark-theme, mat-color($background, card), #d9dfec);
        }
      }

      .th {
        color: mat-color($foreground, text);
      }
    }

    .no-data {
      span {
        color: mat-color($foreground, text);
      }
    }
  }

  @if $is-dark-theme {
    app-video-tabs {
      .aside-btn.ant-btn {
        background-color: mat-color($background, background);
        border-color: mat-color($background, background);
        color: mat-color($foreground, text);

        &:hover,
        &:focus,
        &:active {
          background-color: mat-color($background, background);
          border-color: mat-color($background, background);
        }
      }

      .ant-picker-range {
        background-color: mat-color($background, background);
        border-color: mat-color($background, background);
        color: mat-color($foreground, text);

        .ant-picker-input > input {
          color: mat-color($foreground, text);
        }

        .ant-picker-suffix {
          color: mat-color($foreground, secondary-text);
        }

        .ant-picker-separator {
          color: mat-color($foreground, secondary-text);
        }
      }

      .unit {
        .ant-input-affix-wrapper {
          background: mat-color($background, background);
          border: 1px solid mat-color($background, background);
          color: mat-color($foreground, text);

          input.ant-input {
            color: mat-color($foreground, text);
            background-color: mat-color($background, background);
          }
        }
      }

      .video-aside-footer .btns {
        button:first-child {
          background-color: transparent;
          color: mat-color($foreground, text);

          &:hover,
          &:active {
            color: mat-color($primary, text);
          }
        }
      }
    }
  }
}
