<div class="video-container">
  <div class="video-header">
    <div class="video-title">{{ currTab === 0 ? ('实时视频' | translate) : ('历史视频' | translate) }}</div>
    <span>{{ '设备号' | translate }}：{{ imei }}</span>
    <!-- <span>{{ '车牌号' | translate }}：{{ unitName }}</span> -->
    <span>{{ '通道号' | translate }}：{{ channel }}</span>
    <div class="video-aside-footer" *ngIf="currTab === VideoTabType.History">
      <form nz-form [formGroup]="historyVideoForm" (ngSubmit)="searchHistoryVideo()">
        <div class="date">
          <nz-range-picker
            [nzShowTime]="{ nzFormat: accountSetting.timeFormat }"
            [nzFormat]="accountSetting.dateTimeFormat"
            formControlName="date"
            [nzAllowClear]="false"
            (nzOnOpenChange)="toggleDateRangeModal($event)"
            appLocalNzDateTime
          ></nz-range-picker>
        </div>
        <div class="btns">
          <button type="button" nz-button nzType="default" (click)="resetHistoryVideo(true)">
            {{ '重置' | translate }}
          </button>
          <button type="submit" nz-button nzType="primary">{{ '搜索' | translate }}</button>
        </div>
      </form>
    </div>
  </div>
  <!-- <div class="video-aside" [ngClass]="{ hide: expandAside === false }">
    <button nz-button nzSize="small" class="aside-btn" [ngClass]="{ expand: expandAside }" (click)="toggleAside()">
      <i nz-icon nzType="double-right" nzTheme="outline"></i>
    </button> -->

  <!-- <div class="video-aside-header">
      <div class="tabs">
        <div
          class="tab"
          [ngClass]="{ active: currTab === index }"
          *ngFor="let tag of tabs; let index = index"
          (click)="changeTab(index)"
        >
          {{ tag | translate }}
        </div>
      </div>
    </div> -->
  <!-- <div class="video-aside-body"> -->
  <!-- <form nz-form [formGroup]="form" class="search-form"> -->
  <!-- <div class="search">
          <div
            class="organ"
            nz-popover
            nzPopoverTrigger="click"
            nzPopoverOverlayClassName="popover-organ"
            [(nzPopoverVisible)]="isShowOrganTree"
            [nzPopoverContent]="organTreeTemplate"
            nzPopoverPlacement="bottomLeft"
          >
            <div class="organ-name">
              <img src="/assets/font/organizationSearch.svg" alt="" />
              <span>{{ currOrganName }}</span>
            </div>
            <i nz-icon nzType="right" nzTheme="outline"></i>
          </div> -->

  <!-- <ng-template #organTreeTemplate>
            <div class="organ-tree">
              <nz-input-group>
                <input
                  type="text"
                  nz-input
                  placeholder="{{ '请输入机构关键词' | translate }}"
                  [(ngModel)]="searchOrganName"
                  [ngModelOptions]="{ standalone: true }"
                />
              </nz-input-group>
              <div class="organ-tree-list">
                <nz-tree
                  [nzData]="organTree"
                  nzShowLine
                  [nzExpandedKeys]="organExpandedKeys"
                  [nzSelectedKeys]="organSelectedKeys"
                  [nzSearchValue]="searchOrganName"
                  (nzClick)="changeOrgan($event)"
                ></nz-tree>
              </div>
            </div>
          </ng-template> -->

  <!-- <div class="status">
          <div
            class="status-item"
            [ngClass]="{ active: item.checked }"
            *ngFor="let item of vehicleStatusList"
            (click)="changeStatus(item)"
          >
            <div class="status-item-icon">
              <img class="img-car" [src]="item.legendIcon" />
            </div>
            <div class="status-item-info">
              <div class="status-text">{{ item.name | translate }}</div>
              <div class="status-count">{{ item.count | number }}</div>
            </div>
            <div class="status-item-selected"></div>
          </div>
        </div> -->

  <!-- <div class="unit">
            <nz-input-group class="unit-input" [nzSuffix]="suffixTemplate">
              <input
                type="text"
                nz-input
                maxlength="12"
                placeholder="{{ '车牌号' | translate }}"
                formControlName="plateNumber"
                zr-trim
              />
            </nz-input-group>
            <ng-template #suffixTemplate>
              <i nz-icon nzType="search" nzTheme="outline"></i>
            </ng-template>
            <div class="refresh">
              <i nz-icon nzType="reload" (click)="refresh()" [nzSpin]="refreshing"></i>
            </div>
          </div> -->
  <!-- </div> -->
  <!-- <div class="list">
          <div class="list-header" [class.list-header-live]="currTab === VideoTabType.Live">
            <div class="th" translate>车牌号</div>
            <div *ngIf="currTab === VideoTabType.Live" class="th" translate>视频指令</div>
            <div class="th" translate>摄像头</div>
          </div>
          <div class="list-body">
            <nz-spin class="spining" *ngIf="refreshing; else listTpl"></nz-spin>
            <ng-template #listTpl>
              <div class="no-data" *ngIf="vehicleList.length == 0">
                <img src="/assets/font/noDate.png" />
                <span class="no-data-title">{{ '暂无数据' | translate }}</span>
              </div>
              <div class="list-item" [class.expand]="item.expand" *ngFor="let item of vehicleList">
                <div class="list-item-header" [class.list-item-header-live]="currTab === VideoTabType.Live">
                  <div class="item-left">
                    <label
                      *ngIf="currTab === VideoTabType.Live"
                      nz-checkbox
                      [(ngModel)]="item.checked"
                      (ngModelChange)="changeVehicleChecked(item)"
                      [ngModelOptions]="{ standalone: true }"
                      [nzIndeterminate]="item.indeterminate"
                    ></label>
                    <div class="name">{{ item.plateNumber }}</div>
                    <img class="status img-car" [src]="vehicleStatusInfo[item.carState].legendIcon" />
                  </div>
                  <div class="item-center" *ngIf="currTab === VideoTabType.Live">
                    <i
                      appApplyPermission="video_commands"
                      class="iot icon-xingzhuang1 color-main"
                      (click)="openCommandsModal(item)"
                    ></i>
                  </div>
                  <div class="item-right">
                    <div class="count">
                      {{ item.deviceCameraVos.length }}
                    </div>
                    <div class="toggle" [ngClass]="{ expand: !!item.expand }" (click)="toggleCamera(item)">
                      <i nz-icon nzType="down" nzTheme="outline"></i>
                    </div>
                  </div>
                </div>
                <div class="list-item-body" [ngStyle]="{ display: item.expand ? 'block' : 'none' }">
                  <div class="vehicle" *ngFor="let cameraItem of item.deviceCameraVos">
                    <label
                      *ngIf="currTab === VideoTabType.Live"
                      nz-checkbox
                      [(ngModel)]="cameraItem.checked"
                      (ngModelChange)="changeCameraChecked(item, cameraItem)"
                      [ngModelOptions]="{ standalone: true }"
                    ></label>
                    <label
                      *ngIf="currTab === VideoTabType.History"
                      nz-radio
                      [(ngModel)]="cameraItem.checked"
                      (ngModelChange)="changeCameraRadioChecked(item, cameraItem)"
                      [ngModelOptions]="{ standalone: true }"
                    ></label>
                    <span>{{ cameraItem.camera }}</span>
                  </div>
                </div>
              </div>
            </ng-template>
          </div>
        </div> -->
  <!-- </form> -->
  <!-- </div> -->
  <!-- <div class="video-aside-footer" [ngStyle]="{ display: currTab === VideoTabType.History ? 'block' : 'none' }">
      <form nz-form [formGroup]="historyVideoForm" (ngSubmit)="searchHistoryVideo()">
        <div class="date">
          <nz-range-picker
            [nzShowTime]="{ nzFormat: accountSetting.timeFormat }"
            [nzFormat]="accountSetting.dateTimeFormat"
            formControlName="date"
            [nzAllowClear]="false"
            (nzOnOpenChange)="toggleDateRangeModal($event)"
            appLocalNzDateTime
          ></nz-range-picker>
        </div>
        <div class="btns">
          <button type="button" nz-button nzType="default" (click)="resetHistoryVideo(true)">Reset</button>
          <button type="submit" nz-button nzType="primary">Search</button>
        </div>
      </form>
    </div>
  </div> -->

  <div class="video-content" [@flyIn]="currTab">
    <app-live-video
      *ngIf="currTab === VideoTabType.Live"
      [videoList]="videoList"
      (changeVideos)="changeVideos($event)"
    ></app-live-video>
    <app-history-video *ngIf="currTab === VideoTabType.History" [videoInfo]="histroyVideoInfo"></app-history-video>
  </div>
</div>
