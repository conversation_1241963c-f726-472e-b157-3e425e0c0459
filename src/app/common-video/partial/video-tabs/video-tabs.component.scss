:host {
  display: block;
  width: 100%;
  height: 100%;
  padding-bottom: 15px;
  font-weight: 400;
}

.video-container {
  height: 100%;
  position: relative;

  .video-content {
    flex: 1;
    height: calc(100vh - 110px);
    border-radius: 2px 12px 2px 2px;
  }

  .video-title {
    color: #4777fd;
    font-size: 16px;
    font-weight: 600;
    display: inline-block;
    padding: 10px;
    margin-right: 40px;
  }

  .video-header span {
    margin-left: 10px;
  }
  .video-aside-footer {
    display: inline-block;
    form > div {
      display: inline-block;
    }
  }
}

.video-aside {
  width: 335px;
  height: calc(100vh - 90px);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right: 16px;
  border-radius: 12px 0px 0px 12px;
  overflow: hidden;
  transition: all 0.5s;

  &.hide {
    width: 46px;
    padding-left: 46px;
  }

  .aside-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    height: 32px;
    padding: 0;
    cursor: pointer;
    transition: all 0.5s;

    &.expand {
      transform: rotate(180deg);
    }
  }

  &-header {
    min-width: 335px;
    padding: 10px 15px 0;
  }

  &-body {
    min-width: 335px;
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
    padding: 10px 15px;

    .search-form {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  &-footer {
    min-width: 335px;
    padding: 15px;

    .date {
      margin-bottom: 10px;
    }
  }

  .btns {
    display: flex;
    justify-content: flex-end;

    button {
      height: 32px;
      padding: 3px 7px;
      font-size: 12px;

      &:first-child {
        margin-right: 10px;
      }
    }
  }
}

.tabs {
  display: flex;
  padding-right: 46px;

  .tab {
    margin-right: 30px;
    font-size: 14px;
    line-height: 35px;
    font-weight: bold;
    cursor: pointer;
  }
}

.search {
  width: 100%;

  .organ {
    width: 100%;
    min-height: 32px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 7px 10px;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;

    &-name {
      flex: 1;
      display: flex;
      align-items: center;

      img {
        margin-right: 5px;
      }

      span {
        flex: 1;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .status {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 10px;
  }
}

.status-item {
  flex: 1;
  display: flex;
  position: relative;
  margin-right: 10px;
  padding: 10px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;

  &-icon {
    align-self: self-start;
    margin-top: -2px;
    padding-right: 7px;
  }

  &-info {
    flex: 1;

    .status-text {
      font-size: 12px;
    }

    .status-count {
      font-size: 18px;
    }
  }

  &-selected {
    display: none;
    width: 32px;
    height: 18px;
    position: absolute;
    bottom: -10px;
    right: -9px;
    transform: rotate(-36deg);

    &:after {
      content: " ";
      width: 4px;
      height: 8px;
      position: absolute;
      top: -1px;
      left: 17px;
      transform: rotate(-105deg);
    }
  }

  &.active {
    .status-item-selected {
      display: block;
    }
  }

  &:last-child {
    margin-right: 0;
  }
}

.unit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &-input {
    flex: 1;
    margin-right: 10px;
  }

  .refresh {
    i {
      vertical-align: baseline;
      cursor: pointer;
    }
  }
}

.list {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;

  &-header {
    display: grid;
    grid-template-columns: 1fr 80px;
    border-radius: 4px;

    &-live {
      grid-template-columns: 2fr 1fr 1fr;
    }
  }

  &-body {
    flex: 1;
    height: 0;
    padding: 8px 0;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    .spining {
      margin-top: 50px;
    }
  }

  &-item {
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;

    &-header {
      display: grid;
      grid-template-columns: 1fr 80px;
      padding: 16px 12px;

      &-live {
        grid-template-columns: 2fr 1fr 1fr;
      }
    }

    &-body {
      padding: 9px 12px 9px 36px;

      .vehicle {
        padding: 9px 0;
        display: flex;
        align-items: center;

        label,
        > span {
          margin-right: 10px;
        }
      }
    }
  }

  .th {
    padding: 10px 12px;
    font-size: 12px;
  }

  .item-left {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 12px;

    .name {
      margin: 0 8px;
      flex: 1;
      width: 0;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .item-center {
    padding: 0 12px;
    text-align: right;

    i {
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .item-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 12px;

    .count {
      flex: 1;
      width: 0;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .toggle {
      cursor: pointer;
      width: 32px;
      text-align: center;
      transition: transform 0.5s;

      &.expand {
        transform: rotate(-180deg);
      }
    }
  }
}

.no-data {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 210px;
  }

  span {
    color: #a6abb6;
  }
}

:host ::ng-deep {
  .ant-checkbox-wrapper,
  .ant-radio-wrapper {
    transform: scale(0.8);
  }

  .ant-picker-range {
    width: 100% !important;
    font-size: 12px;
  }

  .unit {
    .ant-input-affix-wrapper {
      input.ant-input {
        border: 0;
        font-size: 12px;
      }
    }
  }
}

::ng-deep {
}
