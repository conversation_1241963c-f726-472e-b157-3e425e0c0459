import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { trigger, transition, useAnimation } from '@angular/animations';
import { Observable, merge, timer } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, map, tap } from 'rxjs/operators';

import { startOfDay, endOfDay } from 'date-fns';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { TranslateService } from '@ngx-translate/core';
import { BsModalService } from 'ngx-bootstrap/modal';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { flyInAnimation } from '@app/shared/animations';

import { VehicleStatus, vehicleStatusInfo } from '@app/data-management/vehicle-management/shared/models/vehicle';
import { VehicleMonitorParams, VehicleMonitor, Camera, VideoInfo, CameraType } from '@app/shared/models/video';
import { VideoCommandModalComponent } from '@app/video-management/shared/components/video-command-modal/video-command-modal.component';

import { TreeService } from '@app/shared/services/tree.service';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { VideoApiService } from '@app/shared/services/video-api.service';
import { ActivatedRoute } from '@angular/router';
import { dryptoFun } from '../../shared/common-function';
import { NzMessageService } from 'ng-zorro-antd/message';

enum VideoTabType {
  Live,
  History
}
@Component({
  selector: 'app-video-tabs',
  templateUrl: './video-tabs.component.html',
  styleUrls: ['./video-tabs.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [trigger('flyIn', [transition('* => *', [useAnimation(flyInAnimation)])])]
})
export class VideoTabsComponent implements OnInit, OnDestroy, AfterViewInit {
  log: Logger;
  translates = {
    '请重新选择,时间范围不能超过多少小时': '',
    请先选择车辆: '',
    '操作失败,最多支持选择36个通道': '',
    参数错误: ''
  };

  accountSetting = accountSetting;
  VideoTabType = VideoTabType;
  tabs: string[] = ['实时视频', '历史视频'];
  currTab: any = null;
  expandAside = true;

  form: FormGroup;
  historyVideoForm: FormGroup;
  maxHour = 24;
  oneHourMillisecond = 1 * 60 * 60 * 1000;
  refreshing = false;
  unitName = '';
  imei = '';
  channel = '';

  isShowOrganTree = false;
  currOrganName = '';
  searchOrganName = '';
  organTree: NzTreeNodeOptions[] = [];
  organExpandedKeys: string[] = [];
  organSelectedKeys: string[] = [];

  vehicleStatusInfo = vehicleStatusInfo;
  vehicleStatusList = [vehicleStatusInfo[VehicleStatus.Moving], vehicleStatusInfo[VehicleStatus.Stop]];

  hasMoreVehicle = false;
  vehicleList: VehicleMonitor[] = [];
  videoList: Array<VideoInfo | undefined> = [undefined];
  histroyVideoInfo: VideoInfo | undefined;

  constructor(
    private modalService: BsModalService,
    private cd: ChangeDetectorRef,
    private fb: FormBuilder,
    private loggerFactory: LoggerFactory,
    private treeService: TreeService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private nzMessage: NzMessageService,
    private videoApiService: VideoApiService,
    private routeInfo: ActivatedRoute
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates), { hour: this.maxHour }).subscribe((res) => {
      this.translates = res;
    });
    this.buildForm();
    this.buildHistoryVideoForm();
  }

  ngOnInit() {
    this.initRouteInfo();
    this.vehicleStatusList.forEach((item) => {
      item.checked = true;
      item.count = 0;
    });
    // this.getOrganTree().subscribe(() => this.search());
  }

  ngAfterViewInit(): void {
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this.vehicleStatusList.forEach((item) => {
      item.checked = true;
      item.count = 0;
    });
  }
  // 处理url参数
  initRouteInfo() {
    const key = this.routeInfo.snapshot.queryParams.type;
    console.log(key);
    if (key) {
      const model = dryptoFun(decodeURIComponent(key)).split('&');
      console.log(model, 111);
      if (model.length < 3) {
        this.nzMessage.create('warning', this.translates['参数错误']);
        return;
      }
      this.currTab = model[0] === '1' ? VideoTabType.Live : VideoTabType.History;
      // this.unitName = model[1].split('=')[1];
      this.imei = model[1].split('=')[1];
      this.channel = model[2].split('=')[1];
      if (!this.imei || !this.channel) {
        this.nzMessage.create('warning', this.translates['参数错误']);
        return;
      }
      this.videoList = [
        {
          plateNumber: this.unitName,
          deviceNumber: this.imei,
          channel: this.channel,
          carId: '11',
          type: CameraType.Video,
          camera: ''
        }
      ];
    }
  }

  changeTab(index: number) {
    if (this.currTab === index) {
      return;
    }
    this.currTab = index;
    this.videoList = [undefined];
    this.histroyVideoInfo = undefined;
    this.reset();
    this.resetHistoryVideo();
  }

  toggleAside() {
    this.expandAside = !this.expandAside;
  }

  changeStatus(item: any) {
    item.checked = !item.checked;
    const carStates = this.vehicleStatusList.filter((item) => item.checked).map((item) => item.value);
    this.form.get('carStates').setValue(carStates);
    this.search();
  }

  search() {
    if (this.currTab === VideoTabType.History) {
      this.videoList = [undefined];
    }
    this.refreshing = true;
    this.form.get('pageIndex').setValue(1);
    this.vehicleList = [];
    merge(this.getStatusCount(), this.getVehicleList())
      .pipe(
        finalize(() => {
          this.refreshing = false;
          this.cd.markForCheck();
        })
      )
      .subscribe();
  }

  reset() {
    if (this.organTree.length > 0) {
      this.isShowOrganTree = false;
      this.organTree = this.treeService.formatOrganNodes(this.organTree);
      this.currOrganName = this.organTree[0].title;
      this.organSelectedKeys = this.organExpandedKeys = [this.organTree[0].key];
    }
    this.searchOrganName = '';
    this.form.reset({
      organIds: this.organSelectedKeys || [],
      carStates: this.vehicleStatusList.map((item) => {
        item.checked = true;
        return item.value;
      }),
      plateNumber: null,
      pageIndex: 1,
      pageSize: 10
    });
    this.search();
  }

  refresh() {
    if (this.currTab === VideoTabType.Live) {
      this.videoList = [undefined];
    }
    this.reset();
  }

  searchHistoryVideo() {
    if (!this.videoList[0]) {
      this.log.warn(this.translates['请先选择车辆']);
      return;
    }
    const date = this.historyVideoForm.get('date').value;
    let startDate = date[0];
    let endDate = date[1];
    if (endDate.getTime() - startDate.getTime() > this.maxHour * this.oneHourMillisecond) {
      this.log.warn(this.translates['请重新选择,时间范围不能超过多少小时']);
      return;
    }
    this.histroyVideoInfo = {
      ...this.videoList[0],
      startTime: startDate.getTime().toString(),
      endTime: endDate.getTime().toString()
    };
  }

  resetHistoryVideo(isSearch = false) {
    const date = new Date();
    this.historyVideoForm.reset({
      date: [startOfDay(date), endOfDay(date)]
    });
    if (isSearch) {
      this.histroyVideoInfo = undefined;
    }
  }

  toggleDateRangeModal(isOpen: boolean) {
    const dateControl = this.historyVideoForm.get('date');
    if (!isOpen) {
      const date = dateControl.value;
      const startDate = date[0];
      const endDate = date[1];
      // nz-date-range bug 开始时间会大于结束时间
      if (endDate.getTime() < startDate.getTime()) {
        timer(30)
          .pipe(finalize(() => this.cd.markForCheck()))
          .subscribe(() => {
            dateControl.setValue([endDate, startDate]);
          });
      }
    }
    // modal弹窗是异步
    timer(300).subscribe(() => {
      document
        .querySelectorAll('.disable-date-range-input input.ant-calendar-input')
        .forEach((item) => item.setAttribute('disabled', 'disabled'));
    });
  }

  toggleCamera(vehicle: VehicleMonitor) {
    vehicle.expand = !vehicle.expand;
  }

  changeVehicleChecked(vehicle: VehicleMonitor) {
    vehicle.indeterminate = false;
    vehicle.deviceCameraVos.forEach((camera) => {
      camera.checked = vehicle.checked;
      if (camera.checked) {
        this.addVideo(vehicle, camera);
      } else {
        this.removeVideo(vehicle, camera);
      }
    });
  }

  changeCameraChecked(vehicle: VehicleMonitor, camera?: Camera) {
    if (vehicle.deviceCameraVos.every((cameraItem) => cameraItem.checked === false)) {
      vehicle.checked = false;
      vehicle.indeterminate = false;
    } else if (vehicle.deviceCameraVos.every((cameraItem) => cameraItem.checked === true)) {
      vehicle.checked = true;
      vehicle.indeterminate = false;
    } else {
      vehicle.indeterminate = true;
    }
    if (!camera) {
      return;
    }
    if (camera.checked) {
      this.addVideo(vehicle, camera);
    } else {
      this.removeVideo(vehicle, camera);
    }
  }

  changeCameraRadioChecked(vehicle: VehicleMonitor, camera: Camera) {
    this.vehicleList.forEach((item) => {
      item.deviceCameraVos
        .filter((cameraItem) => cameraItem !== camera)
        .forEach((cameraItem) => (cameraItem.checked = false));
    });
    this.videoList = [];
    this.addVideo(vehicle, camera);
  }

  addVideo(vehicle: VehicleMonitor, camera: Camera) {
    const isExits = this.videoList.find(
      (item) => item && item.carId === vehicle.carId && item.channel === camera.channel
    );
    if (isExits) {
      return;
    }
    const index = this.videoList.findIndex((item) => item === undefined);
    const video: VideoInfo = {
      carId: vehicle.carId,
      plateNumber: vehicle.plateNumber,
      deviceNumber: vehicle.deviceNumber,
      ...camera
    };
    if (index > -1) {
      this.videoList[index] = video;
      this.videoList = [...this.videoList];
      return;
    }
    if (this.videoList.length < 36) {
      this.videoList.push(video);
      this.videoList = [...this.videoList];
      return;
    }
    this.log.warn(this.translates['操作失败,最多支持选择36个通道']);
    timer(100)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe(() => this.updateCameraChecked());
  }

  removeVideo(vehicle: VehicleMonitor, camera: Camera) {
    const removeIndex = this.videoList.findIndex(
      (item) => item && item.carId === vehicle.carId && item.channel === camera.channel
    );
    if (removeIndex > -1) {
      this.videoList[removeIndex] = undefined;
      this.videoList = [...this.videoList];
    }
  }

  changeVideos(videos: any[]) {
    this.videoList = videos;
    this.updateCameraChecked();
  }

  updateCameraChecked() {
    this.vehicleList.forEach((vehicle: VehicleMonitor) => {
      vehicle.deviceCameraVos.forEach((camera: Camera) => {
        camera.checked = false;
        this.videoList.forEach((video: VideoInfo) => {
          if (video && video.carId === vehicle.carId && video.channel === camera.channel) {
            camera.checked = true;
          }
        });
      });
      this.changeCameraChecked(vehicle);
    });
  }

  openCommandsModal(vehicle?: VehicleMonitor) {
    const initialState = { vehicle };
    this.modalService.show(VideoCommandModalComponent, {
      initialState,
      class: 'modal-video-command',
      ignoreBackdropClick: true
    });
    const onHidden = this.modalService.onHidden.subscribe(() => {
      onHidden.unsubscribe();
    });
  }

  buildForm() {
    this.form = this.fb.group({
      organIds: [[]],
      carStates: [this.vehicleStatusList.map((item) => item.value)],
      plateNumber: [null],
      pageIndex: [1],
      pageSize: [10_000]
    });
    this.form
      .get('plateNumber')
      .valueChanges.pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => this.search());
  }

  buildHistoryVideoForm() {
    const date = new Date();
    this.historyVideoForm = this.fb.group({
      date: [[startOfDay(date), endOfDay(date)]]
    });
  }

  getOrganTree(): Observable<void> {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.organTree = [];
    return this.referenceDataService.getUserChildrenOrganizations(id).pipe(
      map((res) => {
        this.organTree = res;
        if (this.organTree.length === 0) {
          return;
        }
        this.currOrganName = this.organTree[0].title;
        this.organSelectedKeys = this.organExpandedKeys = [this.organTree[0].key];
        this.form.get('organIds').setValue(this.organSelectedKeys);
        return;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  getStatusCount() {
    const params: VehicleMonitorParams = this.form.getRawValue();
    params.carStates = this.vehicleStatusList.map((item) => item.value);
    let drivingCount = 0;
    let staticCount = 0;
    return this.videoApiService.getVehicleCount(params).pipe(
      tap((res) => {
        if (res.success && res.data) {
          drivingCount = res.data.drivingCount;
          staticCount = res.data.staticCount;
        }
      }),
      finalize(() => {
        this.vehicleStatusList[0].count = drivingCount;
        this.vehicleStatusList[1].count = staticCount;
      })
    );
  }

  getVehicleList() {
    const params: VehicleMonitorParams = this.form.getRawValue();
    params.carStates = this.vehicleStatusList.filter((item) => item.checked).map((item) => item.value);
    Reflect.deleteProperty(params, 'date');
    return this.videoApiService.getVehicleList(params).pipe(
      tap((res) => {
        this.hasMoreVehicle = false;
        if (res.success && Array.isArray(res.data)) {
          res.data = res.data.map((item) => {
            item.expand = false;
            item.deviceCameraVos = item.deviceCameraVos || [];
            item.deviceCameraVos.forEach((camera) => (camera.checked = false));
            return item;
          });
          this.vehicleList = [...this.vehicleList, ...res.data];
          this.hasMoreVehicle = res.data.length === params.pageSize;
          this.updateCameraChecked();
        }
      })
    );
  }

  getNextPageVehicleList() {
    if (!this.hasMoreVehicle) {
      return;
    }
    const pageIndexControl = this.form.get('pageIndex');
    pageIndexControl.setValue(pageIndexControl.value + 1);
    this.getVehicleList();
  }
}
