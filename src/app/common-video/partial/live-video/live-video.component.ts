import {
  Component,
  OnInit,
  QueryList,
  ViewChildren,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  ElementRef,
  Output,
  Input,
  EventEmitter,
  HostListener
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory } from '@core/logger-factory.service';
import { Logger } from '@core/logger.service';

import { VideoInfo } from '@app/shared/models/video';
import { OpenVideoComponent } from '@app/shared';
import { timer } from 'rxjs';

@Component({
  selector: 'app-live-video',
  templateUrl: './live-video.component.html',
  styleUrls: ['./live-video.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LiveVideoComponent implements OnInit {
  @Input()
  get videoList(): Array<VideoInfo | undefined> {
    return this._videoList;
  }
  set videoList(val: Array<VideoInfo | undefined>) {
    this._videoList = val;
    this.updateLayout();
    // 因OnPush策略原因,所以用timer强制更新hasCountdownMask值,
    timer().subscribe(() => this.cd.markForCheck());
  }
  @Output() changeVideos: EventEmitter<Array<VideoInfo | undefined>> = new EventEmitter();

  @HostListener('document:fullscreenchange', ['$event'])
  onFullscreen() {
    this.isFullScreening = !!document.fullscreen;
  }

  log: Logger;
  translates = {
    获取视频地址失败: ''
  };

  videoLayoutList = { 1: '1', 2: '4', 3: '9', 4: '16', 5: '25', 6: '36' };
  currLayout: number = 1;
  isFullScreening = false;
  @ViewChildren(OpenVideoComponent) videoComponentList: QueryList<OpenVideoComponent>;

  get hasCountdownMask(): boolean {
    let val = false;
    if (this.videoComponentList && this.videoComponentList.length > 0) {
      val = this.videoComponentList.some((item: OpenVideoComponent) => !item.isPaused);
    }
    return val;
  }

  _videoList: Array<VideoInfo | undefined> = [undefined];

  constructor(
    private cd: ChangeDetectorRef,
    private elementRef: ElementRef,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }

  clearVideoAll() {
    this.changeVideos.emit([undefined]);
  }

  playVideoAll() {
    this.videoComponentList.forEach((item: OpenVideoComponent) => {
      item.play();
    });
  }

  pauseVideoAll() {
    this.videoComponentList.forEach((item: OpenVideoComponent) => {
      item.pause();
    });
  }

  mutedVideoAll() {
    this.videoComponentList.forEach((item: OpenVideoComponent) => {
      item.changeVolume(0);
    });
  }

  changeLayout() {
    const len = this.videoList.length;
    const layoutLen = Number(this.videoLayoutList[this.currLayout]);
    if (layoutLen === len) {
      return;
    }
    // 不要直接修改this.videoList,从父组件传参数修改
    const _videoList = [...this.videoList];
    _videoList.length = layoutLen;
    this.changeVideos.emit(_videoList);
  }

  updateLayout() {
    const videoLength: number = this.videoList.length;
    let currLayout = 1;
    if (videoLength === 1) {
      currLayout = 1;
    } else if (videoLength > 1 && videoLength <= 4) {
      currLayout = 2;
    } else if (videoLength > 4 && videoLength <= 9) {
      currLayout = 3;
    } else if (videoLength > 9 && videoLength <= 16) {
      currLayout = 4;
    } else if (videoLength > 16 && videoLength <= 25) {
      currLayout = 5;
    } else if (videoLength > 25 && videoLength <= 36) {
      currLayout = 6;
    }
    // 避免死循环
    if (this.currLayout === currLayout) {
      return;
    }
    this.currLayout = currLayout;
    this.changeLayout();
  }

  trackByVideo(index: number, item: VideoInfo) {
    return item ? `${item.carId}${item.channel.toString()}` : index;
  }

  onChangeFullScreen(selector: string) {
    if (this.isFullScreening && document.exitFullscreen) {
      document.exitFullscreen();
      return;
    }
    const el = this.elementRef.nativeElement.querySelector(selector);
    if (el.requestFullscreen) {
      el.requestFullscreen();
    }
  }
}
