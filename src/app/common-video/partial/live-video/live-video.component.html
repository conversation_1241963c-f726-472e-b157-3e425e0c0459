<div class="condition">
  <div class="condition-left">
    <!--<nz-slider
      [nzMax]="6"
      [nzMin]="1"
      [nzTipFormatter]="null"
      [nzMarks]="videoLayoutList"
      [(ngModel)]="currLayout"
      (ngModelChange)="changeLayout()"
    ></nz-slider>-->
  </div>
  <div class="condition-right">
    <!-- <div class="action-btn" (click)="clearVideoAll()">
      <i class="iot iot-video-reset"></i>
      <span translate>一键清除</span>
    </div> -->
    <div class="action-btn" (click)="mutedVideoAll()">
      <i class="iot iot-video-silent"></i>
      <span translate>一键静音</span>
    </div>
    <div class="action-btn" (click)="playVideoAll()">
      <i class="iot iot-video-play"></i>
      <span translate>播放</span>
    </div>
    <div class="action-btn" (click)="pauseVideoAll()">
      <i class="iot iot-video-pause"></i>
      <span translate>暂停</span>
    </div>
    <div class="action-btn" (click)="onChangeFullScreen('.video-container')">
      <i class="iot iot-video-fullscreen"></i>
      <span translate>全屏</span>
    </div>
  </div>
</div>
<div class="video-container">
  <app-countdown-mask [isStart]="hasCountdownMask" (onEnd)="pauseVideoAll()"></app-countdown-mask>
  <div class="action-btn exit-fullscreen-btn" *ngIf="isFullScreening" (click)="onChangeFullScreen('.video-container')">
    <i class="iot iot-video-fullscreen"></i>
    <span translate>退出全屏</span>
  </div>
  <div class="video-list" [class.video-list-fullscreen]="isFullScreening">
    <div
      class="video-item"
      [ngClass]="'video-item-' + videoLayoutList[currLayout]"
      *ngFor="let item of videoList; let index = index; trackBy: trackByVideo"
    >
      <div class="video-item-content">
        <!-- <div class="video-item-title">
          <h6 *ngIf="item">{{ item.plateNumber || '' }}</h6>
          <span>-</span>
          <p *ngIf="item">{{ item.camera || '' }}</p>
        </div> -->
        <app-open-video *ngIf="item; else noVideo" [videoInfo]="item"></app-open-video>
      </div>
    </div>
  </div>
</div>

<ng-template #noVideo>
  <div class="no-video">
    <i nz-icon nzType="play-circle" nzTheme="fill"></i>
    <p translate>当前暂无视频播放</p>
  </div>
</ng-template>
