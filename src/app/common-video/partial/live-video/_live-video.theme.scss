@mixin app-live-video-theme($theme) {
  $is-dark-theme: map-get($theme, is-dark);
  $primary: map-get($theme, primary);
  $background: map-get($theme, background);
  $foreground: map-get($theme, foreground);

  app-live-video {
    .condition {
      background: if($is-dark-theme, mat-color($background, card), mat-color($background, hover));
    }

    .action-btn {
      color: mat-color($foreground, text);

      &.exit-fullscreen-btn {
        background: rgba(0, 0, 0, 0.5);
      }

      &:hover {
        color: mat-color($primary);
      }
    }

    .no-video {
      color: #99a0ac;
    }

    .condition-left {
      // line
      .ant-slider-rail {
        background-color: if($is-dark-theme, #525967, #d9dce2);
      }

      .ant-slider:hover .ant-slider-rail {
        background-color: if($is-dark-theme, #525967, #d9dce2);
      }

      // dot
      .ant-slider-dot {
        background-color: if($is-dark-theme, mat-color($background, card), mat-color($background, background));
        border: 1px solid if($is-dark-theme, #525967, #e8e8eb);
      }

      // active dot
      .ant-slider-handle {
        border: 3px solid if($is-dark-theme, #7f94b2, mat-color($primary));

        &:hover {
          border: 3px solid if($is-dark-theme, #7f94b2, mat-color($primary));
        }
      }

      .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
        border-color: if($is-dark-theme, #7f94b2, mat-color($primary));
      }

      // text
      .ant-slider-mark-text {
        color: if($is-dark-theme, #a6abb6, #999);

        &.ant-slider-mark-active {
          color: mat-color($foreground, text);
        }
      }
    }
  }
}
