:host {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.condition {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  border-top-right-radius: 12px;

  &-left {
    width: 200px;
  }

  &-right {
    width: 485px;
    display: flex;
    justify-content: space-between;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 12px;
  cursor: pointer;

  &.exit-fullscreen-btn {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 16px;
    justify-content: flex-end;
    z-index: 14;
    cursor: pointer;
  }

  i {
    font-size: 22px;
  }

  span {
    padding-left: 10px;
  }
}

.video-container {
  width: 100%;
  height: 0;
  flex: 1;
  position: relative;
  padding: 8px 8px 4px;
}

.video-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  height: 100%;
  margin: -4px;

  &.video-list-fullscreen {
    height: 100%;
    background-color: #fff;
  }

  .video-item {
    padding: 4px;

    &.video-item-1 {
      width: 100%;
      height: 100%;
    }

    &.video-item-4 {
      width: 50%;
      height: 50%;
    }

    &.video-item-9 {
      width: 33.3333%;
      height: 33.3333%;
    }

    &.video-item-16 {
      width: 25%;
      height: 25%;
    }

    &.video-item-25 {
      width: 20%;
      height: 20%;
    }

    &.video-item-36 {
      width: 16.6666%;
      height: 16.6666%;
    }

    .video-item-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      border: 1px solid #e6e8ec;
      border-radius: 3px;
    }

    .video-item-title {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      padding: 3px 5px;
      color: #a6abb4;
      font-size: 12px;

      h6 {
        margin-right: 3px;
        font-size: 12px;
        color: #091832;
      }

      span {
        margin-right: 3px;
        color: #9da3ad;
      }

      p {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}

.no-video {
  flex: 1;
  height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-image: url(/assets/media/app/img/video/bg.png);
  background-size: 100% 100%;
  color: #99a0ac;

  i {
    font-size: 25px;
    margin-bottom: 8px;
  }

  p {
    font-size: 12px;
  }
}

:host ::ng-deep {
  .condition-left {
    // line
    .ant-slider-rail {
      top: 5px;
      height: 1px;
    }

    .ant-slider-track {
      height: 0;
    }
  }
}
