import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin, timer, Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

import { NzConfigService } from 'ng-zorro-antd/core/config';

import { environment } from '@env/environment';
import { AuthenticationOAuth2Service } from '@app/core';
import { CreateSubscriptionService } from '@app/shared';

import { I18nService } from '@app/core/i18n.service';
import { ThemeService } from '@app/shared/services/theme.service';

import { mapConfig } from '@app/map/models/map-config';
import { SwitchMapService } from '@app/map/services/switch-map.service';

import { HourTime } from '@app/account-settings/models/account';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-sign-in-callback',
  templateUrl: './sign-in-callback.component.html',
  styleUrls: ['./sign-in-callback.component.scss']
})
export class SignInCallbackComponent implements OnInit {
  loading = true;
  success = false;
  loginUrl: string = environment.authentication.type === 'oauth2' ? '' : '/login';

  constructor(
    private authenticationOAuth2Service: AuthenticationOAuth2Service,
    private accountService: AccountService,
    private i18nService: I18nService,
    private nzConfigService: NzConfigService,
    private router: Router,
    private subscriptionService: CreateSubscriptionService,
    private switchMapService: SwitchMapService,
    private themeService: ThemeService
  ) {}

  ngOnInit() {
    timer(10).subscribe(() => {
      this.authenticationOAuth2Service
        .signinCallback()
        .then(() => this.syncAccount())
        .then(() => this.navigateUrl())
        .catch(() => {
          this.loading = false;
          this.success = false;
        });
    });
  }

  private navigateUrl() {
    const defaultUrl = '/positionManagement';
    let currentRouting: string = sessionStorage.getItem('currentRouting') || defaultUrl;
    currentRouting = ['/authentication/callback', '/'].includes(currentRouting) ? defaultUrl : currentRouting;
    return this.router.navigateByUrl(`/${currentRouting}`).then((isSuccess) => {
      this.subscriptionService.login$.next(isSuccess);
    });
  }

  /**
   * 同步账号信息
   * @returns
   */
  syncAccount(): Promise<[void, void]> {
    return this.accountService
      .syncAccountInfo()
      .pipe(switchMap(() => forkJoin([this.syncZone(), this.syncAccountSetting()])))
      .toPromise();
  }

  /**
   * 同步当前用户所在时区
   * @returns
   */
  private syncZone(): Observable<void> {
    return this.accountService.setZone();
  }

  /**
   * 获取用户设置的个人信息
   * @returns
   */
  private syncAccountSetting(): Observable<void> {
    return this.accountService.getAccountSetting().pipe(
      map((res) => {
        if (!res.success) {
          return;
        }
        res.data = res.data || {};
        accountSetting.lng = res.data.lng || accountSetting.lng;
        accountSetting.lat = res.data.lat || accountSetting.lat;
        accountSetting.address = res.data.address || accountSetting.address;
        accountSetting.mapType = res.data.mapType || accountSetting.mapType;
        mapConfig.center = [accountSetting.lng, accountSetting.lat];
        this.switchMapService.mapType = accountSetting.mapType;

        accountSetting.hourTime = res.data.hourTime || accountSetting.hourTime;
        accountSetting.dateTimeFormat = res.data.dateTimeFormat || accountSetting.dateTimeFormat;
        const dateTimeSpaceIndex = accountSetting.dateTimeFormat.indexOf(' ');
        accountSetting.dateFormat = accountSetting.dateTimeFormat.substring(0, dateTimeSpaceIndex);
        accountSetting.timeFormat = accountSetting.dateTimeFormat.substring(dateTimeSpaceIndex + 1);
        this.nzConfigService.set('timePicker', {
          nzUse12Hours: accountSetting.hourTime === HourTime.Hour12 ? 'true' : 'false'
        });

        accountSetting.logo = res.data.logo || accountSetting.logo;
        accountSetting.theme = res.data.theme || accountSetting.theme;
        this.themeService.changeTheme(accountSetting.theme);

        // TODO: 固定为英文,如需动态切换,取消下面注释
        // if (typeof res.data.language !== 'undefined') {
        //   accountSetting.language = res.data.language;
        //   this.i18nService.switchLanguage(res.data.language)
        // }
        return;
      })
    );
  }
}
