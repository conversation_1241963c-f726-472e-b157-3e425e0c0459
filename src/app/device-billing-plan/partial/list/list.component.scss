.board {
  padding: 25px 20px 0 20px;
  display: flex;
  justify-content: space-between;
}

.status {
  width: 100%;
  display: flex;
  //   justify-content: space-between;
}

.overdue {
  margin-right: 50px;
  padding: 12px;
  width: 310px;
  height: 92px;
  background: url(/assets/media/app/img/customer/status-overdue.png) center no-repeat;
  background-size: 100% 100%;
  border-bottom: 8px solid #e13823;
  text-align: center;
  font-size: 18px;
  line-height: 20px;
  color: #e53030;
  // &:hover {
  //   cursor: pointer;
  // }
}

.overdue-data {
  font-size: 32px;
  line-height: 1.5;
  color: #570404;
}

.normal {
  padding: 12px;
  width: 310px;
  height: 92px;
  background: url(/assets/media/app/img/customer/status-normal.png) center no-repeat;
  background-size: 100% 100%;
  border-bottom: 8px solid #3da637;
  text-align: center;
  font-size: 18px;
  line-height: 20px;
  color: #3da637;
  // &:hover {
  //   cursor: pointer;
  // }
}

.normal-data {
  font-size: 32px;
  line-height: 1.5;
  color: #016723;
}

.data-show {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  width: 100%;
  height: 92px;
  background: #faf2e2;
}

.img {
  margin-right: 20px;
}

.total {
  margin-top: 10px;
  width: 62px;
  height: 42px;
}

.balance {
  width: 51px;
  height: 57px;
}

.box {
  display: flex;
  justify-content: space-around;
}

.line {
  width: 1px;
  height: 54px;
  background: #d8d8d8;
  border: 1px solid #ebd1a1;
}

.number-title {
  font-size: 14px;
  line-height: 1.5;
  color: #866222;
}

.number {
  font-size: 28px;
  line-height: 1.5;
  color: #654610;
}

.red {
  color: red;
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

:host ::ng-deep .m-portlet__head-caption {
  display: flex !important;
  flex-direction: row-reverse;
  align-content: center;
  height: 100%;
}
