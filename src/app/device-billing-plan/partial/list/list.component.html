<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="board">
      <div class="status">
        <div class="overdue">
          <div>{{ '逾期设备' | translate }}</div>
          <div class="overdue-data">{{ statisticsData?.expireDeviceNo | isNull }}</div>
        </div>
        <div class="normal">
          <div>{{ '正常设备' | translate }}</div>
          <div class="normal-data">{{ statisticsData?.normalDeviceNo | isNull }}</div>
        </div>
      </div>

      <div class="data-show">
        <div class="box">
          <img class="img total" src="/assets/media/app/img/customer/total-accounts.png" alt="" />
          <div class="data-box">
            <div class="number-title">{{ '账户金额' | translate }}</div>
            <div class="number">{{ statisticsData?.totalDepositMoney | isNull }}</div>
          </div>
        </div>
        <div class="line"></div>
        <div class="box">
          <img class="img balance" src="/assets/media/app/img/customer/balance-img.png" alt="" />
          <div class="data-box">
            <div class="number-title">{{ '当前可用余额' | translate }}</div>
            <div class="number">{{ statisticsData?.balanceMoney | isNull }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'deviceNo'" label="{{ '设备号' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <input
                type="text"
                nz-input
                placeholder="{{ '请输入设备号' | translate }}"
                [(ngModel)]="rule.datas[dataIndex]"
              />
            </ng-template>
          </ngx-query-field>
          <ngx-query-field [name]="'rechargeTime'" label="{{ '充值时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
          <ngx-query-field [name]="'createdTime'" label="{{ '创建时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
          <ngx-query-field [name]="'status'" label="{{ '计费状态' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-select
                class="w-full"
                [(ngModel)]="rule.datas[dataIndex]"
                nzPlaceHolder="{{ '请选择计费状态' | translate }}"
              >
                <nz-option
                  *ngFor="let item of statusOptions"
                  [nzLabel]="item.label | translate"
                  [nzValue]="item.value"
                ></nz-option>
              </nz-select>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <div class="m-portlet">
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          [width]="260"
          name="{{ '所属机构' | translate }}"
          prop="group"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" [title]="row.orgName">
              {{ row.orgName | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '设备号' | translate }}"
          [width]="200"
          prop="deviceNo"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" [title]="row.deviceNo">
              {{ row.deviceNo | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '设备状态' | translate }}"
          prop="status"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ deviceStatus[row.status] | translate | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '到期日' | translate }}"
          prop="expireTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.expireTime | localDate : accountSetting.dateTimeFormat }}"
            >
              {{ row.expireTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="260"
          name="{{ '产品规则标准' | translate }}"
          prop="serviceType"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ serviceEnum[row.serviceType] | translate }}"
            >
              {{ serviceEnum[row.serviceType] | translate | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" title="{{ row.deviceTypeName }}">
              {{ row.deviceTypeName | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ row.servicePeriod | isNull }}{{ row.periodUnit | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ row.unitPrice | isNull }}{{ row.monetaryUnit | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '充值时间' | translate }}"
          prop="rechargeTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.rechargeTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.rechargeTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '创建时间' | translate }}"
          prop="createdAt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.createdAt | localDate : accountSetting.dateTimeFormat }}"
            >
              {{ row.createdAt | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '首次上报位置' | translate }}"
          prop="firstLocationTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.firstLocationTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.firstLocationTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.firstLocationAddress | isNull }}"
            >
              {{ row.firstLocationAddress | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '更新位置' | translate }}"
          prop="locationTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.locationTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.locationTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" title="{{ row.address | isNull }}">
              {{ row.address | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [frozenRight]="true" [width]="20" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
