import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { DeviceBillingPlanService } from '@app/device-billing-plan/shared/device-billing-plan.service';
import { addDays, endOfDay, startOfDay } from 'date-fns';

@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit, AfterViewInit {
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  originTemplate: any;
  deviceStatus = {
    '1': '正常',
    '2': '逾期'
  };
  serviceEnum = {
    '1': '平台',
    '2': 'API'
  };
  statisticsData = {} as any;
  overdue = 2;
  // queryStatus: number;
  statusOptions: any[] = [
    {
      label: '正常',
      value: 1
    },
    {
      label: '逾期',
      value: 2
    }
  ];

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'deviceNo', op: 'cn' },
          { field: 'rechargeTime', op: 'eq' },
          { field: 'createdTime', op: 'eq' },
          { field: 'status', op: 'eq' }
        ],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    private loggerFactory: LoggerFactory,
    private cd: ChangeDetectorRef,
    private deviceBillingPlanService: DeviceBillingPlanService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }
  ngOnInit(): void {
    this.getBalance();
  }

  getBalance() {
    this.deviceBillingPlanService
      .getBalance()
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.statisticsData = res.data;
        }
      });
  }

  ngAfterViewInit() {
    this.cd.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  loadList(event: any) {
    this.event = event;

    const page = event.page;
    const params: any = this.loadProperty(page);
    // if (this.queryStatus) {
    //   params.status = this.queryStatus;
    // }
    this.loading = true;
    this.footer.showTotalElements = true;
    this.deviceBillingPlanService
      .getDeviceList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'rechargeTime' && rule.data) {
        map['rechargeStartTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['rechargeEndTimeStamp'] = endOfDay(rule.data[1]).getTime();
      } else if (rule.field === 'createdTime' && rule.data) {
        map['createdStartTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['createdEndTimeStamp'] = endOfDay(rule.data[1]).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  // 重置查询模板
  reset() {
    // this.queryStatus = null;
    this.queryTemplates = this.originTemplate;
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  // getListData(status: number) {

  //   this.queryStatus = status;
  //   this.loadList(this.event);
  // }
}
