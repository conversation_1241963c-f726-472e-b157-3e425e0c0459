import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RouteExtensions } from '@app/core';

import { ListComponent } from './partial/list/list.component';

const routes: Routes = RouteExtensions.withHost(
  {
    path: '',
    component: ListComponent
  },
  []
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DeviceBillingPlanRoutingModule {}
