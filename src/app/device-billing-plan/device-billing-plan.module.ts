import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { SharedModule } from '../shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { DeviceBillingPlanRoutingModule } from './device-billing-plan-routing.module';
import { ListComponent } from './partial/list/list.component';

@NgModule({
  declarations: [ListComponent],
  imports: [
    CommonModule,
    TranslateModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    DeviceBillingPlanRoutingModule
  ]
})
export class DeviceBillingPlanModule {}
