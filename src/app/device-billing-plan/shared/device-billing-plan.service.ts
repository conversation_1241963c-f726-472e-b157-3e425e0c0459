import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { WebApiResultResponse } from '@core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class DeviceBillingPlanService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  // 获取设备列表
  getDeviceList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/device/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取账户金额和余额
  // getBalance() {
  //   const url = `glcrm-account-api/v1/api/settlement/account/statistics`;
  //   return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  // }
  getBalance(tenantId?: string) {
    const params = {
      tenantId
    };
    const url = `glcrm-account-api/v1/api/settlement/account/statistics`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
