import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDatatableFooterModule } from 'ngx-datatable-footer';
import { NgxDatatableActionsModule } from 'ngx-datatable-actions';
import { MapModule } from '@app/map/map.module';

import { VehicleManagementRoutingModule } from './vehicle-management-routing.module';
import { VehicleIndexComponent } from './partial/vehicle-index/vehicle-index.component';
import { VehicleCreatComponent } from './partial/vehicle-creat/vehicle-creat.component';
import { InstallInfoComponent } from './partial/install-info/install-info.component';

import { DeviceManagementModule } from '../device-management/device-management.module';
import { DriverManagementModule } from '../driver-management/driver-management.module';
import { DataComponent } from './partial/data/data.component';
import { VehicleEditComponent } from './partial/vehicle-edit/vehicle-edit.component';
import { VehicleUiComponent } from './partial/vehicle-ui/vehicle-ui.component';
import { VehicleUiFormComponent } from './partial/vehicle-ui-form/vehicle-ui-form.component';
import { VehicleUiDeviceComponent } from './partial/vehicle-ui-device/vehicle-ui-device.component';
import { VehicleUiDriverComponent } from './partial/vehicle-ui-driver/vehicle-ui-driver.component';
import { OwnersManagementModule } from '../owners-management/owners-management.module';
import { GroupChangeComponent } from './partial/group-change/group-change.component';

@NgModule({
  imports: [
    FormsModule,
    SharedModule,
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    NgxDatatableFooterModule,
    NgxDatatableActionsModule,
    VehicleManagementRoutingModule,
    DeviceManagementModule,
    DriverManagementModule,
    MapModule,
    OwnersManagementModule
  ],
  declarations: [
    DataComponent,
    VehicleIndexComponent,
    VehicleCreatComponent,
    VehicleEditComponent,
    VehicleUiComponent,
    VehicleUiFormComponent,
    VehicleUiDeviceComponent,
    VehicleUiDriverComponent,
    InstallInfoComponent,
    GroupChangeComponent
  ],
  entryComponents: [VehicleIndexComponent, InstallInfoComponent]
})
export class VehicleManagementModule {}
