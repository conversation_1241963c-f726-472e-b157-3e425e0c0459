import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';

import { DataComponent } from './partial/data/data.component';
import { VehicleCreatComponent } from './partial/vehicle-creat/vehicle-creat.component';
import { VehicleEditComponent } from './partial/vehicle-edit/vehicle-edit.component';
import { VehicleDetailComponent } from '../driver-management/partial/vehicle-detail/vehicle-detail.component';

const routes: Routes = RouteExtensions.withHost({ path: '', component: DataComponent }, [
  { path: 'new', component: VehicleCreatComponent, data: { title: '车辆新建' } },
  { path: ':id/edit', component: VehicleEditComponent, data: { title: '车辆编辑' } },
  { path: ':id', component: VehicleDetailComponent, data: { title: '车辆详情' } }
]);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class VehicleManagementRoutingModule {}
