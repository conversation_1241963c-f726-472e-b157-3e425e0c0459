<div class="content">
  <div class="row">
    <div class="title">
      <div class="flag">
        <img src="/assets/font/blue_small_tip.png" alt="" />
      </div>
      <div class="txt" translate>
        绑定设备
        <img
          class="question-mark"
          src="/assets/font/question-mark.svg"
          nz-tooltip
          nzTooltipPlacement="top"
          nzTooltipTitle="{{ '选填' | translate }}"
        />
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-5">
      <div class="m-portlet">
        <div class="m-portlet__head">
          <div class="m-portlet__head-tools">
            <div class="toolbar">
              <ngx-query
                [hidden]="true"
                [datePickerReadonly]="false"
                [columnNumber]="1"
                class="full-screen no-header"
                #deviceNgxQuery
                [isRetainQueryMode]="true"
                [queryTemplates]="deviceQueryTemplates"
                [showModeButtons]="true"
                [mode]="mode"
                [showPlainCollapseToolBar]="false"
              >
                <ngx-query-field [name]="'search'" [label]="''" [type]="'string'">
                  <ng-template
                    ngx-query-value-input-template
                    dataType="text"
                    let-rules="rules"
                    let-rule="rule"
                    let-dataIndex="dataIndex"
                  >
                    <button class="querybtn btn">
                      <i class="la la-search"></i>
                    </button>
                    <input
                      type="text"
                      [(ngModel)]="rule.datas[dataIndex]"
                      class="form-control form-control-sm query_input"
                      placeholder="{{ '车主名/车牌号/设备号' | translate }}"
                    />
                  </ng-template>
                </ngx-query-field>

                <ngx-query-field [name]="'status'" [label]="'设备状态'" [type]="'string'" isCollapse="true">
                  <ng-template
                    ngx-query-value-input-template
                    dataType="text"
                    let-rules="rules"
                    let-rule="rule"
                    let-dataIndex="dataIndex"
                    let-placeholder="placeholder"
                  >
                    <input
                      type="text"
                      [(ngModel)]="rule.datas[dataIndex]"
                      class="form-control form-control-sm"
                      placeholder="{{ '设备状态' | translate }}"
                    />
                  </ng-template>
                </ngx-query-field>
              </ngx-query>
              <div class="search-top">
                <button nz-button nzType="primary" [disabled]="!vehicle" (click)="creatByModal()">
                  <i nz-icon nzType="plus" nzTheme="outline"></i>
                  {{ '新建' | translate }}
                </button>
                <div class="query-int">
                  <button class="querybtn" (click)="executeQuery()">
                    <img src="/assets/font/search-logo.svg" alt="" />
                  </button>
                  <button class="clearbtn" (click)="getRefresh()" *ngIf="intext">×</button>
                  <input
                    id="input"
                    [(ngModel)]="intext"
                    type="text"
                    class="form-control form-control-sm query_input"
                    placeholder="{{ '请输入设备型号/设备号' | translate }}"
                    (keyup.enter)="executeQuery()"
                  />
                </div>
                <button
                  nz-button
                  nz-popover
                  nzPopoverTrigger="click"
                  nzPopoverPlacement="bottom"
                  [(nzPopoverVisible)]="filterVisible"
                  [nzPopoverContent]="filterPopover"
                  [nzPopoverOverlayStyle]="{ width: '140px' }"
                >
                  <img src="/assets/font/filter.png" alt="{{ '筛选' | translate }}" />
                </button>
                <button
                  type="button"
                  nz-button
                  (click)="getRefresh()"
                  nz-tooltip
                  nzTooltipPlacement="top"
                  nzTooltipTitle="{{ '刷新' | translate }}"
                >
                  <i class="la la-refresh"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="m-portlet__body p-0">
          <ngx-datatable
            class="material"
            [scrollbarH]="true"
            (select)="onEquipmentList($event)"
            [rows]="equipmentList"
            appNgxDataTable
            [saveState]="false"
            [selected]="equipmented"
            [loadingIndicator]="loading"
            [selectionType]="'checkbox'"
            (loadValue)="getEquipment($event)"
            ngxNoPageFooterWatcher
            [isRetainCurrentPageQuery]="false"
            [footer]="deviceFooter"
            [count]="deviceCurrentNumber"
            [columnMode]="'force'"
            [ngxQuery]="deviceNgxQuery"
            [selectAllRowsOnPage]="false"
            externalPaging="false"
          >
            <ngx-datatable-column
              [width]="40"
              [sortable]="false"
              [canAutoResize]="false"
              [draggable]="false"
              [resizeable]="true"
              [headerCheckboxable]="true"
              [checkboxable]="true"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备型号' | translate }}"
              prop="deviceType"
              [width]="160"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备号' | translate }}"
              prop="deviceNo"
              [width]="140"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备类型' | translate }}"
              prop="wireless"
              [width]="120"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <span *ngIf="row.wireless === '0'" translate>有线</span>
                <span *ngIf="row.wireless === '1'" translate>无线</span>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备状态' | translate }}"
              prop="status"
              [width]="120"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <nz-badge
                  [nzColor]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].backgroundColor"
                  [nzText]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].name | translate"
                ></nz-badge>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              [width]="210"
              name="{{ '传感器' | translate }}"
              prop="sensorConfig"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <a class="sensors">
                  <i
                    *ngFor="let sensor of row?.sensorConfig"
                    class="iot {{ sensorTypeInfo[sensor.type].iconNew }} sensor-icon-page"
                    nz-tooltip
                    nzTooltipTitle="{{ sensorTypeInfo[sensor.type].label | translate }}"
                  ></i>
                </a>
              </ng-template>
            </ngx-datatable-column>
          </ngx-datatable>
          <br />
          <div class="footer-style">
            <nopage-datatable-footer
              #deviceFooter
              [currentNumber]="deviceCurrentNumber"
              [totalNumber]="devicetotalNumber"
              (getTotal)="getDeviceTotal()"
              [checkTurnPage]="deviceTurnPage.bind(this)"
            ></nopage-datatable-footer>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-2" [ngStyle]="{ display: 'flex' }">
      <div class="form-group btnLocation" [ngStyle]="{ margin: 'auto' }">
        <button
          type="button"
          nz-button
          nzType="primary"
          nzBlock
          [ngStyle]="{ 'margin-bottom': '20px' }"
          [disabled]="!vehicle"
          (click)="addMenu()"
        >
          {{ '绑定' | translate }}
          <i nz-icon nzType="arrow-right" nzTheme="outline"></i>
        </button>
        <br />
        <button type="button" nz-button nzBlock [disabled]="!vehicle" (click)="removeMenu()">
          {{ '解绑' | translate }}
          <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
        </button>
      </div>
    </div>
    <div class="col-md-5">
      <div class="m-portlet">
        <div class="m-portlet__head">
          <div class="m-portlet__head-caption">
            <div class="m-portlet__head-tools">
              <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                  <span>
                    <span translate>已绑定设备数量:</span>
                    {{ bindDevicetotalNumber }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <!-- 已绑定设备列表 -->
        <div class="m-portlet__body p-0">
          <ngx-datatable
            class="material"
            [scrollbarH]="true"
            (select)="bindEquipment($event)"
            appNgxDataTable
            [rows]="bindEquipmentList"
            [saveState]="false"
            [loadingIndicator]="bindLoading"
            [selected]="bindEquipmented"
            [selectionType]="'checkbox'"
            (loadValue)="getbindEquipment($event)"
            ngxNoPageFooterWatcher
            [isRetainCurrentPageQuery]="false"
            [footer]="bindDeviceFooter"
            [count]="bindDeviceCurrentNumber"
            [columnMode]="'force'"
            [selectAllRowsOnPage]="false"
            externalPaging="false"
          >
            <ngx-datatable-column
              [width]="40"
              [sortable]="false"
              [canAutoResize]="false"
              [draggable]="false"
              [resizeable]="true"
              [headerCheckboxable]="true"
              [checkboxable]="true"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备型号' | translate }}"
              prop="deviceType"
              [width]="160"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备号' | translate }}"
              prop="deviceNo"
              [width]="140"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备类型' | translate }}"
              prop="wireless"
              [width]="120"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <span *ngIf="row.wireless === '0'" translate>有线</span>
                <span *ngIf="row.wireless === '1'" translate>无线</span>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '设备状态' | translate }}"
              prop="status"
              [width]="120"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <nz-badge
                  [nzColor]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].backgroundColor"
                  [nzText]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].name | translate"
                ></nz-badge>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              [width]="210"
              name="{{ '传感器' | translate }}"
              prop="sensorConfig"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <a class="sensors">
                  <i
                    *ngFor="let sensor of row?.sensorConfig"
                    class="iot {{ sensorTypeInfo[sensor.type].iconNew }} sensor-icon-page"
                    nz-tooltip
                    nzTooltipTitle="{{ sensorTypeInfo[sensor.type].label | translate }}"
                  ></i>
                </a>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '操作' | translate }}"
              prop="status"
              [width]="150"
              headerClass="text-left"
              cellClass="text-left"
            >
              <ng-template let-row="row" ngx-datatable-cell-template>
                <span *ngIf="row.empty" translate (click)="addInstallInfo(row.id, vehicleId)" class="install_info">
                  添加安装信息
                </span>
                <span *ngIf="!row.empty" translate (click)="addInstallInfo(row.id, vehicleId)" class="install_info">
                  安装信息
                </span>
              </ng-template>
            </ngx-datatable-column>
          </ngx-datatable>
          <br />
          <div class="footer-style">
            <nopage-datatable-footer
              #bindDeviceFooter
              [currentNumber]="bindDeviceCurrentNumber"
              [totalNumber]="bindDevicetotalNumber"
              (getTotal)="getBindDeviceTotal()"
              [checkTurnPage]="bindDeviceTurnPage.bind(this)"
            ></nopage-datatable-footer>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #filterPopover>
  <div nz-row [nzGutter]="[0, 8]">
    <div nz-col [nzSpan]="24">
      <nz-checkbox-wrapper nz-row [nzGutter]="[0, 8]">
        <div nz-col [nzSpan]="24" *ngFor="let item of deviceStatusList">
          <span nz-checkbox [nzValue]="item.value" [(ngModel)]="item.checked">
            {{ item.label | translate }}
          </span>
        </div>
      </nz-checkbox-wrapper>
    </div>
    <div nz-col [nzSpan]="24">
      <div nz-row nzAlign="middle" nzJustify="end" [nzGutter]="[8, 0]">
        <div nz-col>
          <button nz-button nzSize="small" (click)="reset()">{{ '重置' | translate }}</button>
        </div>
        <div nz-col>
          <button nz-button nzSize="small" nzType="primary" (click)="determine()">
            {{ '确定' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
