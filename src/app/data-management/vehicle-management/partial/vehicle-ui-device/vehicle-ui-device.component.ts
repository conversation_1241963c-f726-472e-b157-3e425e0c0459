import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { NgxQueryComponent, QueryMode } from '@zhongruigroup/ngx-query';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';

import { Logger, LoggerFactory } from '@app/core';
import { QueryTemplate } from '@app/shared/models/type';
import { Order } from '../../shared/models';

import {
  SensorConfig,
  sensorTypeInfo,
  DeviceStatus,
  deviceStatusInfo
} from '@app/data-management/device-management/shared/models/device';
import { VehicleService } from '@app/data-management/vehicle-management/shared/vehicle.service';
import { DeviceUiComponent } from '@app/data-management/device-management/partial/device-ui/device-ui.component';
import { InstallInfoComponent } from '../install-info/install-info.component';

@Component({
  selector: 'app-vehicle-ui-device',
  templateUrl: './vehicle-ui-device.component.html',
  styleUrls: ['./vehicle-ui-device.component.scss']
})
export class VehicleUiDeviceComponent implements OnInit {
  @Input() title: string;
  @Input() get vehicle(): any {
    return this._vehicle;
  }
  set vehicle(vehicle: any) {
    this._vehicle = vehicle;
    if (this.vehicle) {
      this.loadData = true;
      this.getRefresh();
    }
  }

  log: Logger;

  sensorTypeInfo = sensorTypeInfo;
  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  intext: string = ''; // 搜索清除
  filterDisplay: boolean = false; // 筛选
  loading = false;
  bindLoading = false;
  bindDevicetotalNumber: number;
  devicetotalNumber: number;
  equipmentList: Array<Order>;
  equipmented: Array<Order> = [];
  bindEquipmentList: Array<Order> = [];
  bindEquipmented: Array<Order> = [];
  deviceCurrentNumber = 10;
  bindDeviceCurrentNumber = 10;
  deviceEvent: any = {};
  page = {
    pageIndex: 1,
    pageSize: 10
  };
  deviceParams: any = {};

  filterVisible = false;
  deviceStatusList: Array<{ label: string; value: string; checked: boolean }> = [
    { label: '在线', value: '2', checked: false },
    { label: '离线', value: '1', checked: false },
    { label: '未启用', value: '0', checked: false }
  ];

  loadData: boolean = false;
  bindDeviceEvent: any;

  mode: QueryMode = QueryMode.plainCollapse;

  @ViewChild('deviceNgxQuery') deviceNgxQuery: NgxQueryComponent;
  @ViewChild('deviceFooter') deviceFooter: NoPageDatatableFooterComponent;

  deviceQueryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'status', op: 'eq' },
          { field: 'search', op: 'cn' }
        ],
        groups: []
      }
    }
  ];

  _vehicle: any;

  constructor(
    private vehicleService: VehicleService,
    private modalService: BsModalService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger();
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  // 回车输入查询未绑定列表
  executeQuery() {
    this.deviceEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.getEquipment(this.deviceEvent);
  }

  // 新建设备
  creatByModal() {
    const initialState = {
      orgId: this.vehicle.orgId
    };
    this.modalService.show(DeviceUiComponent, {
      ignoreBackdropClick: true,
      initialState: initialState,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.deviceNgxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 刷新
  getRefresh() {
    this.deviceEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.deviceParams = {};
    this.intext = '';
    this.equipmented = [];
    this.bindEquipmented = [];
    this.reset();
    this.getbindEquipment(this.deviceEvent);
  }

  // 筛选重置
  reset() {
    this.deviceStatusList = this.deviceStatusList.map((item) => {
      item.checked = false;
      return item;
    });
    this.determine(); // 重置后进行查询
  }

  // 多选查询
  determine() {
    const deviceStatus = this.deviceStatusList.filter((item) => item.checked).map((item) => item.value);
    this.deviceParams.status = deviceStatus;
    this.filterVisible = false;
    this.getEquipment(this.deviceEvent);
  }

  // 该车未绑定设备列表
  getEquipment(event: any) {
    this.deviceEvent = event;
    if (!this.loadData) {
      return;
    }
    this.deviceParams.pageIndex = event.page.pageIndex;
    this.deviceParams.pageSize = event.page.pageSize;
    this.deviceParams.id = this.vehicle.id;
    this.deviceParams.orgId = this.vehicle.orgId;
    this.deviceFooter.showTotalElements = true;
    this.deviceParams.query = this.intext;
    this.loading = true;
    this.vehicleService
      .getEquipment(this.deviceParams)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        if (res.code === '200') {
          res.data?.records?.forEach((item: any) => {
            // 去掉重复sensor类型
            let sensorConfig: SensorConfig[] = [];
            item.sensorConfig
              ?.filter((sensor: SensorConfig) => sensorTypeInfo[Number(sensor.type)])
              .forEach((sensor: SensorConfig) => {
                const isExits = sensorConfig.some((item) => item.type === Number(sensor.type));
                sensorConfig = isExits
                  ? [...sensorConfig]
                  : [...sensorConfig, { ...sensor, type: Number(sensor.type) }];
              });
            item.sensorConfig = sensorConfig;
          });

          this.equipmentList = res.data.records;
          this.deviceCurrentNumber = this.equipmentList.length;
        }
      });
  }

  getDeviceTotal() {
    if (!this.loadData) {
      this.devicetotalNumber = 0;
      return;
    }
    const params = {
      id: this.vehicle.id,
      orgId: this.vehicle.orgId,
      query: this.intext,
      status: this.deviceStatusList.filter((item) => item.checked).map((item) => item.value)
    };
    this.vehicleService.getDevicePage(params).subscribe((response) => {
      if (response.success) {
        this.devicetotalNumber = response.data;
      }
    });
  }

  loadProperty(page: any): any {
    const map: any = {};
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    if (page.filter) {
      const rules = page.filter.rules;
      rules.forEach((rule: { field: any; data: any }) => {
        const key = rule.field;
        map[key] = rule.data;
      });
    }
    return map;
  }

  // 未绑定翻页
  deviceTurnPage() {
    this.equipmented = [];
    return this.deviceNgxQuery.validateQuery();
  }

  // 表格
  onEquipmentList(event: any) {
    if (!event || !Array.isArray(event.selected)) {
      return;
    }
    if (event.selected.length === 0) {
      this.equipmented = [];
      return;
    }
    const equipmented = event.selected.filter((item: any) =>
      this.equipmented.every((selectedItem) => selectedItem.id !== item.id)
    );
    this.equipmented = [...this.equipmented, ...equipmented];
  }

  bindDeviceTurnPage() {
    this.bindEquipmented = [];
    return true;
  }

  bindEquipment(event: any) {
    if (!event || !Array.isArray(event.selected)) {
      return;
    }
    if (event.selected.length === 0) {
      this.bindEquipmented = [];
      return;
    }
    const bindEquipmented = event.selected.filter((item: any) =>
      this.bindEquipmented.every((selectedItem) => selectedItem.id !== item.id)
    );
    this.bindEquipmented = [...this.bindEquipmented, ...bindEquipmented];
  }

  //  移入到该车已绑定设备
  addMenu() {
    if (this.equipmented.length === 0) {
      this.translate.get('请先选择要绑定的设备').subscribe((res: string) => {
        this.log.error(res);
      });
      return;
    }
    const params = {
      list: Array.from(new Set(this.equipmented.map((item) => item.id))),
      id: this.vehicle.id
    };
    this.vehicleService.addDevice(params).subscribe((res) => {
      if (!res.success) {
        this.translate.get(res.message).subscribe((reason: string) => {
          this.log.error(reason);
        });
        return;
      }
      this.equipmented = [];
      this.bindEquipmented = [];
      this.getEquipment(this.deviceEvent);
      this.getbindEquipment(this.bindDeviceEvent);
    });
  }

  // 移除该车已绑定设备
  removeMenu() {
    if (this.bindEquipmented.length === 0) {
      this.translate.get('请先选择要移除的设备').subscribe((res: string) => {
        this.log.error(res);
      });
      return;
    }
    const params = {
      list: Array.from(new Set(this.bindEquipmented.map((item) => item.id))),
      id: this.vehicle.id
    };
    this.vehicleService.removeDevice(params).subscribe((response: any) => {
      if (response.success) {
        this.bindEquipmented = [];
        this.equipmented = [];
        this.getEquipment(this.deviceEvent);
        this.getbindEquipment(this.bindDeviceEvent);
      }
    });
  }

  // 该车已绑定设备列表
  getbindEquipment(event: any) {
    this.bindDeviceEvent = event;
    if (!this.loadData) {
      return;
    }
    const bindDeviceParams = this.bindDeviceEvent.page;
    const params = {
      pageIndex: bindDeviceParams.pageIndex,
      pageSize: bindDeviceParams.pageSize,
      vehicleId: Number(this.vehicle.id)
    };
    this.bindLoading = true;
    this.vehicleService
      .BindEquipment(params)
      .pipe(finalize(() => (this.bindLoading = false)))
      .subscribe((res) => {
        res?.data?.records?.forEach((item: any) => {
          // 去掉重复sensor类型
          let sensorConfig: SensorConfig[] = [];
          item.sensorConfig
            ?.filter((sensor: SensorConfig) => sensorTypeInfo[Number(sensor.type)])
            .forEach((sensor: SensorConfig) => {
              const isExits = sensorConfig.some((item) => item.type === Number(sensor.type));
              sensorConfig = isExits ? [...sensorConfig] : [...sensorConfig, { ...sensor, type: Number(sensor.type) }];
            });
          item.sensorConfig = sensorConfig;
        });
        this.bindEquipmentList = res?.data?.records || [];
        this.bindDeviceCurrentNumber = this.bindEquipmentList.length;
        this.getBindDeviceTotal();
      });
  }

  // 绑定设备总数
  getBindDeviceTotal() {
    if (!this.loadData) {
      this.bindDevicetotalNumber = 0;
      return;
    }
    const params = { vehicleId: this.vehicle.id };
    this.vehicleService.getBindDevicePage(params).subscribe((response: any) => {
      if (response) {
        this.bindDevicetotalNumber = response.data;
      }
    });
  }

  addInstallInfo(id: any, vehicleId: any) {
    const initialState = {
      deviceId: id,
      vehicleId: vehicleId
    };
    this.modalService.show(InstallInfoComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.deviceNgxQuery.executeQuery();
      this.getbindEquipment(this.bindDeviceEvent);
      onHidden.unsubscribe();
    });
  }
}
