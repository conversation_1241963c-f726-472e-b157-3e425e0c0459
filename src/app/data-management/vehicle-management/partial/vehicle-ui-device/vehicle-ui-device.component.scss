.title {
  display: flex;
  padding: 15px;

  span {
    font-size: 8px;
    margin-left: 10px;
    margin-top: 3px;
  }
}

.flag {
  color: #1e8ff8;
  padding-right: 15px;
}

.txt {
  color: black;
  font-weight: bold;
}

.ngx-datatable.fixed-header .datatable-header .datatable-header-inner .datatable-header-cell {
  flex: 1 !important;
}

input::-webkit-input-placeholder {
  font-size: 13px;
  color: #c0c0c0;
  text-indent: 10px;
  font-size: 13px;
}

.ngx-datatable .datatable-row-left,
.ngx-datatable .datatable-row-center,
.ngx-datatable .datatable-row-group,
.ngx-datatable .datatable-row-right {
  display: flex !important;
}

.ngx-datatable .datatable-body-row,
.ngx-datatable .datatable-row-center,
.ngx-datatable .datatable-header-inner {
  width: 0px !important;
}

.ngx-datatable.fixed-header .datatable-header .datatable-header-inner {
  width: 0px !important;
}

input:focus {
  outline: none;
  border-color: #9ecaed;
  box-shadow: 0 0 10px #9ecaed;
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  justify-content: flex-end;
  height: 50px;
  min-width: 200px;
}

.device-status {
  margin-left: 10px;
  width: 24px;
  height: 22px;
  font-size: 16px;
  text-align: left;
  color: #f1f4f8;
  line-height: 22px;
}

.lable {
  color: white;
  font-size: 12px;
  margin: 3px 0;
}

:host ::ng-deep .card-body {
  border: 0px !important;
}

.search-top {
  display: flex;
  align-items: center;

  .query-int {
    margin: 0 10px;
    flex: 1;
    position: relative;
    background: #f1f3f8;

    .query_input {
      border-radius: 5px;
      border-color: #f1f3f8;
      background: #f1f3f8;
      width: 100%;
    }

    .clearbtn {
      position: absolute;
      right: 43px;
      font-size: 20px;
      width: 41px;
      height: 38px;
      border: none;
      border-radius: 5px;
      outline: none;
      cursor: pointer;
      background: #f1f3f8;
    }

    .querybtn {
      position: absolute;
      right: 1px;
      border: none;
      border-radius: 5px;
      width: 41px;
      height: 36px;
      margin-top: 1px;
      outline: none;
      cursor: pointer;
      background: #f1f3f8;

      .la {
        margin-top: 3px;
      }
    }
  }

  .btn-new {
    width: 96px;

    span {
      padding-left: 0;
    }
  }
}

.btnLocation {
  text-align: center;
}

.btn-creat {
  margin-left: 0 !important;
}

:host ::ng-deep .dropmenu {
  display: none !important;
}

.question-mark {
  padding-bottom: 4px;
  cursor: pointer;
}

.content {
  padding: 0 40px;
  background: #ffffff;
  border-radius: 14px;
  box-shadow: 2px 0px 6px 0px rgba(203, 209, 228, 0.28);
  color: #454c62;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;

  .m-portlet .m-portlet__head {
    border: 0;
  }
}

.install_info {
  color: #4777fd;
  line-height: 14px;
  font-size: 14px;
  font-weight: Regular;
  font-family: PingFangSC;
  cursor: pointer;
  white-space: normal;
  word-break: break-word;
}

:host .ngx-datatable.material {
  min-height: 0 !important;
}

.col-md-5 {
  min-width: 415px;
}

.sensors {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  text-decoration: none;

  i {
    width: 24px;
    padding: 2px;
  }
}
