import { Component, OnInit, Input, EventEmitter, Output, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { of } from 'rxjs';
import { catchError, filter, finalize, map, switchMap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { PageMode } from '@app/shared/models/page-mode';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { VehicleService } from '@app/data-management/vehicle-management/shared/vehicle.service';
import { regex } from '@app/shared/utils/regex';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { OwnersSelectComponent } from '@app/data-management/owners-management/partial/components/owners-select/owners-select.component';
import { OwnersManagementService } from '@app/data-management/owners-management/shared/owners-management.service';
import { AccountService } from '@app/account-settings/services/account.service';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-vehicle-ui-form',
  templateUrl: './vehicle-ui-form.component.html',
  styleUrls: ['./vehicle-ui-form.component.scss']
})
export class VehicleUiFormComponent implements OnInit {
  @Input() pageMode = PageMode.Add;
  @Input() title = '新建';
  @Input() saving = false;
  @Input() get vehicle(): any {
    return this._vehicle;
  }
  set vehicle(vehicle: any) {
    this._vehicle = vehicle;
    if (vehicle) {
      this.form.reset({
        plateNumber: { value: vehicle.plateNumber, disabled: this.pageMode === PageMode.Add },
        modelName: { value: vehicle.modelName, disabled: this.pageMode === PageMode.Add },
        vin: { value: vehicle.vin, disabled: this.pageMode === PageMode.Add },
        iconName: { value: vehicle.iconName || 'default', disabled: this.pageMode === PageMode.Add }
      });

      this.form.get('ownerName').setValue(vehicle.ownerName);
      this.form.get('phoneNumber').setValue(vehicle.phoneNumber);
      this.form.get('city').setValue(vehicle.city);
      this.form.get('email').setValue(vehicle.email);

      this.form.get('orgId').reset({ value: vehicle.orgId, disabled: true });
      this.labelIds.setValue(vehicle.labelIds, { emitViewToModelChange: false });
      if (vehicle.carOwnerId) {
        this.getOwnerInfo(vehicle.carOwnerId);
      }
      if (this.pageMode === PageMode.Add) {
        this.isDisabled = true;
        this.labelIds.disable();
      }
    }
  }

  @Output() save = new EventEmitter<any>();

  log: Logger;
  PageMode = PageMode;
  nodes: Array<any> = []; // 组织机构
  labelList: any[] = []; // 标签列表
  iconList = [
    { value: 'default', url: '/assets/media/app/img/map/default.png' },
    { value: 'vehicle', url: '/assets/media/app/img/map/vehicle.png' },
    { value: 'truck', url: '/assets/media/app/img/map/truck.png' },
    { value: 'taxi', url: '/assets/media/app/img/map/taxi.png' },
    { value: 'motorbike', url: '/assets/media/app/img/map/motorbike.png' },
    { value: 'electromobile', url: '/assets/media/app/img/map/electromobile.png' },
    { value: 'human', url: '/assets/media/app/img/map/human.png' },
    { value: 'cat', url: '/assets/media/app/img/map/cat.png' },
    { value: 'dog', url: '/assets/media/app/img/map/dog.png' }
  ];

  form: FormGroup;
  ownerObj: any = {};
  isDisabled: boolean = false;
  loading = true;

  get labelIds(): FormControl {
    return this.form.get('labelIds') as FormControl;
  }

  // 标签不超过三个
  confirmationValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length > 3) {
      return { confirm: true, error: true };
    }
    return null;
  };

  _vehicle: any;

  constructor(
    private cd: ChangeDetectorRef,
    private vehicleService: VehicleService,
    private formBuilder: FormBuilder,
    private translate: TranslateService,
    private modalService: BsModalService,
    private referenceDataService: ReferenceDataService,
    private loggerFactory: LoggerFactory,
    private ownersManagementService: OwnersManagementService,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant(this.title));
    this.buildForm();
  }

  ngOnInit() {
    this.getLabels().subscribe();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.loading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.loading = false;
    });
  }
  getOwnerInfo(id: string) {
    this.ownersManagementService.getOwnerDetail(id).subscribe((res) => {
      this.ownerObj = res.data || {};
    });
  }
  submit() {
    if (this.saving) {
      return;
    }
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    const params = this.form.getRawValue();
    this.save.next({ ...params, carOwnerId: this.ownerObj.id || undefined });
  }
  onClose() {
    this.ownerObj = {};
    this.form.get('ownerName').setValue(undefined);
    this.form.get('phoneNumber').setValue(undefined);
    this.form.get('city').setValue(undefined);
    this.form.get('email').setValue(undefined);
  }
  getValue(field: string) {
    return this.form.get(field).value;
  }
  selectOwners() {
    const orgId = this.form.get('orgId').value;
    if (!orgId && (!this._vehicle || !this._vehicle.orgId)) {
      this.log.warn(this.translate.instant('请先选择所属机构'));
      return;
    }
    const initialState = { organId: orgId || (this._vehicle && this._vehicle.orgId) };
    const modalRef: BsModalRef = this.modalService.show(OwnersSelectComponent, {
      initialState,
      class: 'modal-account-settings',
      ignoreBackdropClick: true
    });
    modalRef.content.action.subscribe((value: any) => {
      if (value) {
        this.ownerObj = value;
        this.form.get('ownerName').setValue(value.carOwnerName);
        this.form.get('phoneNumber').setValue(value.contact);
        this.form.get('city').setValue(value.cityName);
        this.form.get('email').setValue(value.email);
        this.cd.markForCheck();
      }
    });
  }
  // 获取标签
  getLabels() {
    return this.vehicleService.getLabelList().pipe(
      map((res) => {
        if (res.success) {
          this.labelList = Array.isArray(res.data) ? res.data : this.labelList;
        }
        return null;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.loading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService
        .getUserChildrenOrganizations(id)
        .pipe(finalize(() => this.cd.markForCheck()))
        .subscribe((organNodes) => {
          if (organizationId) {
            // 超管获取子级
            resolve(organNodes[0].children);
          } else {
            // 非超管直接获取子级组织机构树
            this.nodes = organNodes;
          }
          this.loading = false;
        });
    });
  }

  private buildForm() {
    this.form = this.formBuilder.group({
      orgId: [null, [Validators.required]],
      labelIds: [null, [this.confirmationValidator]],
      plateNumber: [null, [Validators.required, Validators.pattern(regex.bookString)]],
      modelName: [null, [Validators.required]],
      vin: [null, []],
      iconName: ['default', [Validators.required]],

      carOwnerId: [null, []],
      ownerName: [null, [Validators.required, Validators.pattern(regex.bookString)]],
      phoneNumber: [null],
      city: [null],
      email: [null]
    });

    // 模糊过滤不存在时,添加当前模糊搜索的内容为标签
    this.labelIds.valueChanges
      .pipe(
        filter((labelIds) => Array.isArray(labelIds)),
        map((labelIds) =>
          labelIds.filter((labelId: number | string) => typeof labelId === 'string' && !!labelId.trim())
        ),
        filter((labelNames) => labelNames.length === 1),
        switchMap((labelNames: string[]) => {
          let labelName = '';
          return this.vehicleService.addLabelList(labelNames[0].trim()).pipe(
            map((res) => {
              if (res.code === '200') {
                this.log.success(this.translate.instant['新增成功']);
                labelName = labelNames[0].trim();
              }
              return labelName;
            }),
            catchError(() => of(labelName))
          );
        }),
        switchMap((labelName) => {
          if (!labelName) {
            return of(labelName);
          }
          return this.getLabels().pipe(
            map(() => labelName),
            catchError(() => of(labelName))
          );
        }),
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe((labelName) => {
        const labelIds = this.labelIds.value;
        labelIds.pop();
        if (labelName) {
          const addItem = this.labelList.find((item) => item.labelName === labelName);
          if (addItem) {
            labelIds.push(addItem.id);
          }
        }
        this.labelIds.setValue(labelIds);
      });
  }
}
