.title {
  display: flex;
  padding: 15px;

  span {
    font-size: 8px;
    margin-left: 10px;
    margin-top: 3px;
  }

  .flag {
    color: #1e8ff8;
    padding-right: 15px;
  }

  .txt {
    color: black;
    font-weight: bold;
  }
}

select:invalid {
  color: gray;
}

nz-select {
  width: 100%;
}

.vehicle,
.owner {
  margin-top: 15px;
  background: #ffffff;
  border-radius: 14px;
  box-shadow: 2px 0px 6px 0px rgba(203, 209, 228, 0.28);

  .vehicle-form,
  .owner-form {
    padding: 0 183px;

    .ant-form-item-label {
      width: 120px;
      text-align: left;
    }

    ::ng-deep.ant-form-item label {
      font-family: PingFangSC, PingFangSC-Regular;
      color: #575e72;
      font-weight: 400;
    }

    nz-form-control {
      width: 351px;
    }
  }
}

.text-label-es {
  width: 150px !important;
}

.text-right {
  text-align: right;
  width: 100%;
  padding-right: 30px;
  margin: 24px 0;
}

// 选择图标
.query-icon {
  font-size: 12px;
  width: 82px;
  height: 27px;
  line-height: 23px;
  margin-left: 36px;
}

.query-icon:hover {
  border: 1px solid #5590ff !important;
  color: #5590ff !important;
  background: #ffffff !important;
}

.ant-radio-group {
  width: 351px;
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(4, 25%);
  grid-template-rows: repeat(3, 36px);
  align-items: center;
  position: relative;
}

.icon-img {
  width: 62px;
  display: inline-block;
}

.bg-white {
  background-color: #fff;
}

::ng-deep.ant-dropdown {
  left: -98px !important;
  top: 10px !important;
}

.selected-box {
  width: 100%;
  min-width: 0;
  padding: 7px 11px 4px;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-radius: 3.5px;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .content {
    flex: 1;
  }

  &.disabled-box {
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
}

::ng-deep .modal-account-settings {
  width: 90%;
  max-width: 1100px;
  min-width: 1000px;

  .account-setting-tabs > .ant-tabs-bar {
    .ant-tabs-nav .ant-tabs-tab {
      font-weight: 500;
      font-size: 16px;
    }
  }
  .ant-tabs-content-holder {
    position: relative;
  }
}
.tip-gray {
  color: #999898;
}
::ng-deep .ant-tag-geekblue {
  max-width: 100%;
  white-space: normal;
}
.owner-form-detail {
  margin-bottom: 24px;

  .detail-row > label {
    width: 120px;
    color: #575e72;
  }
}

.no-data {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
