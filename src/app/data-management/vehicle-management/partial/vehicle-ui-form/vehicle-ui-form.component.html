<form nz-form [formGroup]="form" (ngSubmit)="submit()" class="ant-advanced-search-form">
  <div class="vehicle">
    <div class="title">
      <div class="flag">
        <img src="/assets/font/blue_small_tip.png" alt="" />
      </div>
      <div class="txt" translate>车辆信息</div>
    </div>

    <div class="vehicle-form">
      <div nz-row [nzGutter]="24">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired nzFor="orgId" class="text-label-es">
              {{ '所属机构' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="orgIdErrorTpl">
              <nz-tree-select
                class="w-full"
                formControlName="orgId"
                id="orgId"
                [nzNodes]="nodes"
                nzShowSearch
                nzShowLine
                nzPlaceHolder="{{ '请选择所属机构' | translate }}"
                [nzAsyncData]="true"
                [nzNotFoundContent]="noData"
                (nzExpandChange)="expandChange($event)"
              ></nz-tree-select>
              <ng-template #noData>
                <div *ngIf="loading" class="no-data">
                  <nz-spin nzSimple></nz-spin>
                </div>
                <div *ngIf="!loading" class="no-data">
                  {{ '暂无数据' | translate }}
                </div>
              </ng-template>
              <ng-template #orgIdErrorTpl let-control>
                {{ '请选择所属机构' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzFor="labels" class="text-label-es">
              {{ '标签' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="labelIdsErrorTpl">
              <nz-select
                class="w-full"
                formControlName="labelIds"
                id="labelIds"
                nzMode="tags"
                nzPlaceHolder="{{ '请选择标签，最多不超过三个' | translate }}"
              >
                <nz-option
                  *ngFor="let option of labelList"
                  [nzLabel]="option.labelName"
                  [nzValue]="option.id"
                ></nz-option>
              </nz-select>
              <ng-template #labelIdsErrorTpl let-control>
                {{ '最多可选择3个标签，请重新选择' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div nz-row [nzGutter]="24">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired nzFor="plateNumber" translate class="text-label-es">
              {{ '车牌号' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="plateNumberErrorTpl">
              <input
                type="text"
                nz-input
                maxlength="50"
                formControlName="plateNumber"
                id="plateNumber"
                placeholder="{{ '请输入车牌号' | translate }}"
              />
              <ng-template #plateNumberErrorTpl let-control>
                {{ '请输入正确格式的车牌号' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired nzFor="modelName" translate class="text-label-es">
              {{ '车辆型号' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="modelNameErrorTpl">
              <input
                type="text"
                nz-input
                formControlName="modelName"
                id="modelName"
                placeholder="{{ '请输入车辆型号' | translate }}"
              />
              <ng-template #modelNameErrorTpl let-control>
                {{ '请输入正确的车辆型号' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div nz-row [nzGutter]="24">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzFor="vin" translate class="text-label-es">
              {{ '车架号' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="vinErrorTpl">
              <input
                type="text"
                nz-input
                formControlName="vin"
                id="vin"
                maxlength="50"
                placeholder="{{ '请输入车架号' | translate }}"
              />
              <ng-template #vinErrorTpl let-control>
                {{ '请输入正确的车架号' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>

        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired nzFor="icon" translate class="text-label-es">
              {{ '图标' | translate }}
            </nz-form-label>
            <nz-form-control>
              <span class="icon-img">
                <img [src]="'/assets/media/app/img/map/' + form.get('iconName').value + '.png'" />
              </span>
              <button
                type="button"
                nz-button
                nz-dropdown
                nzTooltipPlacement="bottomLeft"
                [nzDropdownMenu]="iconListTpl"
              >
                {{ '选择图标' | translate }}
              </button>
              <nz-dropdown-menu #iconListTpl="nzDropdownMenu">
                <div class="ant-dropdown-menu">
                  <nz-radio-group formControlName="iconName">
                    <label *ngFor="let item of iconList" nz-radio [nzValue]="item.value">
                      <img [src]="item.url" [alt]="item.value" />
                    </label>
                  </nz-radio-group>
                </div>
              </nz-dropdown-menu>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </div>
  </div>

  <div class="owner">
    <div class="title">
      <div class="flag">
        <img src="/assets/font/blue_small_tip.png" alt="" />
      </div>
      <div class="txt" translate>车主信息</div>
    </div>

    <div class="owner-form">
      <div nz-row [nzGutter]="24">
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <!-- <nz-form-label nzFor="ownerName" translate>
              {{ '车主名' | translate }}
            </nz-form-label> -->
            <nz-form-control [nzErrorTip]="ownerNameErrorTpl">
              <input type="hidden" formControlName="ownerName" />
              <div class="selected-box">
                <div class="content">
                  <ng-container *ngIf="ownerObj && ownerObj.email">
                    <nz-tag
                      [nzColor]="isDisabled ? '' : 'geekblue'"
                      [nzMode]="isDisabled ? 'default' : 'closeable'"
                      (nzOnClose)="onClose()"
                    >
                      {{ ownerObj.carOwnerName | isNull }} - {{ ownerObj.contact | isNull }} -
                      {{ ownerObj.email | isNull }} - {{ ownerObj.cityName | isNull }}
                    </nz-tag>
                  </ng-container>
                  <span *ngIf="!ownerObj || !ownerObj.email" class="tip-gray" translate>请选择车主</span>
                </div>

                <a nz-button nzType="primary" (click)="selectOwners()" [nzSize]="'small'">
                  {{ 'select' | translate }}
                </a>
              </div>
              <ng-template #ownerNameErrorTpl let-control>
                {{ '请选择车主' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="24" class="owner-form-detail">
          <div nz-row [nzGutter]="24">
            <div class="detail-row" nz-col [nzSpan]="12">
              <label>{{ '车主姓名' | translate }}：</label>
              <div class="detail-value">
                {{ ownerObj.carOwnerName || getValue('ownerName') | isNull }}
              </div>
            </div>
            <div class="detail-row" nz-col [nzSpan]="12">
              <label>{{ '联系电话' | translate }}：</label>
              <div class="detail-value">
                {{ ownerObj.contact || getValue('phoneNumber') | isNull }}
              </div>
            </div>
            <div class="detail-row" nz-col [nzSpan]="12">
              <label>{{ '邮箱' | translate }}：</label>
              <div class="detail-value">
                {{ ownerObj.email | isNull }}
              </div>
            </div>
            <div class="detail-row" nz-col [nzSpan]="12">
              <label>{{ '所在城市' | translate }}：</label>
              <div class="detail-value">
                {{ ownerObj.cityName || getValue('city') | isNull }}
              </div>
            </div>
          </div>
        </div>
        <!-- <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzRequired nzFor="ownerName" translate>
              {{ '姓名' | translate }}
            </nz-form-label>
            <nz-form-control [nzErrorTip]="ownerNameErrorTpl">
              <input
                type="text"
                nz-input
                formControlName="ownerName"
                id="ownerName"
                maxlength="12"
              />
              <ng-template #ownerNameErrorTpl let-control>
                {{ '请选择车主' | translate }}
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzFor="phoneNumber" translate>
              {{ '联系电话' | translate }}
            </nz-form-label>
            <nz-form-control>
              <input nz-input type="text" formControlName="phoneNumber" id="phoneNumber" maxlength="50" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="12">
          <nz-form-item>
            <nz-form-label nzFor="city" translate>
              {{ '所在城市' | translate }}
            </nz-form-label>
            <nz-form-control>
              <input type="text" nz-input formControlName="city" id="city" maxlength="100" />
            </nz-form-control>
          </nz-form-item>
        </div> -->
      </div>
    </div>
  </div>

  <div class="text-right">
    <button type="button" nz-button routerLink="/vehicleManagement">{{ '取消' | translate }}</button>
    <button
      type="submit"
      nz-button
      nzType="primary"
      [nzLoading]="saving"
      [disabled]="pageMode === PageMode.Add && vehicle"
    >
      {{ '保存' | translate }}
    </button>
  </div>
</form>
