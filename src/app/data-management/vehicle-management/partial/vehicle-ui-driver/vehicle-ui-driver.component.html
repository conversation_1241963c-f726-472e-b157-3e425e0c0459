<div class="content">
  <div class="row">
    <div class="title">
      <div class="flag"><img src="/assets/font/blue_small_tip.png" alt="" /></div>
      <div class="txt" translate>
        绑定驾驶员
        <img
          class="question-mark"
          src="/assets/font/question-mark.svg"
          nz-tooltip
          nzTooltipPlacement="top"
          nzTooltipTitle="{{ '选填' | translate }}"
        />
      </div>
    </div>
  </div>
  <br />
  <div class="row">
    <div class="col-md-5">
      <div class="m-portlet">
        <div class="m-portlet__head">
          <div class="m-portlet__head-tools">
            <div class="toolbar">
              <ngx-query
                [hidden]="true"
                [datePickerReadonly]="false"
                [columnNumber]="1"
                class="full-screen no-header"
                #driverNgxQuery
                [queryTemplates]="driverQueryTemplates"
                [isRetainQueryMode]="true"
                [showModeButtons]="true"
                [mode]="mode"
                [showPlainCollapseToolBar]="false"
              >
                <ngx-query-field [name]="'search'" [label]="''" [type]="'string'">
                  <ng-template
                    ngx-query-value-input-template
                    dataType="text"
                    let-rules="rules"
                    let-rule="rule"
                    let-dataIndex="dataIndex"
                  >
                    <button class="querybtn btn">
                      <i class="la la-search"></i>
                    </button>
                    <input
                      type="text"
                      [(ngModel)]="rule.datas[dataIndex]"
                      class="form-control form-control-sm query_input"
                      placeholder="{{ '驾驶员姓名/联系电话/所属公司' | translate }}"
                      (keyup.enter)="screenQuery()"
                    />
                  </ng-template>
                </ngx-query-field>
              </ngx-query>
              <div class="search-top">
                <button nz-button nzType="primary" [disabled]="!vehicle" (click)="creatByDriver()">
                  <i nz-icon nzType="plus" nzTheme="outline"></i>
                  {{ '新建' | translate }}
                </button>
                <div class="query-int">
                  <button class="querybtn" (click)="screenQuery()">
                    <img src="/assets/font/search-logo.svg" alt="" />
                  </button>
                  <button class="clearbtn" (click)="driverReflash()" *ngIf="intext02">×</button>
                  <input
                    id="input02"
                    [(ngModel)]="intext02"
                    type="text"
                    class="form-control form-control-sm query_input"
                    placeholder="{{ '请输入驾驶员姓名、联系电话、所属公司' | translate }}"
                    (keyup.enter)="screenQuery()"
                  />
                </div>
                <button
                  type="button"
                  nz-button
                  (click)="driverReflash()"
                  nz-tooltip
                  nzTooltipPlacement="top"
                  nzTooltipTitle="{{ '刷新' | translate }}"
                >
                  <i class="la la-refresh"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="m-portlet__body p-0">
          <ngx-datatable
            class="material"
            [scrollbarH]="true"
            (select)="onDrivered($event)"
            [ngxQuery]="driverNgxQuery"
            appNgxDataTable
            [rows]="dirverlist"
            [saveState]="false"
            [loadingIndicator]="loading02"
            [selected]="dirvered"
            [isRetainCurrentPageQuery]="false"
            [selectionType]="'checkbox'"
            ngxNoPageFooterWatcher
            [footer]="dirverFooter"
            [count]="driverCurrentNumber"
            (loadValue)="getDriver($event)"
            [columnMode]="'force'"
            [selectAllRowsOnPage]="false"
            externalPaging="false"
          >
            <ngx-datatable-column
              [width]="40"
              [sortable]="false"
              [canAutoResize]="false"
              [draggable]="false"
              [resizeable]="true"
              [headerCheckboxable]="true"
              [checkboxable]="true"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '驾驶员姓名' | translate }}"
              prop="userName"
              [width]="80"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '联系电话' | translate }}"
              prop="phone"
              [width]="40"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '所属公司' | translate }}"
              prop="orgName"
              [width]="60"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
          </ngx-datatable>
          <br />
          <div class="footer-style">
            <nopage-datatable-footer
              #dirverFooter
              [currentNumber]="driverCurrentNumber"
              [totalNumber]="drivertotalNumber"
              (getTotal)="getDriverTotal()"
              [checkTurnPage]="deviceTurnPage.bind(this)"
            ></nopage-datatable-footer>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-2" [ngStyle]="{ display: 'flex' }">
      <div class="form-group btnLocation" [ngStyle]="{ margin: 'auto' }">
        <button
          type="button"
          nz-button
          nzType="primary"
          nzBlock
          [ngStyle]="{ 'margin-bottom': '20px' }"
          [disabled]="!vehicle"
          (click)="addDriver()"
        >
          {{ '绑定' | translate }}
          <i nz-icon nzType="arrow-right" nzTheme="outline"></i>
        </button>
        <br />
        <button type="button" nz-button nzBlock [disabled]="!vehicle" (click)="removeDriver()">
          {{ '解绑' | translate }}
          <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
        </button>
      </div>
    </div>
    <div class="col-md-5">
      <div class="m-portlet">
        <div class="m-portlet__head">
          <div class="m-portlet__head-caption">
            <div class="m-portlet__head-tools">
              <ul class="m-portlet__nav">
                <li class="m-portlet__nav-item">
                  <span>
                    <span translate>已绑定驾驶员数量:</span>
                    {{ bindDrivertotalNumber }}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="m-portlet__body p-0">
          <ngx-datatable
            #bindDriverdt
            class="material"
            [scrollbarH]="true"
            (select)="onbindDrivered($event)"
            [rows]="bindDriverlist"
            [saveState]="false"
            [loadingIndicator]="loading02"
            [selected]="bindDrivered"
            appNgxDataTable
            [isRetainCurrentPageQuery]="false"
            [selectionType]="'checkbox'"
            (loadValue)="getBindDriverList($event)"
            ngxNoPageFooterWatcher
            [footer]="bindDriverFooter"
            [count]="bindDriverCurrentNumber"
            [columnMode]="'force'"
            [selectAllRowsOnPage]="false"
            externalPaging="false"
          >
            <ngx-datatable-column
              [width]="40"
              [sortable]="false"
              [canAutoResize]="false"
              [draggable]="false"
              [resizeable]="true"
              [headerCheckboxable]="true"
              [checkboxable]="true"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '驾驶员姓名' | translate }}"
              prop="userName"
              [width]="80"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '联系电话' | translate }}"
              prop="phone"
              [width]="40"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
            <ngx-datatable-column
              name="{{ '所属公司' | translate }}"
              prop="orgName"
              [width]="60"
              headerClass="text-left"
              cellClass="text-left"
            ></ngx-datatable-column>
          </ngx-datatable>
          <br />
          <div class="footer-style">
            <nopage-datatable-footer
              #bindDriverFooter
              [currentNumber]="bindDriverCurrentNumber"
              [totalNumber]="bindDrivertotalNumber"
              (getTotal)="getBindDriberTotal()"
              [checkTurnPage]="deviceTurnPage.bind(this)"
            ></nopage-datatable-footer>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
