import { AfterViewInit, ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { QueryTemplate } from '@app/shared/models/type';
import { NgxQueryComponent, QueryMode } from '@zhongruigroup/ngx-query';
import { BsModalService } from 'ngx-bootstrap/modal';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { Order } from '../../shared/models';
import { VehicleService } from '../../shared/vehicle.service';
import { finalize } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { DriverUiComponent } from '@app/data-management/driver-management/partial/driver-ui/driver-ui.component';

@Component({
  selector: 'app-vehicle-ui-driver',
  templateUrl: './vehicle-ui-driver.component.html',
  styleUrls: ['./vehicle-ui-driver.component.scss']
})
export class VehicleUiDriverComponent implements OnInit {
  @Input() title: string;
  @Input() get vehicle(): any {
    return this._vehicle;
  }
  set vehicle(vehicle: any) {
    this._vehicle = vehicle;
    if (this._vehicle) {
      this.loadData = true;
      this.driverReflash();
    }
  }

  log: Logger;
  intext02: string;
  loading02 = false;
  dirvered: Array<Order> = [];
  driverCurrentNumber = 10;
  drivertotalNumber: number;
  dirverlist: Array<any> = [];
  bindDrivertotalNumber: number;
  bindDriverlist: Array<Order>;
  bindDriverCurrentNumber = 10;
  bindDrivered: Array<Order> = [];
  driverEvent: any = {};
  page = {
    pageIndex: 1,
    pageSize: 10
  };
  driverParams: any = {};
  selectId: any;
  loadData: boolean = false;
  dirverId: Array<any> = [];
  bindDriverEvent: any;
  filterDisplay: boolean = false;

  mode: QueryMode = QueryMode.plainCollapse;
  driverQueryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'search', op: 'cn' }],
        groups: []
      }
    }
  ];

  @ViewChild('driverNgxQuery') driverNgxQuery: NgxQueryComponent;
  @ViewChild('dirverFooter') dirverFooter: NoPageDatatableFooterComponent;

  _vehicle: any;

  constructor(
    private vehicleService: VehicleService,
    private translate: TranslateService,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.translate.get('新建').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  // 回车查询驾驶员
  screenQuery() {
    this.driverEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.driverParams.userName = this.intext02;
    this.getDriver(this.driverEvent);
  }

  // 新建驾驶员
  creatByDriver() {
    const initialState = {
      orgId: this.vehicle.orgId
    };
    this.modalService.show(DriverUiComponent, {
      ignoreBackdropClick: true,
      initialState,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.driverNgxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 点击×号
  // 刷新
  driverReflash() {
    this.driverEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.driverParams = {};
    this.intext02 = '';
    this.getDriver(this.driverEvent);
    this.getBindDriverList(this.driverEvent);
  }

  // 该车未绑定驾驶员信息列表  左边的列表
  onDrivered(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.dirvered = event.selected;
      this.selectId = this.dirvered[0].id;
    }
  }

  // 未绑定驾驶员
  getDriver(event: any) {
    this.driverEvent = event;
    if (!this.loadData) {
      return;
    } else {
      this.driverParams.pageIndex = event.page.pageIndex;
      this.driverParams.pageSize = event.page.pageSize;
      this.driverParams.orgId = this.vehicle.orgId;
      this.dirverFooter.showTotalElements = true;
      this.dirvered.length = 0;
      this.loading02 = true;
      this.driverParams.id = this.vehicle.id;
      this.vehicleService
        .getDriverList(this.driverParams)
        .pipe(finalize(() => (this.loading02 = false)))
        .subscribe(
          (res) => {
            if (res.code === '200') {
              this.dirverlist = res.data.records;
              this.driverCurrentNumber = this.dirverlist.length;
              console.log(this.dirverlist);
              //   this.getBindDriberTotal();
            } else {
              console.log('驾驶员列表获取失败。', res.msg);
            }
          },
          (error) => console.log('驾驶员列表获取失败', error)
        );
    }
  }

  // 已绑定驾驶员总数
  getBindDriberTotal() {
    if (!this.loadData) {
      this.bindDrivertotalNumber = 0;
      return;
    }
    const params = {
      vehicleId: this.vehicle.id,
      orgId: this.vehicle.orgId
    };
    console.log(params);

    this.vehicleService
      .getBindDriverPage(params)
      .pipe(finalize(() => (this.loading02 = false)))
      .subscribe(
        (response: any) => {
          if (response.success) {
            this.bindDrivertotalNumber = response.data;
          } else {
            console.log('驾驶员列表总数获取失败。');
          }
        },
        (error) => console.log('驾驶员列表总数获取失败。', error)
      );
  }

  // 未绑定驾驶员列表总数
  getDriverTotal() {
    if (!this.loadData) {
      this.drivertotalNumber = 0;
      return;
    }
    const params = {
      vehicleId: this.vehicle.id,
      orgId: this.vehicle.orgId,
      userName: this.intext02
    };
    this.vehicleService
      .getDriverPage(params)
      .pipe(finalize(() => (this.loading02 = false)))
      .subscribe(
        (res: any) => {
          if (res.code === '200') {
            this.drivertotalNumber = res.data;
          } else {
            console.log('驾驶员列表总数获取失败。');
          }
        },
        (error) => console.log('驾驶员列表总数获取失败。', error)
      );
  }

  // 未绑定翻页
  deviceTurnPage() {
    return this.driverNgxQuery.validateQuery();
  }

  //  移入该车绑定驾驶员  selectId
  addDriver() {
    this.dirverId = [];
    if (this.dirvered.length !== 0) {
      for (let i = 0; i < this.dirvered.length; i++) {
        this.dirverId.push(this.dirvered[i].id);
      }
      const params = {
        userIds: this.dirverId,
        vehicleId: this.vehicle.id
      };
      this.vehicleService
        .addDrivers(params)
        .pipe(finalize(() => (this.loading02 = false)))
        .subscribe(
          (res) => {
            if (res.code === '200') {
              this.getBindDriverList(this.bindDriverEvent);
              this.getDriver(this.driverEvent);
              this.dirvered = [];
            }
          },
          (error) => console.log('移入失败。', error)
        );
    } else {
      this.translate.get('请先选择该车未绑定驾驶员').subscribe((res: string) => {
        this.log.error(res);
      });
    }
  }

  //  移除该车绑定驾驶员
  removeDriver() {
    this.dirverId = [];
    for (let i = 0; i < this.bindDrivered.length; i++) {
      this.dirverId.push(this.bindDrivered[i].id);
    }
    if (!this.dirverId.length == null || this.dirverId.length !== 0) {
      const params = {
        userIds: this.dirverId,
        vehicleId: this.vehicle.id
      };
      this.vehicleService
        .removeDrivers(params)
        .pipe(finalize(() => (this.loading02 = false)))
        .subscribe(
          (response) => {
            if (response) {
              this.getDriver(this.driverEvent);
              // this.bindDriverlist = this.bindDriverlist.filter(item => {return item.id!=this.removeDelectId})
              this.getBindDriverList(this.bindDriverEvent);
              this.dirvered = [];
            }
          },
          (error) => console.log('移除失败。', error)
        );
    } else {
      this.translate.get('请先选择该车绑定驾驶员').subscribe((res: string) => {
        this.log.error(res);
      });
    }
  }

  display() {
    this.filterDisplay = !this.filterDisplay;
  }

  // 该车已绑定驾驶员多选列表   右边的列表
  onbindDrivered(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.bindDrivered = event.selected;
    }
  }

  // 已绑定驾驶员
  getBindDriverList(event: any) {
    this.bindDriverEvent = event;
    if (!this.loadData) {
      return;
    } else {
      const params: any = {
        pageIndex: event.page.pageIndex,
        pageSize: event.page.pageSize,
        id: this.vehicle.id
      };
      this.loading02 = true;
      this.vehicleService
        .getBindDriver(params)
        .pipe(finalize(() => (this.loading02 = false)))
        .subscribe(
          (res: any) => {
            this.bindDriverlist = res.data.records;
          },
          (error) => console.log('绑定驾驶员列表获取失败', error)
        );
    }
  }
}
