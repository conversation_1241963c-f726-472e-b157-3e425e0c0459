<div class="tabs" *ngIf="tabIndex > -1">
  <div class="tab-header">
    <button
      *ngFor="let item of tabs; let i = index"
      [appApplyPermission]="item.appApplyPermission"
      [class.active]="i === tabIndex"
      (click)="changeTabIndex(i)"
    >
      {{ item.name | translate }}
    </button>
  </div>

  <ng-container *ngComponentOutlet="tabs[tabIndex]?.component"></ng-container>
</div>

<!-- <ng-template #noData>
  <nz-empty class="no-data m-portlet" nzNotFoundImage="/assets/font/noDate.png" [nzNotFoundContent]="contentTpl">
    <ng-template #contentTpl>
      <span translate>暂无数据</span>
    </ng-template>
  </nz-empty>
</ng-template> -->
