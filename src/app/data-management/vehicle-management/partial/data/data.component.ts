import { AfterViewInit, Component, OnInit } from '@angular/core';

import { ApplyPermissionService } from '@app/shared/services/apply-permission.service';
import { VehicleIndexComponent } from '../vehicle-index/vehicle-index.component';
import { DeviceIndexComponent } from '@app/data-management/device-management/partial/device-index/device-index.component';
import { DriverIndexComponent } from '@app/data-management/driver-management/partial/driver-index/driver-index.component';
import { OwnersIndexComponent } from '@app/data-management/owners-management/partial/owners-index/owners-index.component';

@Component({
  selector: 'app-data',
  templateUrl: './data.component.html',
  styleUrls: ['./data.component.scss']
})
export class DataComponent implements OnInit, AfterViewInit {
  tabIndex = -1;
  tabs: Array<any> = [
    { name: '车辆管理', appApplyPermission: 'vehicle', component: VehicleIndexComponent },
    { name: '设备管理', appApplyPermission: 'device', component: DeviceIndexComponent },
    { name: '驾驶员管理', appApplyPermission: 'driver', component: DriverIndexComponent },
    { name: '车主管理', appApplyPermission: 'owner', component: OwnersIndexComponent }
  ];

  constructor(private applyPermissionService: ApplyPermissionService) {}

  ngOnInit() {
    // this.updateTabs();
    // if (this.tabs.length > 0) {
    //   this.changeTabIndex(0);
    // }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabs();
      this.changeTabIndex(this.tabs.length ? 0 : -1);
    }, 200);
  }

  changeTabIndex(i: number) {
    this.tabIndex = i;
  }

  updateTabs() {
    const widgets = this.applyPermissionService.getPageWidgets();
    this.tabs = this.tabs.filter((item) => {
      return widgets.some((widget) => widget.id === item.appApplyPermission && widget.authorised);
    });
  }
}
