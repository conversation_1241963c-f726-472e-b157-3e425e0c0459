import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { finalize, map } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { PageMode } from '@app/shared/models/page-mode';
import { VehicleService } from '@app/data-management/vehicle-management/shared/vehicle.service';

@Component({
  selector: 'app-vehicle-edit',
  templateUrl: './vehicle-edit.component.html',
  styleUrls: ['./vehicle-edit.component.scss']
})
export class VehicleEditComponent implements OnInit {
  id: number;

  log: Logger;
  vehicleId: any;
  title = '编辑车辆';
  saving = false;
  PageMode = PageMode;
  vehicle: any;

  constructor(
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private vehicleService: VehicleService
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant(this.title));
  }

  ngOnInit() {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.id = id;
        this.load();
      }
    });
  }

  submit(vehicle: any) {
    this.saving = true;
    vehicle.id = this.id;
    this.vehicleService
      .updateVehicle(vehicle)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (res.success) {
            return;
          }
          this.log.error(this.translate.instant('信息修改失败'), this.translate.instant(res.message || ''));
        },
        (error) => this.log.error(this.translate.instant('信息修改失败'))
      );
  }

  load() {
    this.vehicleService.getDriverDetial(this.id).subscribe(
      (res) => {
        if (!res.success) {
          return;
        }
        res.data = res.data || {};
        res.data.labelIds = res.data.labelList?.map((item: { labelId: number }) => item.labelId);
        this.vehicle = res.data;
      },
      (error) => console.log(`数据获取失败,失败信息: ${error}`)
    );
  }
}
