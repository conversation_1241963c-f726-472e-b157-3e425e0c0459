import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { VehicleService } from '@app/data-management/vehicle-management/shared/vehicle.service';

@Component({
  selector: 'app-vehicle-creat',
  templateUrl: './vehicle-creat.component.html',
  styleUrls: ['./vehicle-creat.component.scss']
})
export class VehicleCreatComponent implements OnInit {
  log: Logger;
  title = '新建';
  vehicle: any;
  saving = false;

  constructor(
    private translate: TranslateService,
    private vehicleService: VehicleService,
    private loggerFactory: LoggerFactory
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant(this.title));
  }

  ngOnInit() {}

  submit(vehicle: any) {
    this.saving = true;
    this.vehicleService
      .add(vehicle)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (!res || !res.success || !res.data) {
            this.log.error(this.translate.instant(res.message || '新增失败'));
            return;
          }
          this.vehicle = { ...vehicle, id: res.data };
        },
        (error) => this.log.error(this.translate.instant('新增失败'))
      );
  }
}
