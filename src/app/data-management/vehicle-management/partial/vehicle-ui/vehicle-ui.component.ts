import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { PageMode } from '@app/shared/models/page-mode';

@Component({
  selector: 'app-vehicle-ui',
  templateUrl: './vehicle-ui.component.html',
  styleUrls: ['./vehicle-ui.component.scss']
})
export class VehicleUiComponent implements OnInit {
  @Input() pageMode: PageMode = PageMode.Add;
  @Input() title: string;
  @Input() saving: boolean;
  @Input() vehicle: any;
  @Output() save = new EventEmitter<any>();

  @ViewChild('device') device: any;

  get vehicleId() {
    return this.vehicle?.id;
  }

  log: Logger;

  constructor(private translate: TranslateService, private loggerFactory: LoggerFactory) {}

  ngOnInit() {
    this.log = this.loggerFactory.getLogger(this.translate.instant('安装信息'));
  }

  submit(e: any) {
    this.save.next(e);
  }

  back() {
    let isSomeDeviceInstallEmpty = this.device.bindEquipmentList.some((item: any) => item.empty === 0);
    if (isSomeDeviceInstallEmpty) {
      this.log.error(this.translate.instant('请先填写已绑定设备的安装信息'));
      document.getElementById('span').scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
      return;
    }
    window.history.back();
  }
}
