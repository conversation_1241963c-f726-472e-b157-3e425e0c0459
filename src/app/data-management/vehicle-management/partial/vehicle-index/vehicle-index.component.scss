:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  /* flex-direction: column; */
  height: 50px;
  position: static !important;
  /* width: 15%; */
  /* margin-left: 80%; */
  /* right: 0; */
  justify-content: flex-end;
}

:host ::ng-deep .col-6 {
  position: static;
}

.interval {
  // padding: 0px 72px 0 0;
  width: 80%;
  padding-top: 20px;
}

:host ::ng-deep .float-right {
  // display: none !important;
  position: absolute;
  right: -20%;
  top: -50px;
}

.travel {
  color: #4777fd;
  border: 1px solid;
  padding: 1px 7px 1px 7px;
  margin-right: 5px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 400;
}

.carType {
  width: 80%;
  display: inline-flex;

  .address {
    width: 100%;
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

.transfer-icon {
  width: 19px;
  height: 20px;
}

:host ::ng-deep .m-portlet__head {
  display: flex !important;
  flex-direction: row-reverse;
  align-content: center;
  // height: 100%;
}

.no-data {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
