<div class="m-portlet list_header">
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [columnNumber]="3"
      [datePickerReadonly]="false"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
      (reset)="reset($event)"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [nzAsyncData]="true"
            [nzNotFoundContent]="noData"
            (nzExpandChange)="expandChange($event)"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
          <ng-template #noData>
            <div *ngIf="treeLoading" class="no-data">
              <nz-spin nzSimple></nz-spin>
            </div>
            <div *ngIf="!treeLoading" class="no-data">
              {{ '暂无数据' | translate }}
            </div>
          </ng-template>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'plateNumber'" label="{{ '车牌号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车牌号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'labelIds'" label="{{ '标签' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="labelIds"
            [(ngModel)]="rule.datas[dataIndex]"
            (ngModelChange)="selectMax($event)"
            nzMode="multiple"
            nzPlaceHolder="{{ '请选择标签' | translate }}"
            nzShowSearch
          >
            <nz-option
              *ngFor="let option of listOfOption"
              [nzLabel]="option.labelName"
              [nzValue]="option.id"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'modelName'" label="{{ '车辆型号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车辆型号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'vin'" label="{{ '车架号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车架号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'ownerName'" label="{{ '车主名' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车主名' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<!--  -->
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-tools">
      <ul class="m-portlet__nav" style="float: left">
        <li class="m-portlet__nav-item" *ngIf="!dialog" [appApplyPermission]="'vehicle_import'">
          <button (click)="importArchiveBorrow()" nz-button nzType="primary">
            {{ '批量导入' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'vehicle_add'">
          <button (click)="newvehicle()" nz-button>
            {{ '新建' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'vehicle_export'">
          <button (click)="exportReport()" nz-button>
            {{ '导出' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'install_info'">
          <button (click)="importInstallInfo()" nz-button>
            {{ '批量绑定' | translate }}
          </button>
        </li>
      </ul>
    </div>
  </div>
  <!--  -->
  <div class="m-portlet__body p-0">
    <!--
      (select)="onSelect($event)"
      [selected]="vehicleSelected"
      [selectionType]="'checkbox'"
    -->
    <ngx-datatable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="vehicleList"
      appNgxDataTable
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="getVehicleList($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
      [columnMode]="'force'"
    >
      <ngx-datatable-column
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        headerClass="text-left"
        cellClass="text-left"
        [width]="200"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.orgName }}">{{ row.orgName }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '车牌号' | translate }}"
        prop="plateNumber"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="carType">
            <div class="address" title="{{ row.plateNumber }}">{{ row.plateNumber }}</div>
          </div>
          <img
            class="img-car"
            [src]="vehicleStatusInfo[row?.status || VehicleStatus.Offline].legendIcon"
            nz-tooltip
            nzTooltipPlacement="top"
            nzTooltipTitle="{{ vehicleStatusInfo[row?.status || VehicleStatus.Offline].name | translate }}"
          />
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="{{ '标签' | translate }}" prop="plateNumber" [width]="200">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span class="travel" *ngFor="let item of row.labelList">{{ item.labelName }}</span>
          <span *ngIf="(row.labelList && !row.labelList.length) || !row.labelList">-</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '车辆型号' | translate }}"
        prop="modelName"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '车架号' | translate }}"
        prop="vin"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.vin }}">{{ row.vin }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="100"
        name="{{ '总里程' | translate }}"
        prop="mileage"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span>{{ row.mileage }}km</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="180"
        name="{{ '绑定设备数量' | translate }}"
        prop="count"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '车主名' | translate }}"
        prop="ownerName"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '联系电话' | translate }}"
        prop="phoneNumber"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="220"
        name="{{ '操作' | translate }}"
        prop="total"
        headerClass="text-center"
        cellClass="text-center"
        [frozenRight]="true"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span appApplyPermission="vehicle_detail" class="margin_right" (click)="checkDetail(row)">
            <a>
              <img
                src="/assets/font/detail.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '详情' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="vehicle_detail" class="margin_right">|</span>

          <span appApplyPermission="vehicle_edit" class="margin_right" (click)="editDetail(row)">
            <a>
              <img
                src="/assets/font/edit.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '编辑' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="vehicle_edit" class="margin_right">|</span>

          <span class="margin_right" (click)="delete(row)" [appApplyPermission]="'vehicle_delete'">
            <a>
              <img
                src="/assets/font/delete.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '删除' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="group_transfer" class="margin_right">|</span>

          <span class="margin_right" (click)="transfer(row)" [appApplyPermission]="'group_transfer'">
            <a>
              <img
                class="transfer-icon"
                src="/assets/font/group-transfer.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '组织迁移' | translate }}"
              />
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [width]="10" headerClass="datatable-header-cell-acitons text-left" [frozenRight]="true">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
