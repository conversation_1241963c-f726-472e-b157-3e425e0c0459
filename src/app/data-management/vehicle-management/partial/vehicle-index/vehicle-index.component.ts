import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { finalize } from 'rxjs/operators';

import { max } from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BsModalService } from 'ngx-bootstrap/modal';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';

import { environment } from '@env/environment';
import { Logger } from '@core/logger.service';
import { Dialogs } from '@core/dialogs.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { VehicleService } from '../../shared/vehicle.service';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate, RowDetailToggleEvent } from '@app/shared/models/type';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';

import { vehicleStatusInfo, VehicleStatus } from '../../shared/models/vehicle';
import { Language } from '@app/account-settings/models/account';

// import { accountSetting } from '@app/account-settings/models/account-setting';
import { ImportFileComponent } from '@app/shared';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';
import { GroupChangeComponent } from '../group-change/group-change.component';

@Component({
  selector: 'app-vehicle-index',
  templateUrl: './vehicle-index.component.html',
  styleUrls: ['./vehicle-index.component.scss']
})
export class VehicleIndexComponent implements OnInit, AfterViewInit {
  log: Logger;
  vehicleStatusInfo = vehicleStatusInfo;
  VehicleStatus = VehicleStatus;

  mode: QueryMode = QueryMode.plainCollapse;
  selectList: Array<any> = [];
  loading = false;
  vehicleList: Array<any>;
  vehicleSelected: Array<any> = [];
  notHideColumns: string[] = ['price'];
  isShowCollapse = false;
  dialog = false;
  event: any;
  currentNumber = 10;
  totalNumber: number;
  listOfOption: any[];
  nodes: Array<any> = [];
  labelIds: any[];
  datatable: any;
  originTemplate: any;
  treeLoading = true;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'plateNumber', op: 'eq' },
          { field: 'labelIds', op: 'eq' },
          { field: 'modelName', op: 'eq' },
          { field: 'vin', op: 'eq' },
          { field: 'ownerName', op: 'eq' }
        ],
        groups: []
      }
    }
  ];

  subjectName: string = '车牌号';
  previousPage = 0; // 上一页
  nextPage = 0; // 下一页

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  constructor(
    private dialogs: Dialogs,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef,
    private vehicleService: VehicleService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private router: Router,
    private message: NzMessageService,
    private accountService: AccountService
  ) {
    this.translate.get('编号').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
    this.checkFilters(); // 回显过滤条件
  }

  ngOnInit() {
    this.getLabels();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  ngAfterViewInit(): void {
    this.changeDetectorRef.detectChanges();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  // 获取标签
  getLabels() {
    this.vehicleService.getLabelList().subscribe(
      (res) => {
        if (res.success) {
          // console.log(***************, res.data);
          this.listOfOption = res.data;
        } else {
          console.log('标签列表获取失败', res.msg);
        }
      },
      (error) => console.log('标签列表获取失败', error)
    );
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
        this.treeLoading = false;
      });
    });
  }

  onSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.vehicleSelected = event.selected;
    }
  }

  refreshData() {
    this.getVehicleList(this.event);
  }

  getVehicleList(event: any) {
    this.event = event;
    const page = event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.footer.showTotalElements = true;
    this.vehicleSelected.length = 0;
    this.vehicleService
      .getVehicleList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.data.records && response.data.records.length !== 0) {
            let maxOrgname = 0,
              maxIabel = 0;
            response.data.records.forEach((item: any) => {
              maxOrgname = item.orgName.length > maxOrgname ? item.orgName.length : maxOrgname;
              if (item.labelList) {
                let labelNum = 0;
                item.labelList.forEach((ctem: { labelName: string | any[] }) => {
                  labelNum = ctem.labelName.length + labelNum;
                  console.log(maxIabel);
                });
                maxIabel = max([maxIabel, labelNum]);
              }
            });
            maxOrgname = Math.max.apply(null, [14 * maxOrgname + 34, 100]);
            maxIabel = Math.max.apply(null, [14 * maxIabel + 34, 100]);
            this.table.headerComponent.onColumnResized(maxOrgname, this.table.headerComponent.columns[0]);
            this.table.headerComponent.onColumnResized(maxIabel, this.table.headerComponent.columns[2]);
            // this.nextPage = maxOrgname + maxIabel;
          }
          this.vehicleList = response.data.records;
          // console.log(this.previousPage > this.nextPage);
          // if (this.nextPage <= this.previousPage) {
          //   const ev: any = { scrollYPos: 0, scrollXPos: 0 };
          //   this.table.bodyComponent.onBodyScroll(ev);
          // }
          // this.previousPage = this.nextPage;
          this.currentNumber = this.vehicleList ? this.vehicleList.length : 0;
          this.getTotal();
        },
        (error) => console.log('车辆列表获取失败', error)
      );
  }

  delete(row: any) {
    this.translate.get(`真的要删除吗？`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.vehicleService.deleteOne(row).subscribe(
            (result) => {
              if (result.success) {
                this.translate.get(`删除成功`).subscribe((res) => {
                  this.log.success(res);
                });
              } else {
                this.translate.get(result.message).subscribe((res) => {
                  this.log.error(res);
                });
              }
              this.ngxQuery.executeQuery();
            },
            (error) => console.log(`信息 ${row.ownerName} 删除失败，失败信息：`, error)
          );
        },
        () => console.log(`取消删除信息 ${row.ownerName}`)
      );
    });
  }

  transfer(row: any) {
    const initialState = { id: row.id, orgId: row.orgId, event: this.event };
    this.modalService.show(GroupChangeComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 新建
  newvehicle() {
    sessionStorage.setItem('vehicleManagement', JSON.stringify(this.event.query.query));
    this.router.navigate(['/vehicleManagement/new']);
  }
  // 查看详情
  checkDetail(row: any) {
    sessionStorage.setItem('vehicleManagement', JSON.stringify(this.event.query.query));
    this.router.navigate(['/vehicleManagement/' + row.id]);
  }
  // 编辑
  editDetail(row: any) {
    sessionStorage.setItem('vehicleManagement', JSON.stringify(this.event.query.query));
    this.router.navigateByUrl('/vehicleManagement/' + row.id + '/edit');
  }
  // 返回 保留筛选条件
  checkFilters() {
    const arr = environment.prePath.split('&');
    const prePage = arr[arr.length - 1];
    if (prePage && prePage.search('vehicleManagement') !== -1) {
      const template = sessionStorage.getItem('vehicleManagement');
      const obj = JSON.parse(template);
      if (obj) {
        this.queryTemplates[0].template = obj;
      }
    } else {
      environment.prePath = '';
    }
    sessionStorage.removeItem('vehicleManagement');
  }

  // 重置查询模板
  reset($event: any) {
    this.queryTemplates = this.originTemplate;
  }

  onDetailToggle(event: RowDetailToggleEvent<any>) {
    console.log('Detail Toggled', event);
  }

  toggleExpandRow(row: any) {
    console.log('Toggled Expand Row!', row);
    this.datatable.rowDetail.toggleExpandRow(row);
  }
  // 导入
  importArchiveBorrow() {
    // Language[accountSetting.language];
    const lang = window.localStorage.getItem('lang');
    let language = '';
    if (lang === 'zh-CN') {
      language = 'cn';
    }
    if (lang === 'en-US') {
      language = 'en';
    }
    if (lang === 'es-MX') {
      language = 'esp';
    }
    const initialState = {
      tplUrl: `glcrm-vehicle-api/v1/api/vehicle/template/${language}`,
      uploadUrl: `glcrm-vehicle-api/v1/api/vehicle/importVehicle`
    };
    this.modalService.show(ImportFileComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom',
      initialState
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  importInstallInfo() {
    // const lang = Language[accountSetting.language];
    const lang = window.localStorage.getItem('lang');
    let language = '';
    if (lang === 'zh-CN') {
      language = 'cn';
    }
    if (lang === 'en-US') {
      language = 'en';
    }
    if (lang === 'es-MX') {
      language = 'esp';
    }
    const initialState = {
      title: '批量绑定',
      tplUrl: `glcrm-vehicle-api/v1/api/vehicle/batchtemplate/${language}`,
      // uploadUrl: `glcrm-vehicle-api/v1/api/vehicle/importBatchData/${language}`
      uploadUrl: `glcrm-vehicle-api/v1/api/vehicle/importBatchData`
    };
    this.modalService.show(ImportFileComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom',
      initialState
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }
  // 导出
  exportReport() {
    const curDate: any = new Date();
    const param = this.loadProperty(this.event.page);

    this.vehicleService
      .exportReport(param)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          const link = document.createElement('a');
          const blob = new Blob([response.body], { type: 'application/xlsx' }),
            doloadUrl = window.URL.createObjectURL(blob);
          link.setAttribute('href', doloadUrl);
          // tslint:disable-next-line: max-line-length
          this.translate.get('车辆信息').subscribe((res: string) => {
            link.setAttribute(
              'download',
              res +
                curDate.getFullYear() +
                (curDate.getMonth() + 1) +
                curDate.getDate() +
                curDate.getHours() +
                curDate.getMinutes() +
                curDate.getSeconds() +
                '.xlsx'
            );
          });

          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (error) => {
          if (error === 'OK') {
            // 此时说明是因为超过2000条失败
            console.log('导出失败:', '导出数量超过2000条');
          } else {
            console.log('导出失败:', error);
          }
        }
      );
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  loadProperty(page: any) {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      map[key] = rule.data;
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    this.labelIds = map.labelIds;
    return map;
  }

  getTotal() {
    const page = this.event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.vehicleService
      .getPageNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.success) {
            this.totalNumber = response.data;
          } else {
            console.log('设备列表总数获取失败。', response.msg);
          }
        },
        (error) => console.log('设备列表总数获取失败。', error)
      );
  }

  // 折叠展开工具栏
  showCollapse() {
    this.isShowCollapse = !this.isShowCollapse;
    console.log(`isShowCollapse`, this.isShowCollapse);
    this.ngxQuery.showCollapse(this.isShowCollapse);
  }

  selectMax(e: any) {
    if (e.length > 3) {
      this.labelIds.pop();
      this.translate.get('只能选择3个标签进行查询').subscribe((res: string) => {
        this.message.create('warning', res);
      });
    }
  }
}
