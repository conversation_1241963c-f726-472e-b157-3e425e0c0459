/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { InstallInfoComponent } from './install-info.component';

describe('InstallInfoComponent', () => {
  let component: InstallInfoComponent;
  let fixture: ComponentFixture<InstallInfoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [InstallInfoComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InstallInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
