import { Component, ElementRef, OnInit, TemplateRef, ViewChild, ViewContainerRef, Input } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { format } from 'date-fns';

import { Logger, LoggerFactory } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { VehicleService } from '../../shared/vehicle.service';

interface InstallImg {
  id: number;
  smallUrl: string;
  bigUrl: string;
  text: string;
}

@Component({
  selector: 'app-install-info',
  templateUrl: './install-info.component.html',
  styleUrls: ['./install-info.component.scss']
})
export class InstallInfoComponent implements OnInit {
  @Input() deviceId: string;
  @Input() vehicleId: string;

  log: Logger;
  accountSetting = accountSetting;
  form: FormGroup;

  installImgs: InstallImg[] = []; // 图片集合
  img: any;
  picWain: string = ''; // 图片警告
  remarkWain: string = ''; // 备注警告
  @ViewChild('upImg') upImgRef: ElementRef;

  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private vehicleService: VehicleService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {
    this.getInstallInfo();
  }

  getInstallInfo() {
    this.vehicleService.getInstallInfo(this.deviceId).subscribe((response) => {
      if (response.code === '200') {
        const info = response.data || {};
        info.vehicleId = info.vehicleId || this.vehicleId;
        info.deviceId = info.deviceId || this.deviceId;
        info.installTime = info.installTime ? new Date(info.installTime) : null;
        this.form.patchValue(info);
        if (info.simNo) {
          this.form.get('simNo').reset({ value: info.simNo, disabled: true });
        }
        if (info.imageInfoList) {
          info.imageInfoList.forEach((item: any) => {
            const img: InstallImg = {
              id: item.id,
              smallUrl: item.thumbnailPath,
              bigUrl: item.fileUrl,
              text: item.fileRemark
            };
            this.installImgs.push(img);
          });
        }
      }
    });
  }

  remarkChange() {
    this.remarkWain = '';
  }

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.installImgs.length === 0) {
      this.picWain = '请先上传图片';
      this.translate.get('请先上传图片').subscribe((res: string) => {
        this.picWain = res;
      });
      return false;
    }
    if (this.form.invalid) {
      return;
    }
    let flag = true;
    let picIds: any[] = [];
    this.installImgs?.forEach((item) => {
      if (!item.text || item.text === '') {
        this.remarkWain = '请先填写备注';
        this.translate.get(this.remarkWain).subscribe((res: string) => {
          this.remarkWain = res;
        });
        flag = false;
      }
      picIds = [...picIds, { fileRemark: item.text, id: item.id }];
    });
    if (!flag) {
      return false;
    }
    const params = this.form.getRawValue();
    params.imageInfoList = picIds;
    params.installTime = format(params.installTime, 'yyyy-MM-dd');
    this.vehicleService.savaInstallInfo(params).subscribe((res) => {
      if (res.success) {
        this.activeModal.hide();
      }
    });
  }

  uploadImg(event: any) {
    if (!event) {
      return;
    }
    let flag = false;
    for (const item of event.target.files) {
      if (item.size / (1024 * 1024) > 10) {
        this.translate.get('上传图片大小不能超过10M').subscribe((res: string) => {
          this.log.error(res);
        });
        flag = true;
      }
    }
    if (flag) {
      return false;
    }
    const formData = new FormData();
    for (let i = 0; i < event.target.files.length; i++) {
      formData.append('images', event.target.files[i]);
    }
    formData.append('vehicleDeviceId', this.form.value.id);
    this.vehicleService.upImg(formData).subscribe((response) => {
      if (response.code === '200') {
        const data = response.data;
        this.picWain = '';
        data.forEach((element: any) => {
          const item: InstallImg = {
            id: element.id,
            smallUrl: element.thumbnailPath,
            bigUrl: element.fileUrl,
            text: ''
          };
          if (this.installImgs.length < 8) {
            this.installImgs.push(item);
          }
        });
      }
    });
  }

  openImg(url: string) {
    window.open(url);
  }

  clickImg() {
    this.img = '';
    this.upImgRef.nativeElement.value = '';
    this.upImgRef.nativeElement.click();
  }

  deleteImg(id: number) {
    this.installImgs = this.installImgs.filter((item) => {
      return id !== item.id;
    });
  }

  // 安装时间
  validInstallTime = (control: FormControl): { [s: string]: boolean } => {
    const date = new Date();
    const date1 = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + (date.getDate() + 1);
    if (!control.value) {
      return { required: true };
    } else if (control.value > date1) {
      return { confirm: true, error: true };
    }
    return {};
  };

  buildForm() {
    const phoneNumberReg = /^((0\d{2,3}-\d{7,8})|(1[3|4|5|6|7|8|9][0-9]\d{8}))$/;
    this.form = this.formBuilder.group({
      simNo: [null, [Validators.required]],
      installTime: [null, [Validators.required, this.validInstallTime]],
      installPerson: [null, [Validators.required, Validators.maxLength(50)]],
      installPersonTel: [null, [Validators.required]],
      installAddr: [null, [Validators.required, Validators.maxLength(70)]],
      installLocation: [null, [Validators.required, Validators.maxLength(50)]],

      id: [null], // 安装信息id
      deviceId: [null], // 设备id
      vehicleId: [null], // 车辆id
      imageInfoList: [null]
    });
  }
}
