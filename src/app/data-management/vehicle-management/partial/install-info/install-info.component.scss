:host {
  display: block;
  width: 735px;
}

.modal-body {
  height: 510px;
  max-height: calc(100vh - 120px) !important;
  padding: 30px 80px 0 !important;
  overflow-y: auto;
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  width: 32%;
  white-space: normal;
}

.ant-col-sm-14 {
  width: 60.333333%;
}

.pictures {
  display: grid;
  grid-template-columns: repeat(4, 24.5%);
  grid-row-gap: 20px;

  .input {
    width: 84px;
    height: 28px;
    background: #ffffff;
    border: 1px solid #d3d6de;
    border-radius: 4px;

    font-size: 12px;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 12px;
    padding-left: 7px;
  }

  .picture {
    width: 84px;
    height: 84px;
    border: 1px solid #d3d6de;
    border-radius: 4px;
    cursor: pointer;
  }

  .picture_item {
    width: 84px;
    height: 84px;
    background: #ffffff;
    border: 1px solid #d3d6de;
    border-radius: 4px;
    background-image: url(../../../../../assets/font/add.png);
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;

    .font {
      margin-left: 30px;
      margin-top: 57px;
      width: 24px;
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #cccccc;
      line-height: 14px;
    }
  }

  .close {
    margin-top: -91px;
  }

  .redinput {
    border-color: red;
  }
}

.wain {
  color: red;
  font-size: 14px;
  font-weight: 300;
  line-height: 21px;
}
