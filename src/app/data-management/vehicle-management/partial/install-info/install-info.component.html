<div class="modal-header">
  <h5 class="modal-title" translate>新建</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <form nz-form [formGroup]="form">
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="simNo" translate>
        {{ 'SIM卡号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="simNoErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="simNo"
          id="simNo"
          placeholder="{{ '请输入SIM卡号' | translate }}"
        />
        <ng-template #simNoErrorTpl let-control>
          {{ '请输入正确的SIM卡号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="installTime" translate>
        {{ '安装日期' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="installTimeErrorTpl">
        <nz-date-picker
          class="w-full"
          [nzFormat]="accountSetting.dateFormat"
          nzPlaceHolder="{{ '请选择正确的安装时间' | translate }}"
          formControlName="installTime"
          id="installTime"
          appLocalNzDateTime
        ></nz-date-picker>
        <ng-template #installTimeErrorTpl let-control>
          {{ '请选择正确的安装时间' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="installPerson" translate>
        {{ '安装人员' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="installPersonErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="installPerson"
          id="installPerson"
          placeholder="{{ '请输入安装人员' | translate }}"
        />
        <ng-template #installPersonErrorTpl let-control>
          {{ '请输入安装人员' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="installPersonTel" translate>
        {{ '联系电话' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="installPersonTelErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="installPersonTel"
          id="installPersonTel"
          placeholder="{{ '请输入联系电话' | translate }}"
        />
        <ng-template #installPersonTelErrorTpl let-control>
          {{ '请输入联系电话' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="installAddr" translate>
        {{ '安装地址' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="installAddrErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="installAddr"
          id="installAddr"
          placeholder="{{ '请输入安装地址' | translate }}"
        />
        <ng-template #installAddrErrorTpl let-control>
          {{ '请输入安装地址' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="installLocation" translate>
        {{ '安装位置' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="installLocationErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="installLocation"
          id="installLocation"
          placeholder="{{ '请输入安装位置，如后备箱左侧、副驾驶' | translate }}"
        />
        <ng-template #installLocationErrorTpl let-control>
          {{ '请输入位置' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" nzRequired [nzXs]="24" translate>
        {{ '安装图片' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="14" [nzXs]="24">
        <div class="pictures">
          <input
            type="file"
            #upImg
            id="upImg"
            (change)="uploadImg($event)"
            multiple
            [hidden]="true"
            accept="image/png, image/jpeg"
          />
          <div *ngFor="let item of installImgs; let i = index">
            <div class="picture">
              <img [src]="item.smallUrl" (click)="openImg(item.bigUrl)" />
            </div>
            <img class="close" src="/assets/font/x.png" (click)="deleteImg(item.id)" />
            <input
              class="input"
              (change)="remarkChange()"
              [class.redinput]="!installImgs[i].text || installImgs[i].text === ''"
              [(ngModel)]="installImgs[i].text"
              [ngModelOptions]="{ standalone: true }"
              type="text"
              placeholder=" {{ '请输入备注' | translate }}"
            />
          </div>

          <div class="picture_item" (click)="clickImg()" *ngIf="installImgs.length < 8">
            <div class="font">{{ installImgs.length }}/8</div>
          </div>
        </div>
        <div class="wain">{{ picWain }}</div>
        <div class="wain">{{ remarkWain }}</div>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>

<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
