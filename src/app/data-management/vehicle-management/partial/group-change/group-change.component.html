<div class="modal-header">
  <h5 class="modal-title">{{ '组织迁移' | translate }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <div class="tip">{{ '仅支持将车辆和设备转移到新组。调整后请重新绑定车主和司机信息' | translate }}</div>
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="tenantId">
        {{ '所属机构' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="tenantIdErrorTpl">
        <nz-tree-select
          class="w-full"
          [nzNodes]="nodes"
          nzShowSearch
          nzShowLine
          nzPlaceHolder="{{ '请选择所属机构' | translate }}"
          [nzAsyncData]="true"
          (nzExpandChange)="expandChange($event)"
          id="orgId"
          formControlName="orgId"
        ></nz-tree-select>
        <ng-template #tenantIdErrorTpl let-control>
          {{ '请选择租户名称' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
