import { Component, OnInit, Input } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { LoggerFactory } from '@app/core/logger-factory.service';
import { Logger } from '@app/core/logger.service';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { VehicleService } from '../../shared/vehicle.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-group-change',
  templateUrl: './group-change.component.html',
  styleUrls: ['./group-change.component.scss']
})
export class GroupChangeComponent implements OnInit {
  @Input() id: number;
  @Input() orgId: string;
  @Input() event: any;
  log: Logger;
  saving = false;
  form: FormGroup;
  nodes: Array<any> = [];
  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private accountService: AccountService,
    private vehicleService: VehicleService,
    private router: Router,
    private loggerFactory: LoggerFactory
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit(): void {
    this.form.get('id').patchValue(this.id);
    this.form.get('orgId').patchValue(this.orgId);
    this.getUserInfo();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
    });
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
      });
    });
  }
  submit() {
    if (this.saving) {
      return;
    }

    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }

    if (this.form.invalid) {
      return;
    }
    const params = this.form.value;
    // console.log('params', params)
    this.saving = true;
    this.vehicleService.groupTransfer(params).subscribe((res: any) => {
      if (!res.success) {
        this.translate.get(res.message).subscribe((res: string) => {
          this.log.error(res);
          this.saving = false;
        });
        return;
      }
      this.activeModal.hide();
      this.translate.get('迁移成功').subscribe((res: string) => {
        this.log.success(res);
        // 跳转编辑页面
        sessionStorage.setItem('vehicleManagement', JSON.stringify(this.event.query.query));
        this.router.navigateByUrl('/vehicleManagement/' + this.id + '/edit');
      });
    });
  }

  buildForm() {
    this.form = this.formBuilder.group({
      id: [this.id, [Validators.required]],
      orgId: [null, [Validators.required]]
    });
  }
}
