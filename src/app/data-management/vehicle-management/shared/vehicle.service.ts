import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { WebApiResultResponse, PagingResponse } from '@core/http/web-api-result-response';
import { QueryPageList } from '@app/shared/models/type';
import { Order } from './models';

@Injectable({
  providedIn: 'root'
})
export class VehicleService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');
  constructor(private http: HttpClient) {
    super();
  }

  getVehicleList(params: QueryPageList): Observable<PagingResponse> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/pageVehicle/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 该车未绑定设备信息列表
  getEquipment(params: any) {
    const url = `glcrm-vehicle-api/v1/api/vehicle/devicesUnbind/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 设备分页
  getDevicePage(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/unbindCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 该车已绑定设备信息列表
  BindEquipment(params: any) {
    const url = `glcrm-vehicle-api/v1/api/vehicle/devicesBind/${params.pageIndex}/${params.pageSize}/${params.vehicleId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 已绑定设备分页
  getBindDevicePage(params: any) {
    const url = `glcrm-vehicle-api/v1/api/vehicle/bindCount/${params.vehicleId}`;
    return this.http.get<any>(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 该车未绑定驾驶员信息列表
  getDriverList(params: any) {
    // glcrm-vehicle-api/v1/api/vehicle/user/users/vehicleNot
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/vehicleNot/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 未绑定驾驶员列表分页
  getDriverPage(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/vehicleNot/${params.vehicleId}/${params.orgId}`;
    return this.http.get<any>(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 该车已绑定驾驶员信息列表
  //  { id :1}
  getBindDriver(params: any): Observable<Order> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/vehicle/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 已绑定驾驶员信息列表分页
  getBindDriverPage(params: any) {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/vehicle/${params.vehicleId}/${params.orgId}`;
    return this.http.get<any>(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 添加驾驶人接口
  addDrivers(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/bindUserAndVehicleInfo';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 移除驾驶人接口
  removeDrivers(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/unBindUserAndVehicleInfo';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
    // .pipe(map(super.handleSuccess)),catchError(super.handleError);
  }
  //  移入设备接口
  addDevice(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/bindVehicle';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 移除设备接口
  removeDevice(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/unBindVehicle';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取分页接口
  getPageNumber(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/vehicleCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 编辑车辆接口
  updateVehicle(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  getDriverDetial(id: number): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/${id}`;
    return this.http.get<any>(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  add(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  updateOrder(entity: Order): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/${entity.id}`;
    return this.http.put(url, entity).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  deleteOne(entity: Order): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/${entity.id}`;
    return this.http.delete(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  exportReport(entity: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = `glcrm-vehicle-api/v1/api/vehicle/exportVehicle`;
    return this.http
      .post(url, entity, {
        headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
        responseType: 'blob',
        observe: 'response'
      })
      .pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备型号
  getDeviceType() {
    const url = 'glcrm-vehicle-api/v1/api/device/sysDeviceTypes';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 新建驾驶员
  creatDriverInformation(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/users';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取多个设备定位
  batchPosition(id: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/device?imei=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 标签
  getLabelList(): Observable<any> {
    const url = 'glcrm-account-api/v1/api/label/labelList';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 新增标签列表
  addLabelList(labelName: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/addLabel?labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 上传图片
  upImg(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/uploadImagesToOSS';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 添加安装信息
  savaInstallInfo(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/updateDeviceInfo';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 查询设备安装信息
  getInstallInfo(deviceId: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/getDeviceInfo/' + `${deviceId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 组织迁移
  groupTransfer(params: any) {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/transfer';
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
