/** 车辆状态 默认：Offline */
export enum VehicleStatus {
  'Offline',
  'Moving',
  'Stop',
  'Fco'
}

export const vehicleStatusInfo = {
  [VehicleStatus.Offline]: {
    name: '无信号',
    value: VehicleStatus.Offline,
    label: 'offline',
    msg: '当前车辆状态为：无信号，无法实时轨迹追踪',
    realTimeMsg: '实时追踪已结束，监控对象无信号',
    icon: 'nosignal',
    legendIcon: '/assets/media/app/img/device/offline.png', //'/assets/font/carType_red.png',
    newIcon: '/assets/media/app/img/device/offline',
    count: 0,
    checked: true
  },
  [VehicleStatus.Moving]: {
    name: '移动',
    value: VehicleStatus.Moving,
    label: 'moving',
    msg: '',
    realTimeMsg: '',
    icon: 'moving',
    legendIcon: '/assets/media/app/img/device/moving.png', // '/assets/font/carType_green.png',
    newIcon: '/assets/media/app/img/device/moving',
    count: 0,
    checked: true
  },
  [VehicleStatus.Stop]: {
    name: '停车',
    value: VehicleStatus.Stop,
    label: 'stop',
    msg: '当前车辆状态为：停车，无法实时轨迹追踪',
    realTimeMsg: '实时追踪已结束，监控对象已停车',
    icon: 'static',
    legendIcon: '/assets/media/app/img/device/stop.png', //'/assets/font/carType_blue.png',
    newIcon: '/assets/media/app/img/device/stop',
    count: 0,
    checked: true
  },
  [VehicleStatus.Fco]: {
    name: '断油断电',
    value: VehicleStatus.Fco,
    label: 'fco',
    msg: '当前车辆状态为：断油断电，无法实时轨迹追踪',
    realTimeMsg: '实时追踪已结束，监控对象断油断电',
    icon: 'blocked',
    legendIcon: '/assets/media/app/img/device/fco.png', //'/assets/font/carType_block.png',
    newIcon: '/assets/media/app/img/device/fco',
    count: 0,
    checked: true
  }
};

export const vehicleIconSize = {
  // default: 0.3,
  'default-map': { size: 0.4, offset: [-25, -30] },
  cat: { size: 0.7, offset: [-25, -30] },
  dog: { size: 0.7, offset: [-25, -20] },
  electromobile: { size: 0.7, offset: [-25, -30] },
  human: { size: 0.7, offset: [-30, -30] },
  motorbike: { size: 0.7, offset: [-25, -20] },
  taxi: { size: 0.5, offset: [-25, -20] },
  truck: { size: 0.5, offset: [-25, -30] },
  vehicle: { size: 0.5, offset: [-25, -30] }
};

/** 车辆定位方式 默认：未知 */
export enum VehicleLocation {
  GPS,
  '基站',
  WIFI,
  '北斗',
  'GPS/北斗混合',
  '未知'
}

/**
 * 获取车辆自定义icon图标
 * @param name 自定义icon名称
 * @param status 车辆状态，不同状态icon颜色不一样
 * @returns
 */
export function getVehicleCustomIcon(name: string | null, status: VehicleStatus = VehicleStatus.Moving): string {
  name = name ? name : 'default';
  return `/assets/media/app/img/map/brand/${name}/${vehicleStatusInfo[status].icon}.png`;
}
