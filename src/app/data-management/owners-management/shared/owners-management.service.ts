import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PagingResponse, WebApiResultResponse } from '@app/core';
import { QueryPageList } from '@app/shared/models/type';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class OwnersManagementService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');
  constructor(private http: HttpClient) {
    super();
  }

  getOwners(params: QueryPageList): Observable<PagingResponse> {
    const url = `glcrm-account-api/v1/api/ownerRecord/page`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // f分页
  getPageNumber(params: any): Observable<any> {
    const url = 'glcrm-account-api/v1/api/ownerRecord/page/count';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  getOwnerDetail(id: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/ownerRecord/info`;
    return this.http.get<any>(url, { params: { id } });
  }

  createOwner(params: any): Observable<any> {
    const url = 'glcrm-account-api/v1/api/ownerRecord';
    return this.http.post(url, params);
  }

  updateOwner(params: any): Observable<any> {
    const url = 'glcrm-account-api/v1/api/ownerRecord';
    return this.http.put(url, params);
  }

  deleteOwner(id: string): Observable<any> {
    const url = `glcrm-account-api/v1/api/ownerRecord`;
    return this.http.delete(url, { params: { id } });
  }

  getSelectOwnerList(params: any): Observable<any> {
    const url = 'glcrm-account-api/v1/api/ownerRecord/page/term';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  getSelectOwnerListCount(params: any): Observable<any> {
    const url = 'glcrm-account-api/v1/api/ownerRecord/page/term/count';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 导出
  exportReport(param: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = `glcrm-account-api/v1/api/vehicle/user/export`;
    const options: Object = {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response',
      params: param
    };
    return this.http.get(url, options).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
