import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { finalize } from 'rxjs/operators';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { OwnersManagementService } from '../../shared/owners-management.service';

@Component({
  selector: 'app-owners-create',
  templateUrl: './owners-create.component.html',
  styleUrls: ['./owners-create.component.scss']
})
export class OwnersCreateComponent implements OnInit {
  @Output() action = new EventEmitter();
  public log: Logger;
  saving = false;
  title = '新建';

  constructor(
    public activeModal: BsModalRef,
    private ownersService: OwnersManagementService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService
  ) {
    this.translate.get('新建').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {}

  save(params: any) {
    this.saving = true;
    this.ownersService
      .createOwner(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.log.error(this.translate.instant(res.message || '新增失败'));
            return;
          }
          this.action.emit(true);
          this.activeModal.hide();
        },
        () => this.log.error(this.translate.instant('新增失败'))
      );
  }
}
