import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { TranslateService } from '@ngx-translate/core';
import { OwnersManagementService } from '../../shared/owners-management.service';

@Component({
  selector: 'app-owners-edit',
  templateUrl: './owners-edit.component.html',
  styleUrls: ['./owners-edit.component.scss']
})
export class OwnersEditComponent implements OnInit {
  @Input() data: number;
  @Output() action = new EventEmitter();
  log: Logger;
  saving = false;
  title = '编辑';
  owners: any;

  constructor(
    public activeModal: BsModalRef,
    private ownersService: OwnersManagementService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService
  ) {
    this.translate.get('编辑').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {
    this.getOwners();
  }

  getOwners() {
    this.ownersService.getOwnerDetail(this.data).subscribe((res) => {
      this.owners = res.data;
    });
  }

  save(params: any) {
    this.saving = true;
    this.ownersService
      .updateOwner(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.log.error(this.translate.instant(res.message || '保存失败'));
            return;
          }
          this.action.emit(true);
          this.activeModal.hide();
        },
        () => this.log.error(this.translate.instant('保存失败'))
      );
  }
}
