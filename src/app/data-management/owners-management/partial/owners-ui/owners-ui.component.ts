import { Component, OnInit, Input, EventEmitter, Output, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { regex } from '@app/shared/utils/regex';
import { OwnersManagementService } from '../../shared/owners-management.service';
import { CustomValidators } from 'ngx-custom-validators';

@Component({
  selector: 'app-owners-ui',
  templateUrl: './owners-ui.component.html',
  styleUrls: ['./owners-ui.component.scss']
})
export class OwnersUiComponent implements OnInit {
  @Input() title: string = '新建';
  @Input() data: any;
  // 有orgId表明从编辑车辆新增司机调用
  @Input()
  get orgId(): string {
    return this._orgId;
  }
  set orgId(orgId: string) {
    if (orgId && this.form) {
      this.form.patchValue({ orgId });
    }
    this._orgId = orgId;
  }
  @Output() save = new EventEmitter<any>();

  log: Logger;

  form: FormGroup;
  saving = false;

  listOfOption: any[];
  nodes: Array<any> = []; // 组织机构

  _orgId: string = '';

  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private ownersService: OwnersManagementService,
    public modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private cd: ChangeDetectorRef
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger(this.translate.instant('新建'));
  }

  ngOnChanges(changes: SimpleChanges): void {
    const edit = changes.data ? changes.data.currentValue : null;
    if (this.title === '编辑' && edit) {
      this.form.patchValue({
        orgId: this.data.orgId,
        carOwnerName: this.data.carOwnerName,
        contact: this.data.contact,
        email: this.data.email,
        cityName: this.data.cityName
      });
    }
  }

  ngOnInit() {
    this.getUserAllOrganizations();
  }

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    const params = this.form.getRawValue();
    params.id = this.data ? this.data.id : null;
    if (!this.orgId) {
      this.save.emit(params);
      return;
    }
    if (this.saving) {
      return;
    }
    this.saving = true;
    this.ownersService
      .createOwner(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.log.error(this.translate.instant(res.message || '新增失败'));
            return;
          }
          this.activeModal.hide();
        },
        () => this.log.error(this.translate.instant('新增失败'))
      );
  }

  buildForm() {
    this.form = this.formBuilder.group({
      orgId: [null, [Validators.required]],
      carOwnerName: [null, [Validators.required, Validators.pattern(regex.bookString)]],
      contact: [null, []],
      email: [
        null,
        [
          Validators.required,
          CustomValidators.email,
          CustomValidators.rangeLength([6, 50]),
          Validators.pattern(regex.space)
        ]
      ],
      cityName: [null, []]
    });
  }
  // 获取机构
  getUserAllOrganizations() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.referenceDataService
      .getUserChildrenOrganizations(id)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((organNodes) => (this.nodes = organNodes));
  }
}
