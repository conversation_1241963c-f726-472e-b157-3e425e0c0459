import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { OwnersManagementService } from '../../shared/owners-management.service';

@Component({
  selector: 'app-owners-detail',
  templateUrl: './owners-detail.component.html',
  styleUrls: ['./owners-detail.component.scss']
})
export class OwnersDetailComponent implements OnInit {
  @Input() data: any;

  title: string = '车主详情';
  owners: any;
  constructor(public activeModal: BsModalRef, private ownersService: OwnersManagementService) {}

  ngOnInit() {
    this.getOwners();
  }

  getOwners() {
    this.ownersService.getOwnerDetail(this.data.id).subscribe((res) => {
      this.owners = res.data;
    });
  }
}
