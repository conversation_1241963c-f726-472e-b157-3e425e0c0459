<div class="modal-header">
  <h5 class="modal-title" translate>{{ title }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="m-portlet">
    <div class="m-portlet__head">
      <div class="m-portlet__head-tools">
        <div class="toolbar">
          <ngx-query
            [hidden]="true"
            [datePickerReadonly]="false"
            [columnNumber]="1"
            class="full-screen no-header"
            #dataNgxQuery
            [queryTemplates]="dataQueryTemplates"
            [isRetainQueryMode]="true"
            [showModeButtons]="true"
            [mode]="mode"
            [showPlainCollapseToolBar]="false"
          >
            <ngx-query-field [name]="'term'" [label]="''" [type]="'string'">
              <ng-template
                ngx-query-value-input-template
                dataType="text"
                let-rules="rules"
                let-rule="rule"
                let-dataIndex="dataIndex"
              >
                <button class="querybtn btn">
                  <i class="la la-search"></i>
                </button>
                <input
                  type="text"
                  [(ngModel)]="rule.datas[dataIndex]"
                  class="form-control form-control-sm query_input"
                  placeholder="{{ '驾驶员姓名/联系电话/所属公司' | translate }}"
                  (keyup.enter)="screenQuery()"
                />
              </ng-template>
            </ngx-query-field>
          </ngx-query>
          <div class="search-top">
            <button nz-button nzType="primary" (click)="create()">
              <i nz-icon nzType="plus" nzTheme="outline"></i>
              {{ '新建' | translate }}
            </button>
            <div class="query-int">
              <button class="querybtn" (click)="screenQuery()">
                <img src="/assets/font/search-logo.svg" alt="" />
              </button>
              <button class="clearbtn" (click)="dataReflash()" *ngIf="name">×</button>
              <input
                [(ngModel)]="name"
                type="text"
                class="form-control form-control-sm query_input"
                placeholder="{{ '请输入车主姓名/联系电话/邮箱' | translate }} / {{ '所在城市' | translate }}"
                (keyup.enter)="screenQuery()"
              />
            </div>
            <button
              type="button"
              nz-button
              (click)="dataReflash()"
              nz-tooltip
              nzTooltipPlacement="top"
              nzTooltipTitle="{{ '刷新' | translate }}"
            >
              <i class="la la-refresh"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="m-portlet__body p-0">
      <ngx-datatable
        class="material"
        [scrollbarH]="true"
        [ngxQuery]="dataNgxQuery"
        appNgxDataTable
        [rows]="list"
        [saveState]="false"
        [loadingIndicator]="loading"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="dataFooter"
        [count]="dataCurrentNumber"
        (loadValue)="getList($event)"
        [columnMode]="'force'"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        [selectionType]="'single'"
        (select)="selectChange($event)"
      >
        <ngx-datatable-column
          [width]="40"
          [sortable]="false"
          [canAutoResize]="false"
          [draggable]="false"
          [resizeable]="true"
          [headerCheckboxable]="false"
          [checkboxable]="false"
        ></ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '车主姓名' | translate }}"
          prop="carOwnerName"
          [width]="40"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.carOwnerName | isNull }}">{{ row.carOwnerName | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '联系电话' | translate }}"
          prop="contact"
          [width]="40"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.contact | isNull }}">{{ row.contact | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '邮箱' | translate }}"
          prop="email"
          [width]="60"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '所在城市' | translate }}"
          prop="cityName"
          [width]="60"
          headerClass="text-left"
          cellClass="text-left"
        ></ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #dataFooter
          [currentNumber]="dataCurrentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getListTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
