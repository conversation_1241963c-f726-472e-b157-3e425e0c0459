.search-top {
  display: flex;
  align-items: center;

  .query-int {
    margin: 0 10px;
    flex: 1;
    position: relative;
    background: #f1f3f8;

    .query_input {
      border-radius: 5px;
      border-color: #f1f3f8;
      background: #f1f3f8;
      width: 100%;
    }

    .clearbtn {
      position: absolute;
      right: 43px;
      font-size: 20px;
      width: 41px;
      height: 38px;
      border: none;
      border-radius: 5px;
      outline: none;
      cursor: pointer;
      background: #f1f3f8;
    }

    .querybtn {
      position: absolute;
      right: 1px;
      border: none;
      border-radius: 5px;
      width: 41px;
      height: 36px;
      margin-top: 1px;
      outline: none;
      cursor: pointer;
      background: #f1f3f8;

      .la {
        margin-top: 3px;
      }
    }
  }

  .btn-new {
    width: 96px;

    span {
      padding-left: 0;
    }
  }
}
:host ::ng-deep .dropmenu {
  display: none !important;
}

.footer-style {
  display: flex;
  /* flex-direction: column; */
  height: 50px;
  position: static !important;
  /* width: 15%; */
  /* margin-left: 80%; */
  /* right: 0; */
  justify-content: flex-end;
}

:host ::ng-deep .col-6 {
  position: static;
}
