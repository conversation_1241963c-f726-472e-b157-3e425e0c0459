import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { OwnersManagementService } from '@app/data-management/owners-management/shared/owners-management.service';
import { NgxQueryComponent, QueryMode, QueryTemplate } from '@zhongruigroup/ngx-query';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { finalize } from 'rxjs/operators';
import { OwnersUiComponent } from '../../owners-ui/owners-ui.component';

@Component({
  selector: 'app-owners-select',
  templateUrl: './owners-select.component.html',
  styleUrls: ['./owners-select.component.scss']
})
export class OwnersSelectComponent implements OnInit {
  @Input() organId: string;
  @Output() action = new EventEmitter();
  log: Logger;
  list: any[] = [];
  name: string = '';
  title: string = '选择车主';
  selected: any;

  loading = false;
  dataCurrentNumber = 10;
  totalNumber: number;
  datalist: Array<any> = [];
  dataEvent: any = {};
  page = {
    pageIndex: 1,
    pageSize: 10
  };

  mode: QueryMode = QueryMode.plainCollapse;
  dataQueryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'term', op: 'cn' }],
        groups: []
      }
    }
  ];

  @ViewChild('dataNgxQuery') dataNgxQuery: NgxQueryComponent;
  @ViewChild('dataFooter') dataFooter: NoPageDatatableFooterComponent;

  constructor(
    private cd: ChangeDetectorRef,
    public activeModal: BsModalRef,
    private ownersService: OwnersManagementService,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory
  ) {
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit(): void {}
  submit() {
    const out = this.selected && this.selected.selected.length ? this.selected.selected[0] : {};
    this.action.emit(out);
    this.activeModal.hide();
  }
  // 新建
  create() {
    const initialState = {
      orgId: this.organId
    };
    this.modalService.show(OwnersUiComponent, {
      ignoreBackdropClick: true,
      initialState,
      class: 'modal-lg-custom '
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.dataNgxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }
  selectChange(row: any) {
    this.selected = row;
  }
  screenQuery() {
    this.dataEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.getList(this.dataEvent);
  } // 刷新
  dataReflash() {
    this.dataEvent.page = this.page; // 重置查询，主要目的为将页码重置
    this.name = '';
    this.getList(this.dataEvent);
  }
  // 列表总数
  getListTotal() {
    const params = { pageIndex: 0, pageSize: 0, orgId: this.organId, term: this.name };
    this.ownersService.getSelectOwnerListCount(params).subscribe(
      (res: any) => {
        if (res.code === '200') {
          this.totalNumber = res.data;
        } else {
          console.log('列表总数获取失败。');
        }
      },
      () => console.log('列表总数获取失败。')
    );
  }
  // 未绑定翻页
  turnPage() {
    return this.dataNgxQuery.validateQuery();
  }

  getList(event: any) {
    if (!this.organId) {
      return;
    }
    this.loading = true;
    this.dataEvent = event;
    this.dataFooter.showTotalElements = true;
    this.ownersService
      .getSelectOwnerList({ term: this.name.trim(), orgId: this.organId, ...event.page })
      .pipe(
        finalize(() => {
          this.loading = false;
          this.cd.markForCheck();
        })
      )
      .subscribe(
        (res) => {
          this.list = res.data || [];
          this.dataCurrentNumber = this.list.length;
        },
        () => (this.list = [])
      );
  }
}
