import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { Logger } from '@core/logger.service';
import { Dialogs } from '@core/dialogs.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate } from '@app/shared/models/type';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { OwnersManagementService } from '../../shared/owners-management.service';
import { OwnersEditComponent } from '../owners-edit/owners-edit.component';
import { OwnersCreateComponent } from '../owners-create/owners-create.component';
import { OwnersDetailComponent } from '../owners-detail/owners-detail.component';

@Component({
  selector: 'app-owners-index',
  templateUrl: './owners-index.component.html',
  styleUrls: ['./owners-index.component.scss']
})
export class OwnersIndexComponent implements OnInit {
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;
  ownerList: Array<any>;
  event: any;
  currentNumber = 10;
  totalNumber: number;
  datatable: any;
  nodes: any[] = [];

  previousPage = 0; // 上一页
  nextPage = 0; // 下一页
  originTemplate: any;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'carOwnerName', op: 'eq' },
          { field: 'contact', op: 'eq' },
          { field: 'email', op: 'eq' },
          { field: 'cityName', op: 'eq' }
        ],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  constructor(
    private dialogs: Dialogs,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef,
    private ownerService: OwnersManagementService,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService
  ) {
    this.translate.get('车主').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }

  ngOnInit() {
    this.getUserAllOrganizations();
  }

  ngAfterViewInit(): void {
    this.changeDetectorRef.detectChanges();
  }

  // 获取机构
  getUserAllOrganizations() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => (this.nodes = organNodes));
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }
  refreshData() {
    this.loadOwners(this.event);
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      map[key] = rule.data;
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  loadOwners(event: any) {
    this.event = event;
    const page = event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.footer.showTotalElements = true;
    this.ownerService
      .getOwners(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          this.ownerList = response.data || [];

          this.currentNumber = this.ownerList ? this.ownerList.length : 0;
        },
        (error) => console.log('车主管理列表获取失败', this.translate.instant(error))
      );
  }

  delete(row: any) {
    this.translate.get(`真的要删除吗？`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.ownerService.deleteOwner(row.id).subscribe(
            (data) => {
              if (data.success) {
                this.translate.get(`删除成功`).subscribe((res) => {
                  this.log.success(res);
                });
              } else {
                this.translate.get(data.message).subscribe((res) => {
                  this.log.error(res);
                });
              }
              this.ngxQuery.executeQuery();
            },
            (error) => console.log(`列表 ${row.carOwnerName} 删除失败，失败信息：`, this.translate.instant(error))
          );
        },
        () => console.log(`取消删除列表 ${row.carOwnerName}`)
      );
    });
  }

  // 编辑
  editByModal(row: any) {
    const initialState = { data: row.id };
    const bsModalRef: BsModalRef = this.modalService.show(OwnersEditComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom'
    });
    bsModalRef.content.action.subscribe((res: any) => {
      if (res) {
        this.ngxQuery.executeQuery();
      }
    });
    const onHidden = this.modalService.onHidden.subscribe((val: any) => {
      onHidden.unsubscribe();
    });
  }

  // 查看详情
  checkDetail(row: any) {
    const initialState = { data: row };
    this.modalService.show(OwnersDetailComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom'
    });
  }

  // 新建
  createByModal() {
    const bsModalRef: BsModalRef = this.modalService.show(OwnersCreateComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom'
    });
    bsModalRef.content.action.subscribe((res: any) => {
      if (res) {
        this.ngxQuery.executeQuery();
      }
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      onHidden.unsubscribe();
    });
  }

  getTotal() {
    const page = this.event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.ownerService
      .getPageNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.success) {
            this.totalNumber = response.data;
          } else {
            console.log('车主列表总数获取失败。', response.msg);
          }
        },
        (error) => console.log('车主列表总数获取失败。', this.translate.instant(error))
      );
  }

  //   exportReport() {
  //     const curDate: any = new Date();
  //     const param = this.loadProperty(this.event.page);
  //     const params = {
  //       addr: param.addr,
  //       endTime: param.endTime,
  //       startTime: param.startTime,
  //       carOwnerName: param.carOwnerName,
  //       email: param.orgName,
  //       contact: param.contact,
  //       ownerLicense: param.ownerLicense,
  //       language: window.localStorage.getItem('lang') === 'zh-CN' ? 'cn' : 'en'
  //     };
  //     this.ownerService
  //       .exportReport(params)
  //       .pipe(finalize(() => (this.loading = false)))
  //       .subscribe(
  //         (response) => {
  //           const link = document.createElement('a');
  //           const blob = new Blob([response.body], { type: 'application/xlsx' }),
  //             doloadUrl = window.URL.createObjectURL(blob);
  //           link.setAttribute('href', doloadUrl);
  //           this.translate.get('车主信息').subscribe((res: string) => {
  //             link.setAttribute(
  //               'download',
  //               res +
  //                 curDate.getFullYear() +
  //                 (curDate.getMonth() + 1) +
  //                 curDate.getDate() +
  //                 curDate.getHours() +
  //                 curDate.getMinutes() +
  //                 curDate.getSeconds() +
  //                 '.xlsx'
  //             );
  //           });
  //           // tslint:disable-next-line: max-line-length
  //           // link.setAttribute('download', '车主信息列表' + curDate.getFullYear() + (curDate.getMonth() + 1) + curDate.getDate() + curDate.getHours() + curDate.getMinutes() + curDate.getSeconds() + '.xlsx');
  //           link.style.visibility = 'hidden';
  //           document.body.appendChild(link);
  //           link.click();
  //           document.body.removeChild(link);
  //         },
  //         (error) => {
  //           this.translate.get('导出失败:').subscribe((res1: string) => {
  //             this.translate.get('导出数量超过2000条').subscribe((res2: string) => {
  //               if (error === 'OK') {
  //                 // 此时说明是因为超过2000条失败
  //                 this.log.error(res1, res2);
  //               } else {
  //                 this.log.error(res1, error);
  //               }
  //             });
  //           });
  //         }
  //       );
  //   }
}
