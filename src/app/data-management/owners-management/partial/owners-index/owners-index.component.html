<div class="m-portlet list_header">
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'carOwnerName'" label="{{ '车主姓名' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入车主姓名' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'contact'" label="{{ '联系电话' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入联系电话' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'email'" label="{{ '邮箱' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入邮箱' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'cityName'" label="{{ '所在城市' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入所在城市' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<!--  -->
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-tools">
      <ul class="m-portlet__nav">
        <li class="m-portlet__nav-item" [appApplyPermission]="'owner_add'">
          <button nz-button (click)="createByModal()">
            {{ '新建' | translate }}
          </button>
        </li>
        <!-- <li class="m-portlet__nav-item" [appApplyPermission]="'owner_export'">
          <button nz-button (click)="exportReport()">
            {{ '导出' | translate }}
          </button>
        </li> -->
      </ul>
    </div>
  </div>

  <div class="m-portlet__body p-0">
    <ngx-datatable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="ownerList"
      appNgxDataTable
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="loadOwners($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
      [columnMode]="'force'"
    >
      <ngx-datatable-column
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        [width]="300"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.orgName }}">{{ row.orgName }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [width]="300" name="{{ '车主姓名' | translate }}" prop="carOwnerName">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.carOwnerName | isNull }}">{{ row.carOwnerName | isNull }}</div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        name="{{ '联系电话' | translate }}"
        [width]="300"
        prop="contact"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.contact | isNull }}">{{ row.contact | isNull }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '邮箱' | translate }}"
        prop="email"
        headerClass="text-left"
        cellClass="text-left"
        [width]="400"
      ></ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '所在城市' | translate }}"
        prop="cityName"
        headerClass="text-left"
        cellClass="text-left"
        [width]="400"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.cityName | isNull }}">{{ row.cityName | isNull }}</div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [frozenRight]="true"
        maxWidth="180"
        name="{{ '操作' | translate }}"
        prop="total"
        headerClass="text-center"
        cellClass="text-center"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span appApplyPermission="owner_detail" class="margin_right" (click)="checkDetail(row)">
            <a>
              <img
                src="/assets/font/detail.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '详情' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="owner_detail" class="margin_right">|</span>

          <span appApplyPermission="owner_edit" class="margin_right" (click)="editByModal(row)">
            <a>
              <img
                src="/assets/font/edit.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '编辑' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="owner_edit" class="margin_right">|</span>

          <span class="margin_right" [appApplyPermission]="'owner_delete'" (click)="delete(row)">
            <a>
              <img
                src="/assets/font/delete.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '删除' | translate }}"
              />
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [frozenRight]="true" maxWidth="50" headerClass="datatable-header-cell-acitons text-left">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
