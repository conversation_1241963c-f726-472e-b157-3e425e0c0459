import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OwnersManagementRoutingModule } from './owners-management-routing.module';
import { OwnersIndexComponent } from './partial/owners-index/owners-index.component';
import { OwnersDetailComponent } from './partial/owners-detail/owners-detail.component';
import { OwnersUiComponent } from './partial/owners-ui/owners-ui.component';
import { OwnersCreateComponent } from './partial/owners-create/owners-create.component';
import { OwnersEditComponent } from './partial/owners-edit/owners-edit.component';
import { OwnersSelectComponent } from './partial/components/owners-select/owners-select.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [
    OwnersIndexComponent,
    OwnersDetailComponent,
    OwnersUiComponent,
    OwnersCreateComponent,
    OwnersEditComponent,
    OwnersSelectComponent
  ],
  imports: [
    CommonModule,
    OwnersManagementRoutingModule,
    FormsModule,
    SharedModule,
    TranslateModule,
    CommonModule,
    ReactiveFormsModule
  ],
  exports: [
    OwnersIndexComponent,
    OwnersDetailComponent,
    OwnersUiComponent,
    OwnersCreateComponent,
    OwnersEditComponent,
    OwnersSelectComponent
  ]
})
export class OwnersManagementModule {}
