import { Component, OnInit, Input } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { DriverService } from '../../shared/driver.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-driver-edit',
  templateUrl: './driver-edit.component.html',
  styleUrls: ['./driver-edit.component.scss']
})
export class DriverEditComponent implements OnInit {
  @Input() data: number;
  log: Logger;
  saving = false;
  title = '编辑';
  driver: any;

  constructor(
    public activeModal: BsModalRef,
    private driverService: DriverService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService
  ) {
    this.translate.get('编辑').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {
    this.getDriver();
  }

  getDriver() {
    this.driverService.getDriverDetail(this.data).subscribe((res) => {
      this.driver = res.data;
    });
  }

  save(params: any) {
    this.saving = true;
    this.driverService
      .updateDriver(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((res) => {
        if (res.success) {
          this.modalService.onHidden.emit(params);
          this.activeModal.hide();
        } else {
          this.translate.get(res.message).subscribe((res: string) => {
            this.log.error(res);
          });
        }
      });
  }
}
