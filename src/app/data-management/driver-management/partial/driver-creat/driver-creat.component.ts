import { Component, OnInit } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { finalize } from 'rxjs/operators';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DriverService } from '../../shared/driver.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-driver-creat',
  templateUrl: './driver-creat.component.html',
  styleUrls: ['./driver-creat.component.scss']
})
export class DriverCreatComponent implements OnInit {
  public log: Logger;
  saving = false;
  title = '新建';

  constructor(
    public activeModal: BsModalRef,
    private driverService: DriverService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService
  ) {
    this.translate.get('新建').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {}

  save(params: any) {
    this.saving = true;
    this.driverService
      .creatDriverInformation(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (response) => {
          if (response.code === '200') {
            this.activeModal.hide();
          } else {
            this.translate.get(response.message).subscribe((res: string) => {
              this.log.error(res);
            });
          }
        },
        (error) => console.log('信息保存失败。', error)
      );
  }
}
