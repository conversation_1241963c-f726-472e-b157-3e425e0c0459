.detail-container {
  height: 100%;
  position: relative;
}

.detail-card-container {
  width: 420px;
  position: absolute;
  top: 0;
  bottom: 16px;
  left: 0;
}

.device-header,
.driver-header {
  display: grid;
  grid-template-columns: 60px 1fr 70px 28px;
  align-items: center;
  border-radius: 4px 4px 0px 0px;

  &-title {
    text-align: center;
    // padding-left: 20px;
  }
}

.driver-header {
  grid-template-columns: 25% 65% 10%;
}

.no-info {
  text-align: center;
  padding: 16px 0;
}
