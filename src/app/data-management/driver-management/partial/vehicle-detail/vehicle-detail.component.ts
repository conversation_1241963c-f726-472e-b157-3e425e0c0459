import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable, forkJoin, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { Theme } from '@app/shared/models/theme';

import { vehicleStatusInfo, VehicleStatus } from '@app/data-management/vehicle-management/shared/models/vehicle';
import {
  SensorConfig,
  sensorTypeInfo,
  DeviceStatus,
  deviceStatusInfo
} from '@app/data-management/device-management/shared/models/device';
import { VehicleService } from '@app/data-management/vehicle-management/shared/vehicle.service';
import { DeviceService } from '@app/data-management/device-management/shared/device.service';

@Component({
  selector: 'app-vehicle-detail',
  templateUrl: './vehicle-detail.component.html',
  styleUrls: ['./vehicle-detail.component.scss']
})
export class VehicleDetailComponent implements OnInit {
  @Input()
  get vehicleId(): number {
    return this._vehicleId;
  }
  set vehicleId(val: number) {
    this._vehicleId = val;
    if (!val) {
      return;
    }
    this.getDetail();
  }
  /** 判断从哪里进入车辆详情页 true:从车辆列表点详情进来, false:从司机相机点绑定车辆弹窗进来 */
  @Input() isFromVehicle = true;

  log: Logger;
  accountSetting = accountSetting;
  Theme = Theme;

  vehicleStatusInfo = vehicleStatusInfo;
  VehicleStatus = VehicleStatus;

  sensorTypeInfo = sensorTypeInfo;
  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  vehicle: any = {};
  vehiclePosition: any[]; // 定位信息

  deviceList: any[] = []; // 绑定设备
  driverList: any[] = []; // 绑定驾驶员

  private _vehicleId: number;

  constructor(
    private deviceService: DeviceService,
    private route: ActivatedRoute,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private vehicleService: VehicleService
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant('车辆详情'));
  }

  ngOnInit() {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.vehicleId = Number(id);
      }
    });
  }

  private getDetail() {
    this.vehicleService.getDriverDetial(this.vehicleId).subscribe((res) => {
      if (!res.success || !res.data) {
        return;
      }
      this.vehicle = res.data;

      this.driverList = res.data?.userList || [];
      this.driverList = this.driverList.map((driver) => {
        driver.open = false;
        return driver;
      });

      this.deviceList = res.data?.deviceVoList || [];
      if (this.deviceList.length < 1) {
        return;
      }
      this.deviceList = this.deviceList
        .map((device) => {
          device.open = false;
          device.timeStamp = device.locationTime ? new Date(device.locationTime).getTime() : 0;
          return device;
        })
        .sort((a, b) => b.timeStamp - a.timeStamp);
      this.getDevicePosition(this.deviceList[0].deviceNo);
      const deviceList$ = this.deviceList.map((device: any) =>
        device?.isSensor === '1' ? this.getDeviceSensorInfo(device.deviceNo) : of([])
      );
      forkJoin(deviceList$).subscribe((sensorList) => {
        sensorList.forEach((sensorConfig: SensorConfig[], i: number) => {
          this.deviceList[i].sensorConfig = sensorConfig;
        });
      });
    });
  }

  // 获取设备定位
  getDevicePosition(deviceNo: string) {
    this.vehicleService.batchPosition(deviceNo).subscribe((res) => {
      if (!res.success) {
        return;
      }
      this.vehiclePosition = res.data;
    });
  }

  /**
   * 获取设备传感器信息
   */
  getDeviceSensorInfo(deviceNo: string): Observable<SensorConfig[]> {
    return this.deviceService.getSensorInfo(deviceNo).pipe(
      map((res: any) => {
        if (!res.success || !res.data) {
          return [];
        }
        let sensorConfig = res.data?.sensorConfig || [];
        sensorConfig = sensorConfig
          .filter((sensor: SensorConfig) => sensorTypeInfo[sensor.type])
          .map((sensor: SensorConfig) => {
            sensor.value = this.deviceService.formatSensorValue(sensor);
            return sensor;
          });
        return this.deviceService.collectSensorValue(sensorConfig);
      }),
      catchError(() => of([]))
    );
  }
}
