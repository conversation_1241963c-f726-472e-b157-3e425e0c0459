<div class="detail-container">
  <app-device-info-map [device]="vehiclePosition"></app-device-info-map>
  <div class="detail-card-container">
    <div class="detail-card">
      <div class="detail-card-header">
        <div class="detail-card-header-title">
          <img src="/assets/font/title-front.png" />
          {{ '车辆详情' | translate }}
        </div>
      </div>

      <div class="detail-card-body">
        <div class="box">
          <div class="box-header">
            <div class="box-title" translate>机构信息</div>
          </div>
          <div class="box-body">
            <div class="detail-row">
              <label translate>所属机构</label>
              <div class="detail-value">
                <span class="">{{ vehicle?.orgName }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="box">
          <div class="box-header">
            <div class="box-title" translate>车辆信息</div>
          </div>
          <div class="box-body">
            <div class="detail-row">
              <label translate>标签</label>
              <div class="detail-value">
                <span *ngIf="!vehicle?.labelList || vehicle?.labelList.length === 0; else vehicleLabelTpl">-</span>
                <ng-template #vehicleLabelTpl>
                  <nz-tag
                    *ngFor="let label of vehicle?.labelList"
                    [nzColor]="accountSetting.theme === Theme.Dark ? '#2f3848' : ''"
                  >
                    {{ label.labelName }}
                  </nz-tag>
                </ng-template>
              </div>
            </div>
            <div class="detail-row">
              <label translate>车牌号</label>
              <div class="detail-value">
                <span>
                  {{ vehicle?.plateNumber }}
                  <img
                    class="img-car"
                    [src]="vehicleStatusInfo[vehicle?.status || VehicleStatus.Offline].legendIcon"
                    nz-tooltip
                    nzTooltipPlacement="top"
                    nzTooltipTitle="{{ vehicleStatusInfo[vehicle?.status || VehicleStatus.Offline].name | translate }}"
                  />
                </span>
              </div>
            </div>
            <div class="detail-row">
              <label translate>车辆型号</label>
              <div class="detail-value">
                {{ vehicle?.modelName }}
              </div>
            </div>
            <div class="detail-row">
              <label translate>里程数</label>
              <div class="detail-value">
                {{ vehicle?.mileage }}
              </div>
            </div>
            <div class="detail-row">
              <label translate>车架号</label>
              <div class="detail-value">
                {{ vehicle?.vin }}
              </div>
            </div>
          </div>
        </div>

        <div class="box">
          <div class="box-header">
            <div class="box-title" translate>车主信息</div>
          </div>
          <div class="box-body">
            <div class="detail-row">
              <label translate>车主名</label>
              <div class="detail-value">
                {{ vehicle?.ownerName }}
              </div>
            </div>
            <div class="detail-row">
              <label translate>联系电话</label>
              <div class="detail-value">
                {{ vehicle?.phoneNumber }}
              </div>
            </div>
            <!-- <div class="detail-row">
              <label translate>邮件</label>
              <div class="detail-value">
                {{ vehicle?.email }}
              </div>
            </div> -->
            <div class="detail-row">
              <label translate>所在城市</label>
              <div class="detail-value">
                {{ vehicle?.city }}
              </div>
            </div>
          </div>
        </div>

        <div class="box">
          <div class="box-header">
            <div class="box-title" translate>绑定设备信息</div>
          </div>

          <div class="box-body">
            <div *ngIf="!deviceList || deviceList?.length === 0; else deviceTpl">
              <div class="no-info" translate>暂无绑定设备</div>
            </div>

            <ng-template #deviceTpl>
              <div class="sub-box" *ngFor="let device of deviceList">
                <div class="sub-box-header device-header">
                  <div class="device-header-title" translate>设备号</div>
                  <div>{{ device.deviceNo }}</div>
                  <div>
                    <span translate>{{ device.wireless === '1' ? '无线' : '有线' }}</span>
                    <nz-badge
                      [nzColor]="deviceStatusInfo[device?.status || DeviceStatus.Invalid].backgroundColor"
                      nz-tooltip
                      nzTooltipPlacement="top"
                      [nzTooltipTitle]="deviceStatusInfo[device?.status || DeviceStatus.Invalid].name | translate"
                    ></nz-badge>
                  </div>
                  <i
                    nz-icon
                    [nzType]="device.open ? 'up' : 'down'"
                    nzTheme="outline"
                    class="pointer"
                    (click)="device.open = !device.open"
                  ></i>
                </div>
                <div *ngIf="device.open" class="sub-box-body">
                  <div class="detail-row">
                    <label translate>标签</label>
                    <div class="detail-value">
                      <span *ngIf="!device?.labelList || device?.labelList.length === 0; else deviceLabelTpl">-</span>
                      <ng-template #deviceLabelTpl>
                        <nz-tag
                          *ngFor="let label of device?.labelList"
                          [nzColor]="accountSetting.theme === Theme.Dark ? '#2f3848' : ''"
                        >
                          {{ label.labelName }}
                        </nz-tag>
                      </ng-template>
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>电量</label>
                    <div class="detail-value">
                      <span *ngIf="device?.wireless === '1'">
                        {{ device.battary === null ? '' : device.battary + '%' }}
                      </span>
                      <span *ngIf="device?.wireless === '0'">-</span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>设备型号</label>
                    <div class="detail-value">
                      {{ device.typeName }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>SIM卡号</label>
                    <div class="detail-value">
                      {{ device.simNo }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>供应商</label>
                    <div class="detail-value">
                      {{ device.producerName }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>安装日期</label>
                    <div class="detail-value">
                      {{ device.installTime | localDate : accountSetting.dateFormat }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>安装人员</label>
                    <div class="detail-value">
                      {{ device.installName }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>联系方式</label>
                    <div class="detail-value">
                      {{ device.phoneNumber }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>安装地址</label>
                    <div class="detail-value">
                      {{ device.installAddress }}
                    </div>
                  </div>
                  <div class="detail-row sensor-row" *ngFor="let sensorConfig of device.sensorConfig">
                    <label>
                      <i
                        class="iot {{ sensorTypeInfo[sensorConfig.type].iconNew }} sensor-icon-page"
                        nz-tooltip
                        nzTooltipTitle="{{ sensorTypeInfo[sensorConfig.type].label | translate }}"
                      ></i>
                    </label>
                    <div class="detail-value">
                      <ng-container *ngIf="sensorConfig.value.length === 0; else sensorTpl">-</ng-container>
                      <ng-template #sensorTpl>
                        <span
                          *ngFor="let sensor of sensorConfig.value"
                          nz-tooltip
                          [nzTooltipTitle]="'ID' + sensor.lineNo"
                        >
                          {{ sensor.value | translate }}
                        </span>
                      </ng-template>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </div>
        </div>

        <div class="box">
          <div class="box-header">
            <div class="box-title" translate>绑定驾驶员信息</div>
          </div>
          <div class="box-body">
            <div *ngIf="!driverList || driverList?.length === 0; else driverTpl">
              <div class="no-info" translate>暂无绑定驾驶员</div>
            </div>
            <ng-template #driverTpl>
              <div class="sub-box" *ngFor="let driver of driverList">
                <div class="sub-box-header driver-header">
                  <div class="driver-header-title" translate>驾驶员</div>
                  <div>
                    {{ driver.userName }}
                  </div>
                  <i
                    nz-icon
                    [nzType]="driver.open ? 'up' : 'down'"
                    nzTheme="outline"
                    class="pointer"
                    (click)="driver.open = !driver.open"
                  ></i>
                </div>

                <div *ngIf="driver.open" class="sub-box-body">
                  <div class="detail-row">
                    <label translate>标签</label>
                    <div class="detail-value">
                      <span *ngIf="!driver?.labelList || !driver?.labelList.length === 0; else driverLabelTpl">-</span>
                      <ng-template #driverLabelTpl>
                        <nz-tag
                          *ngFor="let label of driver?.labelList"
                          [nzColor]="accountSetting.theme === Theme.Dark ? '#2f3848' : ''"
                        >
                          {{ label.labelName }}
                        </nz-tag>
                      </ng-template>
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>联系电话</label>
                    <div class="detail-value">
                      {{ driver.phone }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>所属公司</label>
                    <div class="detail-value">
                      {{ driver.orgName }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>驾照到期</label>
                    <div class="detail-value">
                      {{ driver.licenseEnd | localDate : accountSetting.dateFormat }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>常用地址</label>
                    <div class="detail-value">
                      {{ driver.addr }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>驾驶证号</label>
                    <div class="detail-value">
                      {{ driver.driverLicense }}
                    </div>
                  </div>
                  <div class="detail-row">
                    <label translate>驾照等级</label>
                    <div class="detail-value">
                      {{ driver.driverClass }}
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </div>

      <div class="detail-card-footer" *ngIf="isFromVehicle">
        <button type="button" nz-button nzSize="small" [routerLink]="['/vehicleManagement', vehicleId, 'edit']">
          {{ '编辑' | translate }}
        </button>
        <button type="button" nz-button nzSize="small" nzType="primary" routerLink="/vehicleManagement">
          {{ '返回' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
