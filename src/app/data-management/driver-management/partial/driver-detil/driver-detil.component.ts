import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';

import { BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { DriverService } from '../../shared/driver.service';
import { VehicleDetailComponent } from '../vehicle-detail/vehicle-detail.component';
import { DriverVehicleModalComponent } from '../driver-vehicle-modal/driver-vehicle-modal.component';
@Component({
  selector: 'app-driver-detil',
  templateUrl: './driver-detil.component.html',
  styleUrls: ['./driver-detil.component.scss']
})
export class DriverDetilComponent implements OnInit {
  log: Logger;
  accountSetting = accountSetting;

  driver = {
    orgName: '',
    labels: '',
    userName: '',
    phone: '',
    company: '',
    licenseEnd: '',
    addr: '',
    driverLicense: '',
    driverClass: '',
    drivingAge: '',
    vehicles: ''
  };

  constructor(
    private route: ActivatedRoute,
    private translate: TranslateService,
    private driverService: DriverService,
    private loggerFactory: LoggerFactory,
    private modalService: BsModalService
  ) {
    this.log = this.loggerFactory.getLogger(this.translate.instant('详情'));
  }

  ngOnInit() {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.getDriver(id);
      }
    });
  }

  getDriver(id: number) {
    this.driverService.getDriverDetail(id).subscribe((res) => {
      if (!res?.success) {
        return;
      }
      this.getLabels(res.data);
    });
  }

  // 获取标签
  getLabels(driver: any) {
    this.driverService.getLabelList().subscribe((res) => {
      if (!res?.success) {
        return;
      }
      const listOfOption = res?.data || [];
      driver.labels = [];
      for (let i = 0; i < listOfOption.length; i++) {
        driver.labelIds.map((item: any) => {
          if (item === listOfOption[i].id) {
            driver.labels.push(listOfOption[i].labelName);
            return;
          }
        });
      }
      this.driver = driver;
    });
  }

  detailByModal(id: any) {
    const initialState = { vehicleId: id };
    this.modalService.show(DriverVehicleModalComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg modal-driver-vehicle'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      onHidden.unsubscribe();
    });
  }
}
