img {
  margin-right: 5px;
}

.m-form__group {
  padding-left: 20px;
  padding-right: 30px;

  .labelIds {
    position: relative;

    .label {
      position: absolute;
      top: 8px;
      left: 27px;

      span {
        border-radius: 4px;
        margin-right: 10px;
        padding: 0 10px;
        border: 1px solid;
      }
    }
  }
}

.m-form .m-form__group {
  margin-bottom: 0;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 0;
}

.col-form-label {
  text-align: right;
}

table {
  width: 68%;
  table-layout: fixed;
  word-break: break-all;
  border-collapse: collapse;
  margin: 0 auto;
}

th {
  background-color: #e9edf4;
  text-align: left;
  padding: 0px 0px 0px 25px;
  height: 54px;
  border-radius: 4px 4px 0px 0px;
}

td {
  text-align: left;
  padding: 0px 0px 0px 25px;
  height: 54px;
  border-radius: 4px 4px 0px 0px;
}

tr {
  width: 100%;
}

.address {
  width: 55%;
}

.time {
  width: 15%;
}

.content-normal {
  color: #575e72;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
:host .plate-btn {
  color: rgb(0, 0, 255) !important;
  border-bottom: 1px solid rgb(0, 0, 255) !important;
}

.button-blue {
  width: 71px;
  height: 34px;
  background: #4777fd;
  border: 0px;
  color: #ffffff;
  border-radius: 4px;
  margin-left: 17px;
}
.button-blue:focus {
  outline: none;
}
.footer-btn {
  text-align: right;
  margin-top: 20px;
}
