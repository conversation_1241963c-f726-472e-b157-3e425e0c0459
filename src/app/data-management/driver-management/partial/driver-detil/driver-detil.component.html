<!-- <div class="fence-title">
  <span translate>数据管理 / 驾驶员</span>
  <span >&nbsp;&nbsp;/&nbsp;&nbsp; <span translate> 详情</span></span>
</div> -->
<div class="m-portlet">
  <div class="m-portlet__body">
    <div class="row">
      <div class="col-md-12">
        <div class="little-title">
          <h5 translate>
            <img src="/assets/font/managementTitle-front.png" />
            驾驶员信息
          </h5>
        </div>
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label" translate>所属机构：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.orgName" />
      </div>
      <label class="col-sm-2 col-form-label" translate>标签：</label>
      <div class="col-sm-4 labelIds">
        <input disabled type="text" class="form-control" class="form-control" />
        <div class="label">
          <span *ngFor="let item of driver?.labels">{{ item }}</span>
        </div>
        <div class="label" *ngIf="!driver?.labels.length">-</div>
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label" translate>驾驶员姓名：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.userName" />
      </div>
      <label class="col-sm-2 col-form-label" translate>联系电话：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.phone" />
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label" translate>所属公司：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.company" />
      </div>
      <label class="col-sm-2 col-form-label" translate>驾驶证到期日：</label>
      <div class="col-sm-4">
        <input
          disabled
          type="text"
          class="form-control"
          [value]="driver.licenseEnd | localDate : accountSetting.dateFormat"
        />
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label" translate>常用地址：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.addr" />
      </div>
      <label class="col-sm-2 col-form-label" translate>驾驶证号：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.driverLicense" />
      </div>
    </div>
    <div class="form-group m-form__group row">
      <label class="col-sm-2 col-form-label" translate>驾照等级：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.driverClass" />
      </div>
      <label class="col-sm-2 col-form-label" translate>驾龄：</label>
      <div class="col-sm-4">
        <input disabled type="text" class="form-control" [(ngModel)]="driver.drivingAge" />
      </div>
    </div>
  </div>
</div>

<div class="m-portlet">
  <div class="m-portlet__body">
    <div class="row">
      <div class="col-md-12">
        <div class="little-title">
          <h5 translate>
            <img src="/assets/font/managementTitle-front.png" />
            绑定车辆信息
          </h5>
        </div>
      </div>
    </div>
    <div class="table-list">
      <table>
        <tr>
          <th class="time" translate>
            {{ '车主名' | translate }}
          </th>
          <th class="time" translate>
            {{ '车牌号' | translate }}
          </th>
          <th class="time" translate>
            {{ '车辆型号' | translate }}
          </th>
        </tr>
        <tr
          *ngFor="let item of driver?.vehicles; let odd = odd"
          [ngStyle]="{ background: odd ? '#F4F7FB' : '#FFFFFF' }"
        >
          <td>
            <div class="content-normal">{{ item.ownerName }}</div>
          </td>
          <td>
            <div class="content-normal">
              <a class="plate-btn" (click)="detailByModal(item.id)">
                {{ item.plateNumber }}
              </a>
            </div>
          </td>
          <td>
            <div class="content-normal">
              {{ item.modelName }}
            </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="col-md-12 footer-btn">
      <button type="button " class="button-blue" routerLink="/vehicleManagement" translate>返 回</button>
    </div>
  </div>
</div>
