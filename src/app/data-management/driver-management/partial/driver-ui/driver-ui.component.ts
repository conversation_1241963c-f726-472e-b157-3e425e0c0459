import { Component, OnInit, Input, EventEmitter, Output, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { of } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, tap } from 'rxjs/operators';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { CustomValidators } from 'ngx-custom-validators';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { DriverService } from '../../shared/driver.service';
import { regex } from '@app/shared/utils/regex';
import { AccountService } from '@app/account-settings/services/account.service';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-driver-ui',
  templateUrl: './driver-ui.component.html',
  styleUrls: ['./driver-ui.component.scss']
})
export class DriverUiComponent implements OnInit {
  @Input() title: string = '新建';
  @Input() driver: any;
  // 有orgId表明从编辑车辆新增司机调用
  @Input()
  get orgId(): string {
    return this._orgId;
  }
  set orgId(orgId: string) {
    if (orgId && this.form) {
      this.form.patchValue({ orgId });
    }
    this._orgId = orgId;
  }
  @Output() save = new EventEmitter<any>();

  log: Logger;

  accountSetting = accountSetting;
  form: FormGroup;
  saving = false;

  listOfOption: any[];
  nodes: Array<any> = []; // 组织机构
  labelList: any[] = []; // 标签列表
  treeLoading = true;
  _orgId: string = '';

  get labelIds(): FormControl {
    return this.form.get('labelIds') as FormControl;
  }

  // 标签不超过三个
  confirmationValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length > 3) {
      return { confirm: true, error: true };
    }
    return null;
  };

  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private driverService: DriverService,
    public modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private cd: ChangeDetectorRef,
    private accountService: AccountService
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger(this.translate.instant('新建'));
  }

  ngOnChanges(changes: SimpleChanges): void {
    const edit = changes.driver ? changes.driver.currentValue : null;
    if (this.title === '编辑' && edit) {
      this.form.patchValue({
        orgId: this.driver.orgId,
        userName: this.driver.userName,
        phone: this.driver.phone,
        company: this.driver.company,

        licenseEnd: this.driver.licenseEnd ? new Date(this.driver.licenseEnd) : null,
        addr: this.driver.addr,
        driverLicense: this.driver.driverLicense,

        driverClass: this.driver.driverClass,
        drivingAge: this.driver.drivingAge
      });
      this.labelIds.setValue(this.driver.labelIds, { emitViewToModelChange: false });
    }
  }

  ngOnInit() {
    this.getLabels().subscribe();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    const params = this.form.getRawValue();
    params.id = this.driver ? this.driver.id : null;
    params.licenseEndLong = new Date(params.licenseEnd).valueOf();
    // 司机新增/编辑
    if (!this.orgId) {
      this.save.emit(params);
      return;
    }
    if (this.saving) {
      return;
    }
    this.saving = true;
    this.driverService
      .creatDriverInformation(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (res) => {
          if (!res || !res.success) {
            this.log.error(this.translate.instant(res.message || '新增失败'));
            return;
          }
          this.activeModal.hide();
        },
        () => this.log.error(this.translate.instant('新增失败'))
      );
  }

  buildForm() {
    const phoneNumberReg = /^((0\d{2,3}-\d{7,8})|(1[3|4|5|6|7|8|9][0-9]\d{8}))$/;
    this.form = this.formBuilder.group({
      orgId: [null, [Validators.required]],
      labelIds: [null, [this.confirmationValidator]],
      userName: [null, [Validators.required, Validators.pattern(regex.bookString)]],
      phone: [null, [Validators.required]],
      company: [null, [Validators.required]],

      licenseEnd: [null, [Validators.required]],
      addr: [null, [Validators.required]],
      driverLicense: [null, [Validators.required, CustomValidators.number]],

      driverClass: [null],
      drivingAge: [null]
    });

    // 模糊过滤不存在时,添加当前模糊搜索的内容为标签
    this.labelIds.valueChanges
      .pipe(
        filter((labelIds) => Array.isArray(labelIds)),
        map((labelIds) =>
          labelIds.filter((labelId: number | string) => typeof labelId === 'string' && !!labelId.trim())
        ),
        filter((labelNames) => labelNames.length === 1),
        switchMap((labelNames: string[]) => {
          let labelName = '';
          return this.driverService.addLabelList(labelNames[0].trim()).pipe(
            map((res) => {
              if (res.code === '200') {
                this.log.success(this.translate.instant['新增成功']);
                labelName = labelNames[0].trim();
              }
              return labelName;
            }),
            catchError(() => of(labelName))
          );
        }),
        switchMap((labelName) => {
          if (!labelName) {
            return of(labelName);
          }
          return this.getLabels().pipe(
            map(() => labelName),
            catchError(() => of(labelName))
          );
        }),
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe((labelName) => {
        let labelIds = this.labelIds.value;
        labelIds.pop();
        if (labelName) {
          const addItem = this.labelList.find((item) => item.labelName === labelName);
          if (addItem) {
            labelIds.push(addItem.id);
          }
        }
        this.labelIds.setValue(labelIds);
      });
  }

  // 获取标签
  getLabels() {
    return this.driverService.getLabelList().pipe(
      map((res) => {
        if (res.success) {
          this.labelList = Array.isArray(res.data) ? res.data : this.labelList;
        }
        return null;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService
        .getUserChildrenOrganizations(id)
        .pipe(finalize(() => this.cd.markForCheck()))
        .subscribe((organNodes) => {
          if (organizationId) {
            // 超管获取子级
            resolve(organNodes[0].children);
          } else {
            // 非超管直接获取子级组织机构树
            this.nodes = organNodes;
          }
          this.treeLoading = false;
        });
    });
  }
}
