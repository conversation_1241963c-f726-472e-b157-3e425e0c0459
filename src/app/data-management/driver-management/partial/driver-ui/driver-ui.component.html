<div class="modal-header">
  <h5 class="modal-title" translate>{{ title }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="orgId">
        {{ '所属机构' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="orgIdErrorTpl">
        <nz-tree-select
          formControlName="orgId"
          id="orgId"
          [nzDisabled]="title === '编辑' || !!orgId"
          [nzNodes]="nodes"
          [nzAllowClear]="title === '编辑' || !!orgId"
          nzShowSearch
          nzShowLine
          nzPlaceHolder="{{ '请选择所属机构' | translate }}"
          [nzAsyncData]="true"
          [nzNotFoundContent]="noData"
          (nzExpandChange)="expandChange($event)"
        ></nz-tree-select>
        <ng-template #noData>
          <div *ngIf="treeLoading" class="no-data">
            <nz-spin nzSimple></nz-spin>
          </div>
          <div *ngIf="!treeLoading" class="no-data">
            {{ '暂无数据' | translate }}
          </div>
        </ng-template>
        <ng-template #orgIdErrorTpl let-control>
          {{ '请选择所属机构' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24">
        {{ '标签' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="labelIdsErrorTpl">
        <nz-select
          formControlName="labelIds"
          id="labelIds"
          nzMode="tags"
          nzPlaceHolder="{{ '请选择标签，最多不超过三个' | translate }}"
        >
          <nz-option *ngFor="let option of labelList" [nzLabel]="option.labelName" [nzValue]="option.id"></nz-option>
        </nz-select>
        <ng-template #labelIdsErrorTpl let-control>
          {{ '最多可选择3个标签，请重新选择' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="userName">
        {{ '驾驶员姓名' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="userNameErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="50"
          formControlName="userName"
          id="userName"
          placeholder="{{ '请输入驾驶员姓名' | translate }}"
        />
        <ng-template #userNameErrorTpl let-control>
          {{ '请输入驾驶员姓名' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="phone">
        {{ '联系电话' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="phoneErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="30"
          formControlName="phone"
          id="phone"
          placeholder="{{ '请输入联系电话' | translate }}"
        />
        <ng-template #phoneErrorTpl let-control>
          {{ '请输入联系电话' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="company">
        {{ '所属公司' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="companyErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="100"
          formControlName="company"
          id="company"
          placeholder="{{ '请输入驾驶员所属公司' | translate }}"
        />
        <ng-template #companyErrorTpl let-control>
          {{ '请输入驾驶员所属公司' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="licenseEnd">
        {{ '驾驶证到期日' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="licenseEndErrorTpl">
        <nz-date-picker
          class="w-full"
          [nzFormat]="accountSetting.dateFormat"
          nzPlaceHolder="{{ '请选择驾驶证到期日' | translate }}"
          formControlName="licenseEnd"
          id="licenseEnd"
          appLocalNzDateTime
        ></nz-date-picker>
        <ng-template #licenseEndErrorTpl let-control>
          {{ '请选择驾驶证到期日' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="addr">
        {{ '常用地址' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="addrErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="100"
          formControlName="addr"
          id="addr"
          placeholder="{{ '请输入常用地址' | translate }}"
        />
        <ng-template #addrErrorTpl let-control>
          {{ '请输入常用地址' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired nzFor="driverLicense">
        {{ '驾驶证号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="driverLicenseErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="18"
          formControlName="driverLicense"
          id="driverLicense"
          placeholder="{{ '请输入驾驶证号' | translate }}"
        />
        <ng-template #driverLicenseErrorTpl let-control>
          {{ '请输入正确的驾驶证号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzFor="driverClass">
        {{ '驾照等级' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="driverClassErrorTpl">
        <input
          type="text"
          nz-input
          formControlName="driverClass"
          maxlength="100"
          id="driverClass"
          placeholder="{{ '请输入驾照等级' | translate }}"
        />
        <ng-template #driverClassErrorTpl let-control>
          {{ '请输入驾照等级' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="8" [nzXs]="24" nzFor="drivingAge">
        {{ '驾龄' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="16" [nzXs]="24" [nzErrorTip]="drivingAgeErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="100"
          formControlName="drivingAge"
          id="drivingAge"
          placeholder="{{ '请输入驾龄' | translate }}"
        />
        <ng-template #drivingAgeErrorTpl let-control>
          {{ '请输入驾龄' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
