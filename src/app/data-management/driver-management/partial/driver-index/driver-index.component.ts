import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Router } from '@angular/router';

import { max } from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import moment from 'moment';
import { NzMessageService } from 'ng-zorro-antd/message';
import { DatatableComponent } from '@swimlane/ngx-datatable';

import { environment } from '@env/environment';
import { Logger } from '@core/logger.service';
import { Dialogs } from '@core/dialogs.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { DriverService } from '../../shared/driver.service';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { QueryTemplate, RowDetailToggleEvent } from '@app/shared/models/type';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';

import { Order } from '../../shared/models';
import { Language } from '@app/account-settings/models/account';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { DriverCreatComponent } from '../driver-creat/driver-creat.component';
import { ImportFileComponent } from '@app/shared';
import { DriverEditComponent } from '../driver-edit/driver-edit.component';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';
import { AccountService } from '@app/account-settings/services/account.service';

@Component({
  selector: 'app-driver-index',
  templateUrl: './driver-index.component.html',
  styleUrls: ['./driver-index.component.scss']
})
export class DriverIndexComponent implements OnInit, AfterViewInit {
  [x: string]: any;

  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;
  driverList: Array<any>;
  selecteddriverList: Array<Order> = [];
  isShowCollapse = false;
  event: any;
  currentNumber = 10;
  totalNumber: number;
  datatable: any;

  listOfOption: any[];
  nodes: Array<any> = [];
  labelIds: any[];
  treeLoading = true;

  previousPage = 0; // 上一页
  nextPage = 0; // 下一页
  originTemplate: any;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'userName', op: 'eq' },
          { field: 'labelIds', op: 'eq' },
          { field: 'phone', op: 'eq' },
          { field: 'company', op: 'eq' },
          { field: 'licenseEnd', op: 'eq' },
          { field: 'addr', op: 'eq' },
          { field: 'driverLicense', op: 'eq' }
        ],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  // @ViewChild('dt') datatable: DatatableComponent;
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;

  constructor(
    private dialogs: Dialogs,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef,
    private driverService: DriverService,
    private translate: TranslateService,
    private referenceDataService: ReferenceDataService,
    private router: Router,
    private message: NzMessageService,
    private accountService: AccountService
  ) {
    this.translate.get('驾驶人').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
    this.checkFilters(); // 回显过滤条件
  }

  ngOnInit() {
    this.getLabels();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  ngAfterViewInit(): void {
    this.changeDetectorRef.detectChanges();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  // 获取标签
  getLabels() {
    this.driverService.getLabelList().subscribe(
      (res) => {
        if (res.success) {
          this.listOfOption = res.data;
        } else {
          console.log('标签列表获取失败', res.msg);
        }
      },
      (error) => console.log('标签列表获取失败', error)
    );
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
        this.treeLoading = false;
      });
    });
  }

  onSelect(event: any) {
    if (event !== void 0 && event.selected !== void 0) {
      this.selecteddriverList = event.selected;
    }
  }
  turnPage() {
    return this.ngxQuery.validateQuery();
  }
  refreshData() {
    this.loadDrivers(this.event);
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      map[key] = rule.data;
      if (rule.field === 'licenseEnd') {
        const arr: Array<any> = ['', ''];
        if (rule.data) {
          arr[0] = moment(rule.data[0]).format('YYYY-MM-DD HH:mm:ss').split(' ')[0];
          arr[1] = moment(rule.data[1]).format('YYYY-MM-DD HH:mm:ss').split(' ')[0];
          page.filters.push({
            field: rule.field,
            op: 'ge',
            term: arr[0]
          });
          page.filters.push({
            field: rule.field,
            op: 'le',
            term: arr[1]
          });
        }
        page.filter.rules.push({
          field: rule.field.op,
          op: 'bt',
          data: undefined,
          datas: arr
        });
        const start = new Date(arr[0]);
        const end = new Date(arr[1]);
        map.startTime = start.getTime();
        map.endTime = end.getTime();
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    map.licenseEnd = '';
    this.labelIds = map.labelIds;
    return map;
  }

  loadDrivers(event: any) {
    this.event = event;
    const page = event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.footer.showTotalElements = true;
    this.selecteddriverList.length = 0;
    this.driverService
      .getDrivers(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.data.records && response.data.records.length !== 0) {
            let maxOrgname = 0,
              maxIabel = 0,
              maxCompany = 0,
              maxAddr = 0;
            response.data.records.forEach((item: any) => {
              maxOrgname = item.orgName.length > maxOrgname ? item.orgName.length : maxOrgname;
              if (item.labelList) {
                let labelNum = 0;
                item.labelList.forEach((ctem: { labelName: string | any[] }) => {
                  labelNum = ctem.labelName.length + labelNum;
                  console.log(maxIabel);
                });
                maxIabel = max([maxIabel, labelNum]);
              }
              maxCompany = item.company && item.company.length > maxCompany ? item.company.length : maxCompany;
              maxAddr = item.addr && item.addr.length > maxAddr ? item.addr.length : maxAddr;
            });
            maxOrgname = Math.max.apply(null, [14 * maxOrgname + 34, 100]);
            maxCompany = Math.max.apply(null, [14 * maxCompany + 34, 100]);
            maxAddr = Math.max.apply(null, [14 * maxAddr + 34, 100]);
            maxIabel = Math.max.apply(null, [16 * maxIabel + 34, 100]);
            this.table.headerComponent.onColumnResized(maxOrgname, this.table.headerComponent.columns[0]);
            this.table.headerComponent.onColumnResized(maxIabel, this.table.headerComponent.columns[2]);
            this.table.headerComponent.onColumnResized(maxCompany, this.table.headerComponent.columns[4]);
            this.table.headerComponent.onColumnResized(maxAddr, this.table.headerComponent.columns[6]);
            // this.nextPage = maxOrgname + maxIabel + maxCompany + maxAddr;
          }
          this.driverList = response.data.records;
          // console.log(this.previousPage > this.nextPage);
          // if (this.nextPage <= this.previousPage) {
          //   const ev: any = { scrollYPos: 0, scrollXPos: 0 };
          //   this.table.bodyComponent.onBodyScroll(ev);
          // }
          // this.previousPage = this.nextPage;
          this.currentNumber = this.driverList ? this.driverList.length : 0;
        },
        (error) => console.log('驾驶员管理列表获取失败', error)
      );
  }

  delete(row: any) {
    this.translate.get(`真的要删除吗？`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.driverService.deleteDriver(row).subscribe(
            (data) => {
              if (data.success) {
                this.translate.get(`删除成功`).subscribe((res) => {
                  this.log.success(res);
                });
              } else {
                this.translate.get(data.message).subscribe((res) => {
                  this.log.error(res);
                });
              }
              this.ngxQuery.executeQuery();
            },
            (error) => console.log(`列表 ${row.userName} 删除失败，失败信息：`, error)
          );
        },
        () => console.log(`取消删除列表 ${row.userName}`)
      );
    });
  }

  // 编辑
  editByModal(row: any) {
    const initialState = { data: row.id };
    this.modalService.show(DriverEditComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 查看详情
  checkDetail(row: any) {
    this.event.query.query.rules.pop();
    sessionStorage.setItem('driverManagement', JSON.stringify(this.event.query.query));
    this.router.navigate(['/driverManagement/' + row.id]);
  }
  // 返回 保留筛选条件
  checkFilters() {
    const arr = environment.prePath.split('&');
    const prePage = arr[arr.length - 1];
    if (prePage && arr[1].search('driverManagement') !== -1) {
      const template = sessionStorage.getItem('driverManagement');
      const obj = JSON.parse(template);
      if (obj) {
        this.queryTemplates[0].template = obj;
      }
    } else {
      environment.prePath = '';
    }
    sessionStorage.removeItem('driverManagement');
  }

  // 重置查询模板
  reset($event: any) {
    this.queryTemplates = this.originTemplate;
  }

  // 新建
  creatByModal() {
    this.modalService.show(DriverCreatComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  onDetailToggle(event: RowDetailToggleEvent<Order>) {
    console.log('Detail Toggled', event);
  }

  toggleExpandRow(row: Order) {
    console.log('Toggled Expand Row!', row);
    this.datatable.rowDetail.toggleExpandRow(row);
  }

  // 导入
  importArchiveBorrow() {
    // const lang = Language[accountSetting.language];
    const lang = window.localStorage.getItem('lang');
    let language = '';
    if (lang === 'zh-CN') {
      language = 'cn';
    }
    if (lang === 'en-US') {
      language = 'en';
    }
    if (lang === 'es-MX') {
      language = 'esp';
    }
    const initialState = {
      tplUrl: `glcrm-vehicle-api/v1/api/vehicle/user/template/${language}`,
      // uploadUrl: `glcrm-vehicle-api/v1/api/vehicle/user/import/${language}`
      uploadUrl: `glcrm-vehicle-api/v1/api/vehicle/user/import`
    };
    this.modalService.show(ImportFileComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom',
      initialState
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  exportReport() {
    const curDate: any = new Date();
    const param = this.loadProperty(this.event.page);
    // const lang = window.localStorage.getItem('lang');
    // let language = '';
    // if (lang === 'zh-CN') {
    //   language = 'cn';
    // }
    // if (lang === 'en-US') {
    //   language = 'en';
    // }
    // if (lang === 'es-MX') {
    //   language = 'esp';
    // }
    const params = {
      addr: param.addr,
      endTime: param.endTime,
      startTime: param.startTime,
      userName: param.userName,
      company: param.orgName,
      phone: param.phone,
      driverLicense: param.driverLicense
      // language: language
    };
    this.driverService
      .exportReport(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          const link = document.createElement('a');
          const blob = new Blob([response.body], { type: 'application/xlsx' }),
            doloadUrl = window.URL.createObjectURL(blob);
          link.setAttribute('href', doloadUrl);
          this.translate.get('驾驶员信息').subscribe((res: string) => {
            link.setAttribute(
              'download',
              res +
                curDate.getFullYear() +
                (curDate.getMonth() + 1) +
                curDate.getDate() +
                curDate.getHours() +
                curDate.getMinutes() +
                curDate.getSeconds() +
                '.xlsx'
            );
          });
          // tslint:disable-next-line: max-line-length
          // link.setAttribute('download', '驾驶员信息列表' + curDate.getFullYear() + (curDate.getMonth() + 1) + curDate.getDate() + curDate.getHours() + curDate.getMinutes() + curDate.getSeconds() + '.xlsx');
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (error) => {
          this.translate.get('导出失败:').subscribe((res1: string) => {
            this.translate.get('导出数量超过2000条').subscribe((res2: string) => {
              if (error === 'OK') {
                // 此时说明是因为超过2000条失败
                this.log.error(res1, res2);
              } else {
                this.log.error(res1, error);
              }
            });
          });
        }
      );
  }
  getTotal() {
    const page = this.event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.driverService
      .getPageNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.success) {
            this.totalNumber = response.data;
          } else {
            console.log('设备列表总数获取失败。', response.msg);
          }
        },
        (error) => console.log('设备列表总数获取失败。', error)
      );
  }

  selectMax(e: any) {
    if (e.length > 3) {
      this.labelIds.pop();
      this.translate.get('只能选择3个标签进行查询').subscribe((res: string) => {
        this.message.create('warning', res);
      });
      // this.message.create('warning', '只能选择3个标签进行查询');
    }
  }
}
