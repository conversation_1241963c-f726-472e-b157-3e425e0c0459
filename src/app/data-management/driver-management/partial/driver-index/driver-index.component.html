<div class="m-portlet list_header" #driverIndex>
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      [mode]="mode"
      (reset)="reset($event)"
      [showPlainCollapseToolBar]="true"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择所属机构' | translate }}"
            [nzAsyncData]="true"
            [nzNotFoundContent]="noData"
            (nzExpandChange)="expandChange($event)"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
          <ng-template #noData>
            <div *ngIf="treeLoading" class="no-data">
              <nz-spin nzSimple></nz-spin>
            </div>
            <div *ngIf="!treeLoading" class="no-data">
              {{ '暂无数据' | translate }}
            </div>
          </ng-template>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'userName'" label="{{ '驾驶员姓名' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入驾驶员姓名' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'labelIds'" label="{{ '标签' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="labelIds"
            [(ngModel)]="rule.datas[dataIndex]"
            (ngModelChange)="selectMax($event)"
            nzMode="multiple"
            nzPlaceHolder="{{ '请选择标签' | translate }}"
            nzShowSearch
          >
            <nz-option
              *ngFor="let option of listOfOption"
              [nzLabel]="option.labelName"
              [nzValue]="option.id"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'phone'" label="{{ '联系电话' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入联系电话' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'company'" label="{{ '所属公司' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入所属公司' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'licenseEnd'" label="{{ '驾驶证到期日' | translate }}" [type]="'date'">
        <ng-template
          ngx-query-value-input-template
          dataType="date"
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <nz-range-picker
            class="w-full"
            [nzFormat]="accountSetting.dateFormat"
            [nzAllowClear]="false"
            [(ngModel)]="rule.datas[dataIndex]"
            appLocalNzDateTime
          ></nz-range-picker>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'addr'" label="{{ '常用地址' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入常用地址' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'driverLicense'" label="{{ '驾驶证号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入驾驶证号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<!--  -->
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-tools">
      <ul class="m-portlet__nav" style="float: left">
        <li class="m-portlet__nav-item" [appApplyPermission]="'driver_import'">
          <button nz-button nzType="primary" (click)="importArchiveBorrow()">
            {{ '批量导入' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'driver_add'">
          <button nz-button (click)="creatByModal()">
            {{ '新建' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'driver_export'">
          <button nz-button (click)="exportReport()">
            {{ '导出' | translate }}
          </button>
        </li>
      </ul>
    </div>
  </div>

  <div class="m-portlet__body p-0">
    <!--
        (select)="onSelect($event)"
        [selected]="selecteddriverList"
        [selectionType]="'checkbox'"
    -->
    <ngx-datatable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="driverList"
      appNgxDataTable
      [saveState]="false"
      [loadingIndicator]="loading"
      [ngxQuery]="ngxQuery"
      (loadValue)="loadDrivers($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
      [columnMode]="'force'"
    >
      <ngx-datatable-column
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.orgName }}">{{ row.orgName }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [width]="200" name="{{ '驾驶员姓名' | translate }}" prop="userName"></ngx-datatable-column>
      <ngx-datatable-column name="{{ '标签' | translate }}" prop="plateNumber">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span class="travel" *ngFor="let item of row.labelList">{{ item.labelName }}</span>
          <span *ngIf="!row.labelList.length">-</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '联系电话' | translate }}"
        prop="phone"
        [width]="150"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '所属公司' | translate }}"
        [width]="300"
        prop="company"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '驾驶证到期日' | translate }}"
        prop="licenseEnd"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.licenseEnd | localDate : accountSetting.dateFormat }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '常用地址' | translate }}"
        [width]="200"
        prop="addr"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="200"
        name="{{ '驾驶证号' | translate }}"
        prop="driverLicense"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [frozenRight]="true"
        [width]="180"
        name="{{ '操作' | translate }}"
        prop="total"
        headerClass="text-center"
        cellClass="text-center"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span appApplyPermission="driver_detail" class="margin_right" (click)="checkDetail(row)">
            <a>
              <img
                src="/assets/font/detail.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '详情' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="driver_detail" class="margin_right">|</span>

          <span appApplyPermission="driver_edit" class="margin_right" (click)="editByModal(row)">
            <a>
              <img
                src="/assets/font/edit.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '编辑' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="driver_edit" class="margin_right">|</span>

          <span class="margin_right" [appApplyPermission]="'driver_delete'" (click)="delete(row)">
            <a>
              <img
                src="/assets/font/delete.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '删除' | translate }}"
              />
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [frozenRight]="true" [width]="50" headerClass="datatable-header-cell-acitons text-left">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
