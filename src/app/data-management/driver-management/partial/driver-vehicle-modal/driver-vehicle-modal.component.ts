import { Component, Input, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-driver-vehicle-modal',
  templateUrl: './driver-vehicle-modal.component.html',
  styleUrls: ['./driver-vehicle-modal.component.scss']
})
export class DriverVehicleModalComponent implements OnInit {
  @Input() vehicleId: number;

  constructor(public activeModal: BsModalRef) {}

  ngOnInit(): void {}
}
