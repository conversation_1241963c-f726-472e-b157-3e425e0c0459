import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RouteExtensions } from '@app/core';

import { DriverIndexComponent } from './partial/driver-index/driver-index.component';
import { DriverCreatComponent } from './partial/driver-creat/driver-creat.component';
import { DriverDetilComponent } from './partial/driver-detil/driver-detil.component';
import { DriverEditComponent } from './partial/driver-edit/driver-edit.component';

const routes: Routes = RouteExtensions.withHost({ path: '', component: DriverIndexComponent }, [
  { path: 'new', component: DriverCreatComponent, data: { title: '新建' } },
  { path: ':id/edit', component: DriverEditComponent, data: { title: '编辑' } },
  { path: ':id', component: DriverDetilComponent, data: { title: '详情' } }
]);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DriverManagementRoutingModule {}
