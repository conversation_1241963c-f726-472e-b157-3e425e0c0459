import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { WebApiResultResponse, PagingResponse } from '@core/http/web-api-result-response';
import { QueryPageList } from '@app/shared/models/type';
import { Order } from './models';

@Injectable({
  providedIn: 'root'
})
export class DriverService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');
  constructor(private http: HttpClient) {
    super();
  }

  getDrivers(params: QueryPageList): Observable<PagingResponse> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  getDriverDetail(orderId: number): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/${orderId}`;
    return this.http.get<any>(url);
  }

  creatDriverInformation(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/users';
    return this.http.post(url, params);
  }

  updateDriver(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/users';
    return this.http.put(url, params);
  }

  deleteDriver(entity: Order): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/users/${entity.id}`;
    return this.http.delete(url);
  }
  // f分页
  getPageNumber(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/vehicle/user/users/vehicelTotal';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 导出
  exportReport(param: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = `glcrm-vehicle-api/v1/api/vehicle/user/export`;
    const options: Object = {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response',
      params: param
    };
    return this.http.get(url, options).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 标签
  getLabelList(): Observable<any> {
    const url = 'glcrm-account-api/v1/api/label/labelList';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 新增标签列表
  addLabelList(labelName: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/addLabel?labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  getDriverDetial(id: number): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/vehicle/${id}`;
    return this.http.get<any>(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备定位
  batchPosition(id: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/device?imei=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
