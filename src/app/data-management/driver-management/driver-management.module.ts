import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { SharedModule } from '../../shared/shared.module';
import { MapModule } from '@app/map/map.module';
import { DriverManagementRoutingModule } from './driver-management-routing.module';

import { DriverIndexComponent } from './partial/driver-index/driver-index.component';
import { DriverDetilComponent } from './partial/driver-detil/driver-detil.component';
import { DriverCreatComponent } from './partial/driver-creat/driver-creat.component';
import { DriverEditComponent } from './partial/driver-edit/driver-edit.component';
import { DriverUiComponent } from './partial/driver-ui/driver-ui.component';

import { DriverVehicleModalComponent } from './partial/driver-vehicle-modal/driver-vehicle-modal.component';
import { VehicleDetailComponent } from './partial/vehicle-detail/vehicle-detail.component';

@NgModule({
  imports: [
    FormsModule,
    SharedModule,
    TranslateModule,
    CommonModule,
    ReactiveFormsModule,
    MapModule,
    DriverManagementRoutingModule,
    NzDatePickerModule
  ],
  declarations: [
    DriverIndexComponent,
    DriverCreatComponent,
    DriverDetilComponent,
    DriverEditComponent,
    VehicleDetailComponent,
    DriverUiComponent,
    DriverVehicleModalComponent
  ],
  exports: [
    DriverIndexComponent,
    DriverCreatComponent,
    DriverDetilComponent,
    DriverEditComponent,
    DriverUiComponent,
    VehicleDetailComponent
  ],
  entryComponents: [DriverCreatComponent, DriverEditComponent, DriverVehicleModalComponent]
})
export class DriverManagementModule {}
