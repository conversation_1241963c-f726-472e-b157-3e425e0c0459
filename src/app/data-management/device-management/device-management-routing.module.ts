import { DeviceDeiailComponent } from './partial/device-deiail/device-deiail.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';
import { DeviceIndexComponent } from './partial/device-index/device-index.component';

import { DeviceCreatComponent } from './partial/device-creat/device-creat.component';
import { EditComponent } from './partial/edit/edit.component';

const routes: Routes = RouteExtensions.withHost({ path: '', component: DeviceIndexComponent }, [
  { path: 'new', component: DeviceCreatComponent, data: { title: '设备新建' } },
  { path: ':id/edit', component: EditComponent, data: { title: '设备编辑' } },
  { path: ':id', component: DeviceDeiailComponent, data: { title: '设备详情' } }
]);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DeviceManagementRoutingModule {}
