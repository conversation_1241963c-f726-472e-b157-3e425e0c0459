import { Component, OnInit, Input } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { DeviceService } from '../../shared/device.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-edit',
  templateUrl: './edit.component.html',
  styleUrls: ['./edit.component.scss']
})
export class EditComponent implements OnInit {
  @Input() data: number;

  log: Logger;
  title = '编辑';

  saving: boolean = false;
  device: any;

  constructor(
    public activeModal: BsModalRef,
    private deviceService: DeviceService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService
  ) {
    this.translate.get('编辑').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {
    this.getDevice();
  }

  getDevice() {
    this.deviceService.getDevice(this.data).subscribe((respone) => {
      this.device = respone.data || {};
      if (Array.isArray(this.device.labelList)) {
        this.device.labelIds = this.device.labelList.map((item: any) => item.labelId);
      }
    });
  }

  save(params: any) {
    this.saving = true;
    this.deviceService
      .updateDevice(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((res) => {
        if (!res.success) {
          this.translate.get(res.message).subscribe((res: string) => {
            this.log.error(res);
          });
          return;
        }
        this.activeModal.hide();
        this.translate.get('信息编辑成功!').subscribe((res: string) => {
          this.log.success(res);
        });
      });
  }
}
