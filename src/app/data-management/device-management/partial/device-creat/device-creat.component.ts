import { Component, OnInit } from '@angular/core';
import { Logger, LoggerFactory } from '@app/core';
import { finalize } from 'rxjs/operators';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DeviceService } from '../../shared/device.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-device-creat',
  templateUrl: './device-creat.component.html',
  styleUrls: ['./device-creat.component.scss']
})
export class DeviceCreatComponent implements OnInit {
  public log: Logger;
  saving = false;
  title = '新建';

  constructor(
    public activeModal: BsModalRef,
    private deviceService: DeviceService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService,
    private translate: TranslateService
  ) {
    this.translate.get('新建').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
  }

  ngOnInit() {}

  save(params: any) {
    if (!this.saving) {
      this.saving = true;
      this.deviceService
        .creatDevice(params)
        .pipe(finalize(() => (this.saving = false)))
        .subscribe((res) => {
          if (!res.success) {
            this.translate.get(res.message).subscribe((res: string) => {
              this.log.error(res);
            });
            return;
          }
          this.activeModal.hide();
          this.translate.get('新增成功').subscribe((res: string) => {
            this.log.success(res);
          });
        });
    }
  }
}
