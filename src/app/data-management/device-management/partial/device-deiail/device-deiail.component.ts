import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';

import { BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { accountSetting } from '@app/account-settings/models/account-setting';
import { Theme } from '@app/shared/models/theme';

import { vehicleStatusInfo, VehicleStatus } from '@app/data-management/vehicle-management/shared/models/vehicle';
import { Device, sensorTypeInfo, SensorConfig, DeviceStatus, deviceStatusInfo } from '../../shared/models/device';
import { DeviceService } from '../../shared/device.service';
import { EditComponent } from '../edit/edit.component';

@Component({
  selector: 'app-device-deiail',
  templateUrl: './device-deiail.component.html',
  styleUrls: ['./device-deiail.component.scss']
})
export class DeviceDeiailComponent implements OnInit {
  log: Logger;
  title = '设备详情';
  accountSetting = accountSetting;
  Theme = Theme;

  vehicleStatusInfo = vehicleStatusInfo;
  VehicleStatus = VehicleStatus;
  sensorTypeInfo = sensorTypeInfo;
  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;

  device: any;
  devicePosition: any; // 定位信息
  deviceId: number;

  constructor(
    private route: ActivatedRoute,
    private loggerFactory: LoggerFactory,
    private translate: TranslateService,
    private modalService: BsModalService,
    public deviceService: DeviceService
  ) {
    this.translate.get('设备详情').subscribe((res: string) => {
      this.log = this.loggerFactory.getLogger(res);
    });
    this.translate.get('设备详情').subscribe((res: string) => {
      this.title = res;
    });
  }

  ngOnInit() {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.deviceId = id;
        this.getDevice();
      }
    });
  }

  getDevice() {
    this.deviceService.getDevice(this.deviceId).subscribe(
      (res) => {
        if (!res.success || !res.data) {
          return;
        }
        res.data.sensorConfig = [];
        this.device = res.data;
        this.getDeviceLocation(this.device.deviceNo);
        if (res.data?.isSensor === '1') {
          this.getSensorInfo();
        }
      },
      (error) => console.log(`设备数据获取失败,失败信息: ${error}`)
    );
  }

  getDeviceLocation(ev: any) {
    console.log(ev);
    this.deviceService.equipmentPosition(ev).subscribe(
      (res) => {
        if (res.success) {
          this.devicePosition = res.data;
        } else {
          console.log('设备定位信息获取失败', res.message);
        }
      },
      (error) => console.log('设备定位信息获取失败', error)
    );
  }

  getSensorInfo() {
    if (!this.device?.deviceNo) {
      return;
    }
    this.deviceService.getSensorInfo(this.device.deviceNo).subscribe((res: any) => {
      if (!res.success || !res.data) {
        return;
      }
      let sensorConfig = res?.data?.sensorConfig || [];
      sensorConfig = sensorConfig
        .filter((sensor: SensorConfig) => sensorTypeInfo[sensor.type])
        .map((sensor: SensorConfig) => {
          sensor.value = this.deviceService.formatSensorValue(sensor);
          return sensor;
        });
      this.device.sensorConfig = this.deviceService.collectSensorValue(sensorConfig);
    });
  }

  // 编辑
  editByModal() {
    const initialState = { data: this.device.id };
    this.modalService.show(EditComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: Device) => {
      this.getDevice();
      onHidden.unsubscribe();
    });
  }
}
