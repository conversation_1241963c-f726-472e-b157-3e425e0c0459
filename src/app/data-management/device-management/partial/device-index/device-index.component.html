<div class="m-portlet list_header" #deviceIndex>
  <div class="ngx-query-container">
    <ngx-query
      [hidden]="false"
      [datePickerReadonly]="false"
      [columnNumber]="3"
      #ngxQuery
      [queryTemplates]="queryTemplates"
      [showModeButtons]="true"
      (reset)="reset($event)"
      [mode]="mode"
      [showPlainCollapseToolBar]="true"
    >
      <ngx-query-field [name]="'orgId'" label="{{ '所属机构' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-tree-select
            class="w-full"
            [nzNodes]="nodes"
            nzShowSearch
            nzShowLine
            nzPlaceHolder="{{ '请选择' | translate }}"
            [nzAsyncData]="true"
            [nzNotFoundContent]="noData"
            (nzExpandChange)="expandChange($event)"
            [(ngModel)]="rule.datas[dataIndex]"
          ></nz-tree-select>
          <ng-template #noData>
            <div *ngIf="treeLoading" class="no-data">
              <nz-spin nzSimple></nz-spin>
            </div>
            <div *ngIf="!treeLoading" class="no-data">
              {{ '暂无数据' | translate }}
            </div>
          </ng-template>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'deviceNo'" label="{{ '设备号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入设备号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'labelIds'" label="{{ '标签' | translate }}" [type]="'string'">
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="labelIds"
            [(ngModel)]="rule.datas[dataIndex]"
            (ngModelChange)="selectMax($event)"
            nzMode="multiple"
            nzPlaceHolder="{{ '请选择标签' | translate }}"
            nzShowSearch
          >
            <nz-option
              *ngFor="let option of listOfOption"
              [nzLabel]="option.labelName"
              [nzValue]="option.id"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field
        [name]="'deviceTypeId'"
        label="{{ '设备型号' | translate }}"
        [type]="'string'"
        [custom]="deviceTypeList"
      >
        <ng-template ngx-query-value-input-template let-rule="rule" let-dataIndex="dataIndex" let-options="custom">
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择设备型号' | translate }}"
          >
            <nz-option
              *ngFor="let option of deviceTypeList"
              [nzLabel]="option.typeName"
              [nzValue]="option.id"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field
        [name]="'wireless'"
        label="{{ '设备类型' | translate }}"
        [type]="'string'"
        [custom]="wirelessList"
      >
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-options="custom"
        >
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择设备类型' | translate }}"
          >
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'status'" label="{{ '设备状态' | translate }}" [type]="'string'" [custom]="statusList">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-options="custom"
        >
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择设备状态' | translate }}"
          >
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>
      <ngx-query-field [name]="'hasBinding'" label="{{ '是否绑定' | translate }}" [type]="'string'" [custom]="yesList">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-options="custom"
        >
          <nz-select
            class="w-full"
            [(ngModel)]="rule.datas[dataIndex]"
            nzPlaceHolder="{{ '请选择是否绑定' | translate }}"
          >
            <nz-option
              *ngFor="let item of options"
              nzLabel="{{ item.name | translate }}"
              [nzValue]="item.key"
            ></nz-option>
          </nz-select>
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'producerName'" label="{{ '供应商' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入供应商' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>

      <ngx-query-field [name]="'simNo'" label="{{ 'SIM卡号' | translate }}" [type]="'string'">
        <ng-template
          ngx-query-value-input-template
          let-rules="rules"
          let-rule="rule"
          let-dataIndex="dataIndex"
          let-placeholder="placeholder"
        >
          <input
            type="text"
            nz-input
            placeholder="{{ '请输入SIM卡号' | translate }}"
            [(ngModel)]="rule.datas[dataIndex]"
          />
        </ng-template>
      </ngx-query-field>
    </ngx-query>
  </div>
</div>
<!--  -->
<div class="m-portlet">
  <div class="m-portlet__head">
    <div class="m-portlet__head-tools">
      <ul class="m-portlet__nav" style="float: left">
        <li class="m-portlet__nav-item" [appApplyPermission]="'device_import'">
          <button nz-button nzType="primary" (click)="importArchiveBorrow()">
            {{ '批量导入' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'device_add'">
          <button nz-button (click)="creatByModal()">
            {{ '新建' | translate }}
          </button>
        </li>
        <li class="m-portlet__nav-item" [appApplyPermission]="'device_export'">
          <button nz-button (click)="exportReport()">
            {{ '导出' | translate }}
          </button>
        </li>
      </ul>
    </div>
  </div>

  <div class="m-portlet__body p-0">
    <!--
      (select)="onSelect($event)"
      [selectionType]="'checkbox'"
      [selected]="deviceListed"
    -->
    <ngx-datatable
      #dt
      class="material"
      [scrollbarH]="true"
      [rows]="deviceList"
      [saveState]="false"
      [loadingIndicator]="loading"
      appNgxDataTable
      [ngxQuery]="ngxQuery"
      #scroller
      (loadValue)="loadDevices($event)"
      [isRetainCurrentPageQuery]="false"
      ngxNoPageFooterWatcher
      [footer]="footer"
      [count]="currentNumber"
      [selectAllRowsOnPage]="false"
      externalPaging="false"
      style="width: 100%"
      [columnMode]="'force'"
    >
      <ngx-datatable-column
        name="{{ '所属机构' | translate }}"
        prop="orgName"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.orgName }}">{{ row.orgName }}</div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="220"
        name="{{ '设备号' | translate }}"
        prop="deviceNo"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div class="ellipsis" title="{{ row.deviceNo }}">
            {{ row.deviceNo }}
            <img
              *ngIf="row.isExpire"
              nz-tooltip
              nzTooltipPlacement="top"
              nzTooltipTitle="{{ '逾期' | translate }}"
              class="overdue-icon"
              src="/assets/font/overdue-icon.png"
            />
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="{{ '标签' | translate }}" prop="plateNumber">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span class="travel" *ngFor="let item of row.labelList">{{ item.labelName }}</span>
          <span *ngIf="(row.labelList && !row.labelList.length) || !row.labelList">-</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="210"
        name="{{ '传感器' | translate }}"
        prop="sensorConfig"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <a class="sensors">
            <i
              class="iot {{ sensorTypeInfo[sensor.type].iconNew }} sensor-icon-page"
              *ngFor="let sensor of row?.sensorConfig"
              nz-tooltip
              nzTooltipTitle="{{ sensorTypeInfo[sensor.type].label | translate }}"
            ></i>
          </a>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [width]="200" name="{{ '设备型号' | translate }}" prop="typeName"></ngx-datatable-column>
      <ngx-datatable-column
        [width]="120"
        name="{{ '设备类型' | translate }}"
        prop="wireless"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span *ngIf="row.wireless == '0'" translate>{{ '有线' | translate }}</span>
          <span *ngIf="row.wireless == '1'" translate>{{ '无线' | translate }}</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [width]="100" name="{{ '电量' | translate }}" prop="battary">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span *ngIf="row.wireless === '0'">-</span>
          <span *ngIf="row.wireless === '1'">{{ row.battary === null ? '' : row.battary + '%' }}</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="120"
        name="{{ '设备状态' | translate }}"
        prop="status"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <nz-badge
            [nzColor]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].backgroundColor"
            [nzText]="deviceStatusInfo[row?.status || DeviceStatus.Invalid].name | translate"
          ></nz-badge>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        [width]="130"
        name="{{ '是否绑定' | translate }}"
        prop="hasBinding"
        headerClass="text-left"
        cellClass="text-left"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span *ngIf="row.hasBinding" translate>{{ '是' | translate }}</span>
          <span *ngIf="!row.hasBinding" translate>{{ '否' | translate }}</span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column
        name="{{ '供应商' | translate }}"
        prop="producerName"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [width]="220"
        name="{{ 'SIM卡号' | translate }}"
        prop="simNo"
        headerClass="text-left"
        cellClass="text-left"
      ></ngx-datatable-column>
      <ngx-datatable-column
        [frozenRight]="true"
        [width]="200"
        name="{{ '操作' | translate }}"
        prop="total"
        headerClass="text-center"
        cellClass="text-center"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span appApplyPermission="device_detail" class="margin_right" (click)="checkDetail(row)">
            <a>
              <img
                src="/assets/font/detail.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '详情' | translate }}"
              />
            </a>
          </span>
          <span class="margin_right" appApplyPermission="device_detail">|</span>

          <span appApplyPermission="device_edit" class="margin_right" (click)="editByModal(row)">
            <a>
              <img
                src="/assets/font/edit.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '编辑' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="device_edit" class="margin_right">|</span>

          <span appApplyPermission="device_commands" class="margin_right" (click)="commandsModal(row)">
            <a>
              <img
                src="/assets/font/commands.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '指令发送' | translate }}"
              />
            </a>
          </span>
          <span appApplyPermission="device_commands" class="margin_right">|</span>

          <span class="margin_right" [appApplyPermission]="'device_delete'" (click)="delete(row)">
            <a>
              <img
                src="/assets/font/delete.png"
                nz-tooltip
                nzTooltipPlacement="top"
                nzTooltipTitle="{{ '删除' | translate }}"
              />
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [frozenRight]="true" [width]="50" headerClass="datatable-header-cell-acitons text-left">
        <ng-template let-column="column" ngx-datatable-header-template>
          <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
    <br />
    <div class="footer-style">
      <nopage-datatable-footer
        #footer
        [currentNumber]="currentNumber"
        [totalNumber]="totalNumber"
        (getTotal)="getTotal()"
        [checkTurnPage]="turnPage.bind(this)"
      ></nopage-datatable-footer>
    </div>
  </div>
</div>
