@import "../../../../styles/themes/theme-value";

.interval {
  width: 80%;
  padding-top: 20px;
}

.sensors {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  text-decoration: none;

  i {
    width: 24px;
    padding: 2px;
  }
}

.travel {
  color: $system-color;
  border: 1px solid;
  padding: 1px 7px 1px 7px;
  margin-right: 5px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 400;
}

.statu {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  display: inline-block;
}

.footer-style {
  display: flex;
  height: 50px;
  position: static !important;
  justify-content: flex-end;
}

.overdue-icon {
  margin-top: -4px;
  width: 15px;
}

:host ::ng-deep {
  .dropmenu {
    display: none !important;
  }

  .col-6 {
    position: static;
  }

  .float-right {
    position: absolute;
    right: -20%;
    top: -50px;
  }

  .m-portlet__head {
    display: flex !important;
    flex-direction: row-reverse;
    align-content: center;
  }
}

.no-data {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
