import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { finalize } from 'rxjs/operators';

import { BsModalService } from 'ngx-bootstrap/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { max } from 'lodash';
import { TranslateService } from '@ngx-translate/core';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';

import { environment } from '@env/environment';
import { LoggerFactory, Logger, Dialogs } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';
import { Language } from '@app/account-settings/models/account';
import { accountSetting } from '@app/account-settings/models/account-setting';

import { Device, SensorConfig, sensorTypeInfo, DeviceStatus, deviceStatusInfo } from '../../shared/models/device';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { DeviceService } from '../../shared/device.service';

import { DeviceCreatComponent } from '../device-creat/device-creat.component';
import { EditComponent } from '../edit/edit.component';
import { CommandsComponent } from '../commands/commands.component';
import { ImportFileComponent } from '@app/shared';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { AccountService } from '@app/account-settings/services/account.service';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-device-index',
  templateUrl: './device-index.component.html',
  styleUrls: ['./device-index.component.scss']
})
export class DeviceIndexComponent implements OnInit, AfterViewInit {
  log: Logger;
  translates = {
    设备列表: '',
    设备信息: '',
    '导出失败:': '',
    导出数量超过2000条: '',
    只能选择3个标签进行查询: '',
    '真的要删除吗？': '',
    删除成功: '',
    删除失败: '',
    '逻辑删除失败,请先解绑车辆': ''
  };

  mode: QueryMode = QueryMode.plainCollapse;
  sensorTypeInfo = sensorTypeInfo;
  DeviceStatus = DeviceStatus;
  deviceStatusInfo = deviceStatusInfo;
  loading = false;

  deviceList: Array<Device>;
  deviceListed: Array<Device> = [];
  totalNumber: number;
  currentNumber: number;
  installTime: string;
  isShowCollapse = false;

  listOfOption: any[];
  nodes: Array<any> = [];
  labelIds: any[];
  originTemplate: any;
  treeLoading = true;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [
          { field: 'orgId', op: 'eq' },
          { field: 'deviceNo', op: 'eq' },
          { field: 'labelIds', op: 'eq' },
          { field: 'deviceTypeId', op: 'eq' },
          { field: 'producerName', op: 'eq' },
          { field: 'simNo', op: 'eq' },
          { field: 'status', op: 'eq' },
          { field: 'hasBinding', op: 'eq' },
          { field: 'wireless', op: 'eq' }
        ],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  accountSetting = accountSetting;
  statusList: Array<any> = [
    { key: undefined, name: '全部' },
    { key: 0, name: '未启用' },
    { key: 1, name: '离线' },
    { key: 2, name: '在线' }
  ];

  wirelessList: Array<any> = [
    { key: undefined, name: '全部' },
    { key: 1, name: '无线' },
    { key: 0, name: '有线' }
  ];

  yesList: Array<any> = [
    { key: undefined, name: '全部' },
    { key: 1, name: '是' },
    { key: 0, name: '否' }
  ];

  previousPage = 0; // 上一页
  nextPage = 0; // 下一页

  deviceTypeList: Array<any> = [];

  constructor(
    private dialogs: Dialogs,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef,
    private deviceService: DeviceService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private router: Router,
    private message: NzMessageService,
    private accountService: AccountService
  ) {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
      this.log = this.loggerFactory.getLogger(this.translates['设备列表']);
    });
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
    this.checkFilters(); // 回显过滤条件
  }

  ngOnInit() {
    this.getDeviceTypeList();
    this.getLabels();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  changeTranslatesLang() {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
    });
  }
  // 获取标签
  getLabels() {
    this.deviceService.getLabelList().subscribe((res) => {
      if (res.success) {
        this.listOfOption = res.data;
      }
    });
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => {
        if (organizationId) {
          // 超管获取子级
          resolve(organNodes[0].children);
        } else {
          // 非超管直接获取子级组织机构树
          this.nodes = organNodes;
        }
        this.treeLoading = false;
      });
    });
  }

  getDeviceTypeList() {
    this.deviceService.getDeviceType().subscribe((res) => {
      if (res.success) {
        this.deviceTypeList = res.data;
      }
    });
  }

  onSelect(event: any) {
    this.event = event;
    if (event !== void 0 && event.selected !== void 0) {
      this.deviceListed = event.selected;
    }
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      map[key] = rule.data;
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    this.labelIds = map.labelIds;
    return map;
  }

  refreshData() {
    this.loadDevices(this.event);
  }

  loadDevices(event: any) {
    this.event = event;
    const page = event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.footer.showTotalElements = true;
    this.deviceListed.length = 0;
    console.log(params);
    this.deviceService
      .getDeviceList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        if (res.data.records && res.data.records.length !== 0) {
          let maxOrgname = 0,
            maxIabel = 0,
            maxProducerName = 0;

          res.data.records.forEach((item: any) => {
            maxOrgname = item.orgName.length > maxOrgname ? item.orgName.length : maxOrgname;
            maxProducerName =
              item.producerName && item.producerName.length > maxProducerName
                ? item.producerName.length
                : maxProducerName;
            if (item.labelList) {
              let labelNum = 0;
              item.labelList.forEach((ctem: { labelName: string | any[] }) => {
                labelNum = ctem.labelName.length + labelNum;
                console.log(maxIabel);
              });
              maxIabel = max([maxIabel, labelNum]);
            }

            // 去掉重复sensor类型
            let sensorConfig: SensorConfig[] = [];
            item.sensorConfig
              ?.filter((sensor: SensorConfig) => sensorTypeInfo[Number(sensor.type)])
              .forEach((sensor: SensorConfig) => {
                const isExits = sensorConfig.some((item) => item.type === Number(sensor.type));
                sensorConfig = isExits
                  ? [...sensorConfig]
                  : [...sensorConfig, { ...sensor, type: Number(sensor.type) }];
              });
            item.sensorConfig = sensorConfig;
          });
          maxOrgname = Math.max.apply(null, [14 * maxOrgname + 34, 100]);
          maxProducerName = Math.max.apply(null, [14 * maxProducerName + 34, 100]);
          maxIabel = Math.max.apply(null, [16 * maxIabel + 34, 100]);
          this.table.headerComponent.onColumnResized(maxOrgname, this.table.headerComponent.columns[0]);
          this.table.headerComponent.onColumnResized(maxProducerName, this.table.headerComponent.columns[7]);
          this.table.headerComponent.onColumnResized(maxIabel, this.table.headerComponent.columns[2]);
        }
        this.deviceList = res.data.records;
        this.currentNumber = this.deviceList ? this.deviceList.length : 0;
      });
  }

  delete(row: any) {
    this.changeTranslatesLang();
    this.dialogs.confirm(this.translates['真的要删除吗？']).subscribe(() => {
      this.deviceService.deleteDevice(row).subscribe(
        (res) => {
          if (!res || !res.success) {
            this.log.error(this.translates[res.message] || this.translates['删除失败']);
            return;
          }
          this.log.success(this.translates['删除成功']);
          this.ngxQuery.executeQuery();
        },
        () => this.log.error(this.translates['删除失败'])
      );
    });
  }

  // 编辑
  editByModal(row: any) {
    const initialState = { data: row.id };
    this.modalService.show(EditComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: Device) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 指令发送
  commandsModal(row: any) {
    const initialState = { data: row };
    this.modalService.show(CommandsComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: Device) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // 查看详情
  checkDetail(row: any) {
    sessionStorage.setItem('deviceManagement', JSON.stringify(this.event.query.query));
    this.router.navigate(['/deviceManagement/' + row.id]);
  }

  // 返回 保留筛选条件
  checkFilters() {
    const arr = environment.prePath.split('&');
    const prePage = arr[arr.length - 1];
    if (prePage && arr[1].search('deviceManagement') !== -1) {
      const template = sessionStorage.getItem('deviceManagement');
      const obj = JSON.parse(template);
      if (obj) {
        this.queryTemplates[0].template = obj;
      }
    } else {
      environment.prePath = '';
    }
    sessionStorage.removeItem('deviceManagement');
  }

  // 重置查询模板
  reset($event: any) {
    this.queryTemplates = this.originTemplate;
  }

  // 新建
  creatByModal() {
    this.modalService.show(DeviceCreatComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom modal-display-table'
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  toggleExpandRow(row: Device) {
    this.datatable.rowDetail.toggleExpandRow(row);
  }

  getTotal() {
    const page = this.event.page;
    this.loading = true;
    const params: any = this.loadProperty(page);
    this.deviceService
      .getTotalNumber(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        if (res.success) {
          this.totalNumber = res.data;
        }
      });
  }

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  field1Changed(rules: Array<any>, val: string) {
    const field2Value: string = val === 'item 3' ? val + '-1234' : '';
    for (const rule of rules) {
      if (rule.field.name === 'field2') {
        rule.datas[0] = field2Value;
      }
    }
  }

  // 导入
  importArchiveBorrow() {
    // const lang = Language[accountSetting.language];
    const lang = window.localStorage.getItem('lang');
    let language = '';
    if (lang === 'zh-CN') {
      language = 'cn';
    }
    if (lang === 'en-US') {
      language = 'en';
    }
    if (lang === 'es-MX') {
      language = 'esp';
    }
    const initialState = {
      tplUrl: `glcrm-vehicle-api/v1/api/device/template/${language}`,
      // uploadUrl: `glcrm-vehicle-api/v1/api/device/importDevice/${language}`
      uploadUrl: `glcrm-vehicle-api/v1/api/device/importDevice`
    };
    this.modalService.show(ImportFileComponent, {
      ignoreBackdropClick: true,
      class: 'modal-lg-custom',
      initialState
    });
    const onHidden = this.modalService.onHidden.subscribe((params: any) => {
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  exportReport() {
    this.changeTranslatesLang();
    const curDate: any = new Date();
    const param = this.loadProperty(this.event.page);

    this.deviceService
      .exportReport(param)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (res) => {
          const link = document.createElement('a');
          const blob = new Blob([res.body], { type: 'application/xlsx' }),
            doloadUrl = window.URL.createObjectURL(blob);
          link.setAttribute('href', doloadUrl);
          // tslint:disable-next-line: max-line-length
          link.setAttribute(
            'download',
            this.translates['设备信息'] +
              curDate.getFullYear() +
              (curDate.getMonth() + 1) +
              curDate.getDate() +
              curDate.getHours() +
              curDate.getMinutes() +
              curDate.getSeconds() +
              '.xlsx'
          );
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        (error) => {
          if (error === 'OK') {
            // 此时说明是因为超过2000条失败
            this.log.error(this.translates['导出失败:'], this.translates['导出数量超过2000条']);
          }
        }
      );
  }

  // 折叠展开工具栏
  showCollapse() {
    this.isShowCollapse = !this.isShowCollapse;
    this.ngxQuery.showCollapse(this.isShowCollapse);
  }

  selectMax(e: any) {
    this.changeTranslatesLang();
    if (e.length > 3) {
      this.labelIds.pop();
      this.message.create('warning', this.translates['只能选择3个标签进行查询']);
    }
  }
}
