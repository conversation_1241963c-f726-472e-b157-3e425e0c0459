@import "../../../../styles/themes/theme-value";

:host {
  display: block;
  width: 835px;
}

.modal-body {
  height: 590px;
  max-height: calc(100vh - 120px) !important;
  padding: 30px 80px 0 !important;
  overflow-y: auto;
}

.cameras {
  // margin-left: 55px;
  padding-top: 10px;
}

.camera {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  position: relative;
  margin-bottom: 8px;

  &:first-child {
    &::before {
      content: " ";
      position: absolute;
      left: 64px;
      top: -7px;
      border-bottom: 10px solid #f8fafc;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  &-left {
    width: 456px;
    margin-right: 5px;
    padding: 8px 16px;
    border-radius: 4px;
    background-color: #f8fafc;

    .camera-name {
      padding-right: 45px;
    }
  }

  &-right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 40px;
    padding: 12px;
    border-radius: 4px;
    background-color: #f8fafc;

    i {
      font-size: 18px;
      cursor: pointer;
      color: $system-color;
    }

    .del {
      cursor: pointer;
    }
  }
}

:host ::ng-deep {
  .ant-form-item-label {
    text-align: left;
  }

  .has-error {
    position: absolute;
    bottom: -21px;

    .ant-form-item-explain {
      color: #ff4d4f;
    }
  }

  .camera {
    .ant-form-item {
      margin-bottom: 12px;
    }

    .ant-input-suffix {
      color: #c6ccdd;
    }
  }
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}

.no-data {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}
