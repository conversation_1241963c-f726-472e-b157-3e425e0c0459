import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ChangeDetectionStrategy
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { Observable, of } from 'rxjs';
import { catchError, filter, finalize, map, switchMap } from 'rxjs/operators';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { CustomValidators } from 'ngx-custom-validators';
import { NzSelectOptionInterface } from 'ng-zorro-antd/select';

import { Logger, LoggerFactory } from '@app/core';
import { Camera, CameraType } from '@app/shared/models/video';
import { SensorType, SensorConfig, sensorTypeInfo } from '../../shared/models/device';

import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { DeviceService } from '../../shared/device.service';
import { AccountService } from '@app/account-settings/services/account.service';
import { NzFormatEmitEvent } from 'ng-zorro-antd/tree';

@Component({
  selector: 'app-device-ui',
  templateUrl: './device-ui.component.html',
  styleUrls: ['./device-ui.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceUiComponent implements OnInit, OnChanges {
  @Input() title: string = '新建';
  @Input() device: any;
  // 有orgId表明从编辑车辆新增设备调用
  @Input()
  get orgId(): string {
    return this._orgId;
  }
  set orgId(orgId: string) {
    if (orgId && this.form) {
      this.form.patchValue({ orgId });
    }
    this._orgId = orgId;
  }
  @Output() save = new EventEmitter<any>();

  log: Logger;
  translates = {
    新增成功: '',
    通道号: ''
  };
  saving = false;
  form: FormGroup;
  treeLoading = true;

  nodes: Array<any> = []; // 组织机构
  labelList: any[] = []; // 标签列表
  deviceTypeList: Array<any> = [];
  channelList: Array<any> = [];
  SensorType = SensorType;
  sensorTypeInfo = sensorTypeInfo;
  sensorTypeRule: any = {};

  sensorTypeList = [
    sensorTypeInfo[SensorType['Oil rod fuel consumption sensor']],
    sensorTypeInfo[SensorType['Ultrasonic Fuel Consumption Sensor']],
    sensorTypeInfo[SensorType['Voltage fuel consumption sensor']],
    sensorTypeInfo[SensorType['Temperature']],
    sensorTypeInfo[SensorType['Humidity']],
    sensorTypeInfo[SensorType['Temperature&Humidity']],
    sensorTypeInfo[SensorType['Door Open']]
  ];

  _orgId: string = '';

  get labelIds(): FormControl {
    return this.form.get('labelIds') as FormControl;
  }

  get wireless(): FormControl {
    return this.form.get('wireless') as FormControl;
  }

  get isCamera(): FormControl {
    return this.form.get('isCamera') as FormControl;
  }

  get isObd(): FormControl {
    return this.form.get('isObd') as FormControl;
  }

  get cameraConfig(): FormArray {
    return this.form.get('cameraConfig') as FormArray;
  }

  get isSensor(): FormControl {
    return this.form.get('isSensor') as FormControl;
  }

  get sensorConfig(): FormArray {
    return this.form.get('sensorConfig') as FormArray;
  }

  get disabledChannel(): number[] {
    return this.cameraConfig.value.filter((item: any) => !!item.channel).map((item: any) => item.channel);
  }

  // 标签不超过三个
  confirmationValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return null;
    }
    if (control.value.length > 3) {
      return { confirm: true, error: true };
    }
    return null;
  };

  /**
   * 选择摄像头时至少有一个摄像头
   * @param control
   * @returns
   */
  minHasOneCamera: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    if (!this.form) {
      return null;
    }
    if (this.wireless.value === '1') {
      return null;
    }
    if (this.isCamera.value === '0') {
      return null;
    }
    if (this.disabledChannel.length === 0) {
      return { minHasOneCamera: true };
    }
    if (this.cameraConfig.invalid) {
      return { minHasOneCamera: true };
    }
    return null;
  };

  /**
   * 选择传感器时至少有一个
   * @param control
   * @returns
   */
  minHasOneSensor: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    if (!this.form) {
      return null;
    }
    if (this.isSensor.value === '0') {
      return null;
    }
    if (this.sensorConfig.invalid) {
      return { minHasOneSensor: true };
    }
    return null;
  };

  constructor(
    public activeModal: BsModalRef,
    private cd: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private deviceService: DeviceService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService,
    private referenceDataService: ReferenceDataService,
    private translate: TranslateService,
    private accountService: AccountService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
      this.channelList = Array.from({ length: 16 }).map((item: any, i: number) => ({
        label: `${this.translates['通道号']}${i + 1}`,
        value: (i + 1).toString()
      }));
    });
    this.buildForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const edit = changes.device ? changes.device.currentValue : null;
    if (this.title === '编辑' && edit) {
      this.form.patchValue({
        orgId: this.device.orgId,
        deviceTypeId: this.device.deviceTypeId,
        simNo: this.device.simNo,
        producerName: this.device.producerName
      });
      this.form.get('deviceNo').reset({ value: this.device.deviceNo, disabled: true });
      this.labelIds.setValue(this.device.labelIds, { emitViewToModelChange: false });
      this.wireless.setValue(this.device.wireless, { emitViewToModelChange: false });
      this.isObd.setValue(this.device.isObd.toString(), { emitViewToModelChange: false });
      this.isCamera.setValue(this.device.isCamera, { emitViewToModelChange: false });
      this.device.cameraConfig = this.device.isCamera === '0' ? [] : this.device.cameraConfig || [];
      this.device.cameraConfig.forEach((item: Camera) => this.addCamera(item));
      this.isSensor.setValue(this.device.isSensor, { emitViewToModelChange: false });
      this.device.sensorConfig = this.device.isSensor === '0' ? [] : this.device.sensorConfig || [];
      this.device.sensorConfig?.forEach((item: SensorConfig) => {
        // 如果是油杆 超声波 油压就调用获取规则列表 根据当前传感器类型
        if (
          item.type === SensorType['Oil rod fuel consumption sensor'] ||
          item.type === SensorType['Ultrasonic Fuel Consumption Sensor'] ||
          item.type === SensorType['Voltage fuel consumption sensor']
        ) {
          this.getRules(item.type).subscribe((list: any) => {
            this.sensorTypeRule[item.type].ruleList = list;
            this.addSensor(item);
          });
        } else {
          this.addSensor(item);
        }
      });
    }
  }

  ngOnInit() {
    this.sensorTypeInfo = sensorTypeInfo;
    this.getDeviceTypeList();
    this.getLabels().subscribe();
    // this.getUserAllOrganizations();
    this.getUserInfo();
  }

  getUserInfo() {
    this.accountService.syncAccountInfo().subscribe((res: any) => {
      const data = res.data;
      if (data.isAdmin) {
        // 登录账号是超管，先根据分类id获取所有一级，再根据一级机构id获取子级
        this.getFirstLevel();
      } else {
        // 当登录账号不是超管，采用之前的逻辑
        this.getUserAllOrganizations();
      }
    });
  }

  getFirstLevel() {
    this.treeLoading = true;
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.nodes = res;
      this.treeLoading = false;
    });
  }

  changeWireless() {
    this.isCamera.setValue('0');
    this.isObd.setValue('0');
  }

  changeIsCamera() {
    for (let i = this.cameraConfig.controls.length - 1; i >= 0; i--) {
      this.delCamera(i);
    }
    if (this.isCamera.value === '1' && this.wireless.value === '0') {
      this.addCamera();
    }
  }

  getCameraNameLength(formGroup: FormGroup) {
    return formGroup.get('camera').value.trim().length;
  }

  changeIsSensor() {
    for (let i = this.sensorConfig.controls.length - 1; i >= 0; i--) {
      this.delSensor(i);
    }
    if (this.isSensor.value === '1') {
      this.addSensor();
    }
  }

  changeSensorType(type: SensorType, sensorFormControl: FormControl) {
    let lineNo = null;
    let disabledLineNo = false;
    switch (type) {
      // 电压油耗传感器只有一个路数,默认选中并禁止
      case SensorType['Voltage fuel consumption sensor']:
        lineNo = sensorTypeInfo[type].lineNoList[0].value;
        disabledLineNo = true;
        this.getRules(type).subscribe((list: any) => {
          this.sensorTypeRule[type].ruleList = list;
        });
        break;
      case SensorType['Oil rod fuel consumption sensor']:
        this.getRules(type).subscribe((list: any) => {
          this.sensorTypeRule[type].ruleList = list;
        });
        break;
      case SensorType['Ultrasonic Fuel Consumption Sensor']:
        this.getRules(type).subscribe((list: any) => {
          this.sensorTypeRule[type].ruleList = list;
        });
        break;
      default:
        break;
    }
    const lineNoControl = sensorFormControl.get('lineNo');
    lineNoControl.reset({ value: lineNo, disabled: disabledLineNo });
    const currSensorTypeInfo = sensorTypeInfo[type];
    if (currSensorTypeInfo) {
      lineNoControl.setValidators(currSensorTypeInfo.lineNoRequired ? [Validators.required] : null);
    }
    lineNoControl.markAsDirty();
    lineNoControl.updateValueAndValidity();

    const ruleConfigIdControl = sensorFormControl.get('ruleConfigId');
    ruleConfigIdControl.reset(null);
    ruleConfigIdControl.markAsDirty();
    ruleConfigIdControl.updateValueAndValidity();
  }

  getRules(type: any): Observable<any> {
    if (this.sensorTypeRule[type] && this.sensorTypeRule[type].ruleList.length) {
      return of(this.sensorTypeRule[type].ruleList);
    } else {
      this.sensorTypeRule[type] = {
        ruleList: []
      };
      return this.deviceService
        .getRuleList({
          ruleTypeId: type,
          pageIndex: 1,
          pageSize: 999
        })
        .pipe(
          map((res) => {
            if (!res || !res.success) {
              return [];
            }
            return Array.isArray(res.data.records) ? res.data.records : [];
          }),
          catchError(() => {
            return of([]);
          }),
          finalize(() => {
            this.cd.markForCheck();
          })
        );
    }
  }

  submit() {
    const cameraConfig = this.form.get('cameraConfig') as FormArray;
    const sensorConfig = this.form.get('sensorConfig') as FormArray;
    [this.form, ...cameraConfig.controls, ...sensorConfig.controls].forEach((formGroup: FormGroup) => {
      for (const i in formGroup.controls) {
        if (formGroup.controls[i]) {
          formGroup.controls[i].markAsDirty();
          formGroup.controls[i].updateValueAndValidity();
        }
      }
    });
    if (this.form.invalid) {
      return;
    }
    const params = this.form.getRawValue();
    params.id = this.device ? this.device.id : null;
    params.simNo = params.simNo || '';
    params.cameraConfig = params.isCamera === '0' ? [] : params.cameraConfig.filter((item: Camera) => !!item.channel);
    params.sensorConfig = params.isSensor === '0' ? [] : params.sensorConfig;
    // 设备新增/编辑
    if (!this.orgId) {
      this.save.emit(params);
      return;
    }
    // 禁止新增车辆设备
    if (this.saving) {
      return;
    }
    // 编辑车辆设备
    this.saving = true;
    this.deviceService
      .creatDevice(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe((res) => {
        if (!res.success) {
          this.translate.get(res.message).subscribe((res: string) => {
            this.log.error(res);
          });
          return;
        }
        this.activeModal.hide();
      });
  }

  buildForm() {
    this.form = this.formBuilder.group(
      {
        orgId: [null, [Validators.required]],
        labelIds: [null, [this.confirmationValidator]],
        deviceTypeId: [null, [Validators.required]],
        deviceNo: [null, [Validators.required, CustomValidators.number]],
        simNo: [null, [Validators.required, CustomValidators.number]],
        producerName: [null, [Validators.required]],
        wireless: ['0'], // 是否无线:0 否(有线设备);1 是(无线设备:没有摄像头)
        isObd: ['0'], // 是否是OBD设备:0 否;1 是
        isCamera: ['0'], // 是否有摄像头(0 否；1 是)
        cameraConfig: this.formBuilder.array([]),
        isSensor: ['0'], // 是否有传感器(0 否；1 是),
        sensorConfig: this.formBuilder.array([])
      },
      { validators: [this.minHasOneCamera, this.minHasOneSensor] }
    );

    // 模糊过滤不存在时,添加当前模糊搜索的内容为标签
    this.labelIds.valueChanges
      .pipe(
        filter((labelIds) => Array.isArray(labelIds)),
        map((labelIds) =>
          labelIds.filter((labelId: number | string) => typeof labelId === 'string' && !!labelId.trim())
        ),
        filter((labelNames) => labelNames.length === 1),
        switchMap((labelNames: string[]) => {
          let labelName = '';
          return this.deviceService.addLabelList(labelNames[0].trim()).pipe(
            map((res) => {
              if (res.code === '200') {
                this.log.success(this.translates['新增成功']);
                labelName = labelNames[0].trim();
              }
              return labelName;
            }),
            catchError(() => of(labelName))
          );
        }),
        switchMap((labelName) => {
          if (!labelName) {
            return of(labelName);
          }
          return this.getLabels().pipe(
            map(() => labelName),
            catchError(() => of(labelName))
          );
        }),
        finalize(() => {
          this.cd.markForCheck();
        })
      )
      .subscribe((labelName) => {
        let labelIds = this.labelIds.value;
        labelIds.pop();
        if (labelName) {
          const addItem = this.labelList.find((item) => item.labelName === labelName);
          if (addItem) {
            labelIds.push(addItem.id);
          }
        }
        this.labelIds.setValue(labelIds);
      });
  }

  addCamera(item?: Camera) {
    if (this.cameraConfig.length >= 16) {
      return;
    }
    const camera = this.formBuilder.group({
      camera: [item ? item.camera : '', [Validators.required]],
      type: [item ? item.type : CameraType.Video, [Validators.required]],
      channel: [item ? item.channel : null, [Validators.required]]
    });
    this.cameraConfig.push(camera);
  }

  delCamera(index: number) {
    this.cameraConfig.removeAt(index);
  }

  addSensor(item?: SensorConfig) {
    let disabledLineNo = false;
    if (item) {
      switch (Number(item.type)) {
        // 电压油耗传感器只有一个路数,默认选中并禁止
        case SensorType['Voltage fuel consumption sensor']:
          disabledLineNo = true;
          break;
        default:
          break;
      }
    }

    // 判断当前规则是不是再规则列表里-当前组织只可以查看本组织的规则 存在下级组织有规则A 但上级组织没有该规则的情况时清空规则值
    let ruleConfigId = item ? item.ruleConfigId : null;
    if (
      item &&
      this.sensorTypeRule[item.type] &&
      this.sensorTypeRule[item.type].ruleList.length &&
      item?.ruleConfigId
    ) {
      const haveRule = this.sensorTypeRule[item.type].ruleList.find((m: any) => {
        return m.id === item.ruleConfigId;
      });
      ruleConfigId = haveRule ? ruleConfigId : null;
    }
    const sensor = this.formBuilder.group({
      type: [item ? Number(item.type) : '', [Validators.required]],
      lineNo: [
        { value: item ? Number(item.lineNo) : null, disabled: disabledLineNo },
        item && sensorTypeInfo[Number(item.type)].lineNoRequired ? [Validators.required] : null
      ],
      site: [
        item ? item.site : null,
        item && sensorTypeInfo[Number(item.type)].siteRequired ? [Validators.required] : null
      ],
      ruleConfigId: [ruleConfigId, null]
    });
    this.sensorConfig.push(sensor);
  }

  delSensor(index: number) {
    this.sensorConfig.removeAt(index);
  }

  hideSensorPort(type: SensorType, lineNo: string) {
    const sensorConfig: SensorConfig[] = this.sensorConfig.value || [];
    return sensorConfig.some((sensor) => sensor.type === type && sensor.lineNo === lineNo);
  }

  /**
   * 获取对应SensorType路数列表
   * @param type
   * @returns
   */
  getLineNoList(type: SensorType): NzSelectOptionInterface[] {
    return this.sensorTypeInfo[type]?.lineNoList || [];
  }

  /**
   * 禁用SensorType,如果对应SensorType路数都选择了
   */
  disabledSensorType(type: SensorType) {
    const sensorConfig: SensorConfig[] = this.sensorConfig.getRawValue() || [];
    const selectedLineNoList = sensorConfig.filter((sensor) => sensor.type === type && !!sensor.lineNo);
    const lineNoList = this.getLineNoList(type);
    return selectedLineNoList.length >= lineNoList.length;
  }

  /**
   * 获取对应SensorType已选择的路数
   */
  hideSensorTypeLineNo(type: SensorType, lineNo: string) {
    const sensorConfig: SensorConfig[] = this.sensorConfig.value || [];
    return sensorConfig.some((sensor) => sensor.type === type && sensor.lineNo === lineNo);
  }

  // 获取标签
  getLabels() {
    return this.deviceService.getLabelList().pipe(
      map((res) => {
        if (res.success) {
          this.labelList = Array.isArray(res.data) ? res.data : this.labelList;
        }
        return null;
      }),
      finalize(() => this.cd.markForCheck())
    );
  }

  expandChange(e: NzFormatEmitEvent): void {
    const node = e.node;
    if (node && node.getChildren().length === 0) {
      this.getUserAllOrganizations(node.origin.id).then((data: any) => {
        node.addChildren(data);
      });
    }
  }

  // 获取机构
  getUserAllOrganizations(organizationId?: any) {
    return new Promise((resolve) => {
      this.treeLoading = true;
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      // organizationId有值，说明是超管账号，通过organizationId获取子级
      // organizationId无值，说明是非超管账号，使用原有逻辑参数
      const id = organizationId ? organizationId : userInfo.departmentId;
      this.referenceDataService
        .getUserChildrenOrganizations(id)
        .pipe(finalize(() => this.cd.markForCheck()))
        .subscribe((organNodes) => {
          if (organizationId) {
            // 超管获取子级
            resolve(organNodes[0].children);
          } else {
            // 非超管直接获取子级组织机构树
            this.nodes = organNodes;
          }
          this.treeLoading = false;
        });
    });
  }

  // 获取设备
  getDeviceTypeList() {
    this.deviceService
      .getDeviceType()
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.deviceTypeList = res.data;
        }
      });
  }
}
