<div class="modal-header">
  <h5 class="modal-title" translate>{{ title }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="orgId">
        {{ '所属机构' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="orgIdErrorTpl">
        <nz-tree-select
          formControlName="orgId"
          id="orgId"
          [nzDisabled]="title === '编辑' || !!orgId"
          [nzNodes]="nodes"
          [nzAllowClear]="title === '编辑' || !!orgId"
          nzShowSearch
          nzShowLine
          nzPlaceHolder="{{ '请选择所属机构' | translate }}"
          [nzAsyncData]="true"
          [nzNotFoundContent]="noData"
          (nzExpandChange)="expandChange($event)"
        ></nz-tree-select>
        <ng-template #noData>
          <div *ngIf="treeLoading" class="no-data">
            <nz-spin nzSimple></nz-spin>
          </div>
          <div *ngIf="!treeLoading" class="no-data">
            {{ '暂无数据' | translate }}
          </div>
        </ng-template>
        <ng-template #orgIdErrorTpl let-control>
          {{ '请选择所属机构' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="labelIds">
        &nbsp;&nbsp;&nbsp;&nbsp;{{ '标签' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="labelIdsErrorTpl">
        <nz-select
          formControlName="labelIds"
          id="labelIds"
          nzMode="tags"
          nzPlaceHolder="{{ '请选择标签，最多不超过三个' | translate }}"
        >
          <nz-option *ngFor="let option of labelList" [nzLabel]="option.labelName" [nzValue]="option.id"></nz-option>
        </nz-select>
        <ng-template #labelIdsErrorTpl let-control>
          {{ '最多可选择3个标签，请重新选择' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="producerName">
        {{ '供应商' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="producerNameErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="100"
          formControlName="producerName"
          id="producerName"
          placeholder="{{ '请输入供应商名称' | translate }}"
        />
        <ng-template #producerNameErrorTpl let-control>
          {{ '请输入供应商' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="simNo">
        {{ 'SIM卡号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="simNoErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="50"
          formControlName="simNo"
          id="simNo"
          placeholder="{{ '请输入SIM卡号' | translate }}"
        />
        <ng-template #simNoErrorTpl let-control>
          {{ '请输入正确的SIM卡号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="deviceNo" translate>
        {{ '设备号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="deviceNoErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="17"
          formControlName="deviceNo"
          id="deviceNo"
          placeholder="{{ '请输入设备号' | translate }}"
        />
        <ng-template #deviceNoErrorTpl let-control>
          {{ '请输入正确的设备号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="deviceTypeId">
        {{ '设备型号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="deviceTypeIdErrorTpl">
        <nz-select
          formControlName="deviceTypeId"
          id="deviceTypeId"
          nzPlaceHolder="{{ '请选择设备型号' | translate }}"
          nzShowSearch
        >
          <nz-option
            *ngFor="let option of deviceTypeList"
            [nzLabel]="option.typeName"
            [nzValue]="option.id"
          ></nz-option>
        </nz-select>
        <ng-template #deviceTypeIdErrorTpl let-control>
          {{ '请选择设备型号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="radio">
        {{ '设备类型' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <nz-radio-group formControlName="wireless" (ngModelChange)="changeWireless()">
          <label nz-radio nzValue="0">{{ '有线' | translate }}</label>
          <label nz-radio nzValue="1">{{ '无线' | translate }}</label>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="radio">
        {{ 'OBD' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <nz-radio-group formControlName="isObd">
          <label nz-radio nzValue="0">{{ '否' | translate }}</label>
          <label nz-radio nzValue="1">{{ '是' | translate }}</label>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item [ngStyle]="{ display: wireless.value === '0' ? 'flex' : 'none' }">
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>
        {{ '摄像头' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <nz-radio-group id="isCamera" formControlName="isCamera" (ngModelChange)="changeIsCamera()">
          <label nz-radio nzValue="0">{{ '否' | translate }}</label>
          <label nz-radio nzValue="1">{{ '是' | translate }}</label>
        </nz-radio-group>
        <div class="cameras">
          <ng-container formArrayName="cameraConfig">
            <div class="camera" *ngFor="let item of cameraConfig.controls; let i = index" [formGroupName]="i">
              <div class="camera-left">
                <nz-form-item>
                  <nz-form-label [nzSpan]="6" nzRequired>{{ '摄像头' | translate }}{{ i + 1 }}</nz-form-label>
                  <nz-form-control [nzSpan]="18">
                    <nz-input-group [nzSuffix]="suffixTemplate">
                      <input
                        type="text"
                        nz-input
                        formControlName="camera"
                        maxlength="10"
                        zr-trim
                        placeholder="{{ '摄像头名称' | translate }}"
                      />
                    </nz-input-group>
                    <ng-template #suffixTemplate>{{ getCameraNameLength(item) }}/10</ng-template>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label [nzSpan]="6" nzRequired>{{ '资源类型' | translate }}</nz-form-label>
                  <nz-form-control [nzSpan]="18">
                    <nz-radio-group formControlName="type">
                      <label nz-radio nzValue="1">{{ '视频' | translate }}</label>
                      <label nz-radio nzValue="0">{{ '音视频' | translate }}</label>
                    </nz-radio-group>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label [nzSpan]="6" nzRequired>{{ '通道号' | translate }}</nz-form-label>
                  <nz-form-control [nzSpan]="18">
                    <nz-select formControlName="channel" nzPlaceHolder="{{ '请选择通道号' | translate }}">
                      <nz-option
                        *ngFor="let channel of channelList"
                        [nzDisabled]="disabledChannel.includes(channel.value)"
                        [nzValue]="channel.value"
                        [nzLabel]="channel.label"
                      ></nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div class="camera-right">
                <div *ngIf="i === 0">
                  <i nz-icon nzType="plus-circle" nzTheme="outline" (click)="addCamera()"></i>
                </div>
                <div *ngIf="i !== 0">
                  <img src="/assets/media/app/img/video/del.svg" (click)="delCamera(i)" class="del" />
                </div>
              </div>
            </div>
          </ng-container>
        </div>

        <div class="has-error">
          <div class="ant-form-item-explain" *ngIf="form?.dirty && form?.errors && form?.errors.minHasOneCamera">
            {{ '请设置摄像头参数' | translate }}
          </div>
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>
        {{ '传感器' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <nz-radio-group formControlName="isSensor" (ngModelChange)="changeIsSensor()">
          <label nz-radio nzValue="0">{{ '否' | translate }}</label>
          <label nz-radio nzValue="1">{{ '是' | translate }}</label>
        </nz-radio-group>
        <div class="cameras" [ngStyle]="{ display: isSensor.value === '1' ? 'block' : 'none' }">
          <ng-container formArrayName="sensorConfig">
            <div class="camera" *ngFor="let item of sensorConfig.controls; let i = index" [formGroupName]="i">
              <div class="camera-left">
                <nz-form-item>
                  <nz-form-label [nzSpan]="7" nzRequired>{{ '类型' | translate }}</nz-form-label>
                  <nz-form-control [nzSpan]="17">
                    <nz-select
                      formControlName="type"
                      (ngModelChange)="changeSensorType($event, item)"
                      [nzAllowClear]="true"
                      nzPlaceHolder="{{ '请选择类型' | translate }}"
                    >
                      <nz-option
                        *ngFor="let sensor of sensorTypeList"
                        [nzDisabled]="disabledSensorType(sensor.value)"
                        [nzValue]="sensor.value"
                        [nzLabel]="sensor.label | translate"
                      ></nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label
                    [nzSpan]="7"
                    [nzRequired]="!!(item.get('type').value && sensorTypeInfo[item.get('type')?.value].lineNoRequired)"
                  >
                    {{ '路数' | translate }}
                  </nz-form-label>
                  <nz-form-control [nzSpan]="17">
                    <nz-select
                      formControlName="lineNo"
                      [nzAllowClear]="true"
                      nzPlaceHolder="{{ '请选择路数' | translate }}"
                    >
                      <nz-option
                        *ngFor="let lineNo of getLineNoList(item.get('type').value)"
                        [nzHide]="hideSensorTypeLineNo(item.get('type').value, lineNo.value)"
                        [nzValue]="lineNo.value"
                        [nzLabel]="lineNo.label"
                      ></nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item>
                  <nz-form-label
                    [nzSpan]="7"
                    [nzRequired]="!!(item.get('type').value && sensorTypeInfo[item.get('type')?.value].siteRequired)"
                  >
                    {{ '传感器安装位置' | translate }}
                  </nz-form-label>
                  <nz-form-control [nzSpan]="17">
                    <input
                      type="text"
                      nz-input
                      formControlName="site"
                      maxlength="50"
                      zr-trim
                      placeholder="{{ '请输入安装位置' | translate }}"
                    />
                  </nz-form-control>
                </nz-form-item>
                <!-- 2.0.4.1新增规则 -->
                <nz-form-item
                  appApplyPermission="device_rule"
                  *ngIf="
                    item.get('type').value === SensorType['Ultrasonic Fuel Consumption Sensor'] ||
                    item.get('type').value === SensorType['Voltage fuel consumption sensor'] ||
                    item.get('type').value === SensorType['Oil rod fuel consumption sensor']
                  "
                >
                  <nz-form-label [nzSpan]="7">
                    {{ '规则参数' | translate }}
                  </nz-form-label>
                  <nz-form-control [nzSpan]="17">
                    <nz-select
                      formControlName="ruleConfigId"
                      nzAllowClear
                      nzShowSearch
                      nzPlaceHolder="{{ '请选择规则参数' | translate }}"
                    >
                      <nz-option
                        *ngFor="let rule of sensorTypeRule[item.get('type')?.value]?.ruleList"
                        [nzValue]="rule.id"
                        [nzLabel]="rule.ruleName"
                      ></nz-option>
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div class="camera-right">
                <div *ngIf="i === 0">
                  <i nz-icon nzType="plus-circle" nzTheme="outline" (click)="addSensor()"></i>
                </div>
                <div *ngIf="i !== 0">
                  <img src="/assets/media/app/img/video/del.svg" (click)="delSensor(i)" class="del" />
                </div>
              </div>
            </div>
          </ng-container>
        </div>

        <div class="has-error">
          <div class="ant-form-item-explain" *ngIf="form?.dirty && form?.errors && form?.errors.minHasOneSensor">
            {{ '请设置传感器参数' | translate }}
          </div>
        </div>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
