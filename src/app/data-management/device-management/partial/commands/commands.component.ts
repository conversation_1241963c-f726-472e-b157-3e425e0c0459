import { Component, OnInit, OnDestroy, Input, EventEmitter, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';

import { Logger, LoggerFactory } from '@app/core';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { CommandsManagementService } from '@app/commands-management/shared/commands-management.service';
import { DeviceService } from '../../shared/device.service';
import { environment } from '@env/environment';

@Component({
  selector: 'app-commands',
  templateUrl: './commands.component.html',
  styleUrls: ['./commands.component.scss']
})
export class CommandsComponent implements OnInit, OnChanges {
  @Input() data: any;

  listOfOption: any[];
  // 组织机构
  nodes: Array<any> = [];

  title = '新建指令';
  device: any;

  public log: Logger;
  form: FormGroup;
  saving = false;
  deviceTypeList = [
    {
      id: 1,
      commandName: '断油断电'
    },
    {
      id: 2,
      commandName: '恢复油电'
    }
  ];
  wireless = '0';
  popShow: boolean = false;
  btnShow: boolean = false;
  errMsg: string;

  countdown: number = 5;
  isDown: boolean = true;

  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private deviceService: DeviceService,
    private loggerFactory: LoggerFactory,
    public modalService: BsModalService,
    private referenceDataService: ReferenceDataService,
    private commandsManagementService: CommandsManagementService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.buildForm();
  }

  ngOnInit() {
    this.getCommadlist();
    this.getDevice();
    this.getUserAllOrganizations();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const edit = changes.device ? changes.device.currentValue : null;
    if (this.title === '编辑' && edit) {
      this.form.patchValue(this.device);
    }
  }

  getCommadlist() {
    this.commandsManagementService.commandPage({ commandName: null }).subscribe(
      (response) => {
        this.commandsManagementService.sysCommand().subscribe(
          (res) => {
            this.deviceTypeList = [...res.data, ...response.data].map((item) => {
              item.checked = false;
              return item;
            });
          },
          (error) => console.log('系统指令列表获取设备', error)
        );
      },
      (error) => console.log('自定义列表获取设备', error)
    );
  }

  // 获取设备信息
  getDevice() {
    this.deviceService.getDevice(this.data.id).subscribe(
      (respone) => {
        respone.data.labels = [];
        respone.data.labelList.forEach((item: { labelName: any }) => {
          respone.data.labels.push(item.labelName);
        });
        console.log('获取设备信息', respone.data);
        this.device = respone.data;
        if (this.device.vehicle) {
          this.device.plateNumber = this.device.vehicle.plateNumber;
        }
        this.form.patchValue(this.device);
      },
      (error) => console.log(`设备数据获取失败,失败信息: ${error}`)
    );
  }

  // 获取机构
  getUserAllOrganizations() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const id = userInfo.departmentId;
    this.referenceDataService.getUserChildrenOrganizations(id).subscribe((organNodes) => (this.nodes = organNodes));
  }

  // 安装时间
  confirmtime = (control: FormControl): { [s: string]: boolean } => {
    const date = new Date();
    const date1 = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + (date.getDate() + 1);
    if (!control.value) {
      return { required: true };
    } else if (control.value > date1) {
      return { confirm: true, error: true };
    }
    return {};
  };

  submit() {
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    if (this.form.invalid) {
      return;
    }
    this.popShow = true;
  }

  confirm() {
    if (this.saving === true) {
      return;
    }
    this.saving = true;
    let type: number;
    //发送指令类型(1 断油断电；2 恢复油电 3自定义指令 4 解除指令
    if (this.form.value.commandsTypeId.commandName === 'Restore') {
      type = 2;
    } else if (this.form.value.commandsTypeId.commandName === 'FCO') {
      type = 1;
    } else if (this.form.value.commandsTypeId.commandName === environment.commandType.releaseAlarm) {
      type = 4;
    } else {
      type = 3;
    }
    // const type =
    //   this.form.value.commandsTypeId.commandName === 'Restore'
    //     ? 1
    //     : this.form.value.commandsTypeId.commandName === 'FCO'
    //     ? 2
    //     : 3; //发送指令类型(1 断油断电；2 恢复油电 3自定义指令
    const params = {
      imei: this.form.getRawValue().deviceNo,
      channel: 'Web command',
      devtype: '',
      cmdno: '1234', //流水号
      type: type,
      commandId: this.form.value.commandsTypeId.id,
      commandName: this.form.value.commandsTypeId.commandName
    };
    this.deviceService.commandJudgment(params).subscribe(
      (respone) => {
        if (respone.success) {
          type === 3 ? this.userCommandSend() : this.commandSend(params);
        } else {
          this.errMsg = respone.message;
          this.btnShow = true;
          this.saving = false;
        }
      },
      (error) => {
        console.log(`请求失败信息: ${error}`), (this.saving = false);
      }
    );
  }

  // 发送指令
  commandSend(params: any) {
    params.deviceStatus = '';
    this.deviceService
      .commandSend(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (respone) => {
          this.activeModal.hide();
          if (respone.success) {
            this.translate.get('指令发送成功!').subscribe((res: string) => {
              this.log.success(res);
            });
          } else {
            this.translate.get('指令发送失败!').subscribe((res: string) => {
              this.log.error(res);
            });
          }
        },
        (error) => console.log(`请求失败信息: ${error}`)
      );
  }

  // 自定义指令
  userCommandSend() {
    const params = {
      imei: this.form.getRawValue().deviceNo,
      value: this.form.value.commandsTypeId.commandContent,
      commandId: this.form.value.commandsTypeId.id,
      commandName: this.form.value.commandsTypeId.commandName
    };
    this.commandsManagementService
      .usrCommandSend(params)
      .pipe(finalize(() => (this.saving = false)))
      .subscribe(
        (respone) => {
          this.activeModal.hide();
          if (respone.success) {
            this.translate.get('指令发送成功!').subscribe((res: string) => {
              this.log.success(res);
            });
          } else {
            this.translate.get('指令发送失败!').subscribe((res: string) => {
              this.log.error(res);
            });
          }
        },
        (error) => console.log(`请求失败信息: ${error}`)
      );
  }

  buildForm() {
    this.form = this.formBuilder.group({
      plateNumber: [{ value: null, disabled: true }],
      deviceNo: [{ value: null, disabled: true }],
      commandsTypeId: [null, [Validators.required]],
      channel: [{ value: 'Web command', disabled: true }]
    });
  }
}
