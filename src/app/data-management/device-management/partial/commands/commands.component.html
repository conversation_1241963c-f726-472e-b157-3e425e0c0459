<ng-container *ngIf="!popShow; else confirmTpl">
  <div class="modal-header">
    <h5 class="modal-title" translate>{{ title }}</h5>
    <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="plateNumber">
          {{ '车辆信息' | translate }}
        </nz-form-label>
        <nz-form-control [nzSm]="14" [nzXs]="24">
          <input
            class="form-control"
            type="text"
            maxlength="100"
            formControlName="plateNumber"
            id="plateNumber"
            placeholder="{{ '请输入车辆型号' | translate }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="deviceNo" translate>
          {{ '设备号' | translate }}
        </nz-form-label>
        <nz-form-control [nzSm]="14" [nzXs]="24">
          <input
            class="form-control"
            type="text"
            maxlength="17"
            formControlName="deviceNo"
            id="deviceNo"
            placeholder="{{ '请输入设备号' | translate }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="commandsTypeId">
          {{ '指令类型' | translate }}
        </nz-form-label>
        <nz-form-control [nzSm]="14" [nzXs]="24" [nzErrorTip]="errorTpl">
          <nz-select
            formControlName="commandsTypeId"
            id="commandsTypeId"
            nzPlaceHolder="{{ '请选择指令类型' | translate }}"
            nzShowSearch
          >
            <nz-option
              *ngFor="let option of deviceTypeList"
              [nzLabel]="option.commandName | translate"
              [nzValue]="option"
            ></nz-option>
          </nz-select>
          <ng-template #errorTpl let-control>
            {{ '请选择指令类型' | translate }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">
          {{ '发送方式' | translate }}
        </nz-form-label>
        <nz-form-control [nzSm]="14" [nzXs]="24">
          <input
            class="form-control"
            formControlName="channel"
            type="text"
            maxlength="50"
            placeholder="{{ '网络指令' | translate }}"
          />
        </nz-form-control>
      </nz-form-item>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" nz-button (click)="activeModal.hide()">
      {{ '取消' | translate }}
    </button>
    <button type="submit" nz-button nzType="primary" (click)="submit()">
      {{ '确认' | translate }}
    </button>
  </div>
</ng-container>

<ng-template #confirmTpl>
  <div class="modal-header">
    <h5 class="modal-title">&nbsp;</h5>
    <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <div nz-row [nzGutter]="[0, 16]">
      <div nz-col nzSpan="24" class="t-center">
        <strong>{{ '请确认是否发送命令' | translate }}？</strong>
      </div>

      <div nz-col nzSpan="24">
        <div nz-row [nzGutter]="[8, 0]">
          <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '车牌号' | translate }}:</div>
          <div nz-col nzFlex="auto">
            {{ form.getRawValue().plateNumber }}
          </div>
        </div>
      </div>
      <div nz-col nzSpan="24">
        <div nz-row [nzGutter]="[8, 0]">
          <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '设备号' | translate }}:</div>
          <div nz-col nzFlex="auto">
            {{ form.getRawValue().deviceNo }}
          </div>
        </div>
      </div>
      <div nz-col nzSpan="24">
        <div nz-row [nzGutter]="[8, 0]">
          <div nz-col nzOffset="4" nzFlex="100px" class="t-right">{{ '指令类型' | translate }}:</div>
          <div nz-col nzFlex="auto" translate>
            {{ form.value.commandsTypeId.commandName }}
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="btnShow" class="error" translate>{{ errMsg }}</div>
  </div>

  <div class="modal-footer">
    <ng-container *ngIf="!btnShow">
      <button type="button" nz-button (click)="activeModal.hide()">
        {{ '取消' | translate }}
      </button>
      <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="confirm()">
        {{ '确认' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="btnShow">
      <button type="button" nz-button nzType="primary" (click)="activeModal.hide()">
        {{ '确定' | translate }}
      </button>
    </ng-container>
  </div>
</ng-template>
