import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SharedModule } from '../../shared/shared.module';
import { MapModule } from '@app/map/map.module';

import { DeviceManagementRoutingModule } from './device-management-routing.module';
import { DeviceIndexComponent } from './partial/device-index/device-index.component';
import { DeviceCreatComponent } from './partial/device-creat/device-creat.component';
import { EditComponent } from './partial/edit/edit.component';
import { CommandsComponent } from './partial/commands/commands.component';
import { DeviceDeiailComponent } from './partial/device-deiail/device-deiail.component';
import { DeviceUiComponent } from './partial/device-ui/device-ui.component';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [
    DeviceIndexComponent,
    DeviceCreatComponent,
    EditComponent,
    CommandsComponent,
    DeviceDeiailComponent,
    DeviceUiComponent
  ],
  imports: [
    FormsModule,
    TranslateModule,
    SharedModule,
    CommonModule,
    DeviceManagementRoutingModule,
    ReactiveFormsModule,
    MapModule
  ],
  exports: [
    DeviceIndexComponent,
    DeviceCreatComponent,
    EditComponent,
    CommandsComponent,
    DeviceDeiailComponent,
    DeviceUiComponent
  ],
  entryComponents: [DeviceUiComponent, DeviceCreatComponent, EditComponent, CommandsComponent]
})
export class DeviceManagementModule {}
