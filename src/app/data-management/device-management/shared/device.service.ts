import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { QueryPageList } from '@app/shared/models/type';
import { Device, SensorConfig, sensorTypeInfo, SensorType, DoorType } from './models/device';
import { WebApiResultResponse, PagingResponse } from '@core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class DeviceService extends WebApiResultResponse {
  // language = window.localStorage.getItem('lang');
  constructor(private http: HttpClient) {
    super();
  }

  // 获取设备型号
  getDeviceType() {
    const url = 'glcrm-vehicle-api/v1/api/device/sysDeviceTypes';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  getDeviceList(params: QueryPageList): Observable<PagingResponse> {
    const url = `glcrm-vehicle-api/v1/api/device/pageDevice/${params.pageIndex}/${params.pageSize}`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  creatDevice(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/device';
    return this.http.post(url, params);
  }

  getDevice(deviceId: number): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/device/${deviceId}`;
    return this.http.get<any>(url);
  }

  updateDevice(params: Device): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/device`;
    return this.http.put(url, params);
  }

  deleteDevice(entity: Device): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/device/${entity.id}`;
    return this.http.delete(url);
  }

  getTotalNumber(params: any): Observable<any> {
    const url = 'glcrm-vehicle-api/v1/api/device/deviceCount';
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 导出
  exportReport(entity: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = `glcrm-vehicle-api/v1/api/device/exportDevice`;
    // tslint:disable-next-line: max-line-length
    return this.http.post(url, entity, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }

  // 获取设备定位信息
  equipmentPosition(id: any): Observable<any> {
    const url = `glcrm-track-api/v1/api/position/device?imei=${id}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 标签
  getLabelList(): Observable<any> {
    const url = 'glcrm-account-api/v1/api/label/labelList';
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 规则
  getRuleList(params: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/ruleConfig/page`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 新增标签列表
  addLabelList(labelName: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/addLabel?labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 条件判断是否可发送指令
  commandJudgment(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/commandJudgment`;
    // const url = `http://*************:7080/v1/api/command/commandJudgment`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 发送指令
  commandSend(params: any): Observable<any> {
    const url = `glcrm-vehicle-api/v1/api/command/commandSend`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  /**
   * 根据设备号获取传感器信息
   * @param id
   */
  getSensorInfo(id: string) {
    const url = `glcrm-vehicle-api/v1/api/device/getDeviceInfoExtByIMEI?deviceNo=${id}`;
    return this.http.get(url);
  }

  /**
   * 根据设备号获取obd设备信息
   * @param id
   */
  getObdInfo(id: string) {
    const url = `glcrm-vehicle-api/v1/api/device/obd/info?deviceNo=${id}`;
    return this.http.get(url);
  }

  /**
   * 转换传感器值
   * @param sensor
   * @returns
   */
  formatSensorValue(sensor: SensorConfig): string {
    let value = '-';
    const info = sensorTypeInfo[sensor.type];
    switch (sensor.type) {
      case SensorType['Door Open']:
        const [on, off] = info.unit.split('/');
        if (typeof sensor.value === 'string') {
          value = sensor.value === DoorType.Off.toString() ? off : on;
        } else {
          value = '-';
        }
        break;
      case SensorType['Temperature&Humidity']:
        if (!sensor.value || typeof sensor.value !== 'object') {
          return value;
        }
        let tem = sensor.value.tem;
        let hum = sensor.value.hum;
        const [temUnit, humUnit] = info.unit.split('/');
        tem = tem !== '' && tem !== null ? `${tem}${temUnit}` : '';
        hum = hum !== '' && hum !== null ? `${hum}${humUnit}` : '';
        value = [tem, hum].filter((v) => !!v).join('、');
        value = value || '-';
        break;
      default:
        // 有规则标定的需要展示L 没有的显示原来的（现在只有油的存在规则）有标定显示标定demarcateValue 没有显示原始值value
        if (
          sensor.type === SensorType['Oil rod fuel consumption sensor'] ||
          sensor.type === SensorType['Ultrasonic Fuel Consumption Sensor'] ||
          sensor.type === SensorType['Voltage fuel consumption sensor']
        ) {
          // info.unit = sensor.ruleConfigId ? 'L' : info.unit;
          if (sensor.demarcateValue !== '' && sensor.demarcateValue !== null) {
            sensor.value = sensor.demarcateValue;
          }
        }
        value =
          sensor.value !== '' && sensor.value !== null
            ? `${sensor.value}${sensor.ruleConfigId ? 'L' : info.unit}`
            : '-';
        break;
    }
    return value;
  }

  /**
   * 相同类型传感器值合并在一起并过滤掉没有未配置值的传感器
   * @param sensors
   * @returns
   */
  collectSensorValue(sensors: SensorConfig[]): { type: SensorType; value: SensorConfig[] }[] {
    let values: { type: SensorType; value: SensorConfig[] }[] = [];
    Object.keys(sensorTypeInfo).forEach((type) => {
      // 过滤掉没有未配置值的传感器
      const isExits = sensors.some((sensorConfig) => Number(sensorConfig.type) === Number(type));
      if (isExits) {
        const currSensors: SensorConfig[] = sensors.filter((item) => item.type === Number(type));
        values = [...values, { type: Number(type), value: currSensors }];
      }
    });
    return values;
  }
}
