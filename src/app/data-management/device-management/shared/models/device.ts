import { NzSelectOptionInterface } from 'ng-zorro-antd/select';

/** 设备状态，默认Invalid */
export enum DeviceStatus {
  Invalid,
  Offline,
  Online
}

export const deviceStatusInfo = {
  [DeviceStatus.Invalid]: { name: '未启用', value: DeviceStatus.Invalid, backgroundColor: '#FF7511' },
  [DeviceStatus.Offline]: { name: '离线', value: DeviceStatus.Offline, backgroundColor: 'rgb(162 170 183)' },
  [DeviceStatus.Online]: { name: '在线', value: DeviceStatus.Online, backgroundColor: '#1CD98F' }
};

/** 门磁状态 */
export enum DoorType {
  On,
  Off
}

/**
 * 传感器类型
 */
export enum SensorType {
  /** 油杆油耗传感器 */
  'Oil rod fuel consumption sensor' = 1,
  /** 超声波油耗传感器 */
  'Ultrasonic Fuel Consumption Sensor',
  /** 电压油耗传感器 */
  'Voltage fuel consumption sensor',
  /** 温度传感器 */
  'Temperature',
  /** 湿度传感器 */
  'Humidity',
  /** 温湿度传感器 */
  'Temperature&Humidity',
  /** 开关门传感器 */
  'Door Open'
}

export interface SensorConfig {
  /** 传感器类型 */
  type: SensorType;
  /** 路数 */
  lineNo?: string;
  /** 安装位置 */
  site?: string;
  /** 传感器值-未标定 */
  value?: any;
  /**规则 */
  ruleConfigId?: any;
  // 标定值
  demarcateValue?: any;
}

export const lineNoList: NzSelectOptionInterface[] = [
  { label: 'ID1', value: 1 },
  { label: 'ID2', value: 2 },
  { label: 'ID3', value: 3 },
  { label: 'ID4', value: 4 }
];

const ruleList: any = [];

export const sensorTypeInfo = {
  [SensorType['Oil rod fuel consumption sensor']]: {
    label: '油杆油耗传感器',
    value: SensorType['Oil rod fuel consumption sensor'],
    unit: '%',
    lineNoList: lineNoList.slice(0),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/oil.png',
    iconNew: 'icon-a-bianzu11',
    ruleList
  },
  [SensorType['Ultrasonic Fuel Consumption Sensor']]: {
    label: '超声波油耗传感器',
    value: SensorType['Ultrasonic Fuel Consumption Sensor'],
    unit: 'mm',
    lineNoList: lineNoList.slice(0),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/ultrasonic.png',
    iconNew: 'icon-a-bianzu6beifen',
    ruleList
  },
  [SensorType['Voltage fuel consumption sensor']]: {
    label: '电压油耗传感器',
    value: SensorType['Voltage fuel consumption sensor'],
    unit: 'mV',
    lineNoList: lineNoList.slice(0, 1),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/voltage.png',
    iconNew: 'icon-a-bianzu9beifen4',
    ruleList
  },
  [SensorType['Temperature']]: {
    label: '温度传感器',
    value: SensorType['Temperature'],
    unit: '℃',
    lineNoList: lineNoList.slice(0),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/temperature.png',
    iconNew: 'icon-a-bianzu6',
    ruleList
  },
  [SensorType['Humidity']]: {
    label: '湿度传感器',
    value: SensorType['Humidity'],
    unit: '%RH',
    lineNoList: lineNoList.slice(0),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/humidity.png',
    iconNew: 'icon-a-bianzu9',
    ruleList
  },
  [SensorType['Temperature&Humidity']]: {
    label: '温湿度传感器',
    value: SensorType['Temperature&Humidity'],
    unit: '℃/%RH',
    lineNoList: lineNoList.slice(0),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/temperature&humidity.png',
    iconNew: 'icon-wenshidu',
    ruleList
  },
  [SensorType['Door Open']]: {
    label: '开关门传感器',
    value: SensorType['Door Open'],
    unit: '开/关',
    lineNoList: lineNoList.slice(0, 3),
    lineNoRequired: true,
    siteRequired: false,
    icon: '/assets/media/app/img/sensor/door.png',
    iconNew: 'icon-a-bianzu12',
    ruleList
  }
};

export interface Device {
  id?: string;
  deviceTypeId: number;
  wireless: number;
  typeName: string;
  deviceNo: string;
  simNo: string;
  phoneNumber: string;
  installName: string;
  installTime: string;
  producerName: string;
  installAddress: string;
  status: number;
}
