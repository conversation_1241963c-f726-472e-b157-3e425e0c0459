import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { SharedModule } from '../shared/shared.module';

import { CustomerBillingPlanRoutingModule } from './customer-billing-plan-routing.module';
import { AccountListComponent } from './partial/account-list/account-list.component';
import { RechargeRecordsComponent } from './partial/recharge-records/recharge-records.component';
import { RechargeComponent } from './partial/recharge/recharge.component';
import { SettlementRulesComponent } from './partial/settlement-rules/settlement-rules.component';
import { DeviceListComponent } from './partial/device-list/device-list.component';
import { CreateRulesComponent } from './partial/create-rules/create-rules.component';
import { AdjustPricesComponent } from './partial/adjust-prices/adjust-prices.component';
import { RulesRecordsComponent } from './partial/rules-records/rules-records.component';
import { BatchOperationComponent } from './partial/batch-operation/batch-operation.component';
import { DeviceConsumptionRecordsComponent } from './partial/device-consumption-records/device-consumption-records.component';
import { CancelDeviceConsumptionComponent } from './partial/cancel-device-consumption/cancel-device-consumption.component';

@NgModule({
  declarations: [
    AccountListComponent,
    RechargeRecordsComponent,
    RechargeComponent,
    SettlementRulesComponent,
    DeviceListComponent,
    CreateRulesComponent,
    AdjustPricesComponent,
    RulesRecordsComponent,
    BatchOperationComponent,
    DeviceConsumptionRecordsComponent,
    CancelDeviceConsumptionComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    CustomerBillingPlanRoutingModule
  ],
  exports: [AccountListComponent, RechargeRecordsComponent]
})
export class CustomerBillingPlanModule {}
