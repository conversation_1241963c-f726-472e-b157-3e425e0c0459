import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { WebApiResultResponse } from '@core/http/web-api-result-response';

@Injectable({
  providedIn: 'root'
})
export class CustomerBillingPlanService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  // 账户
  // 获取客户账户管理的账户列表
  getAccountList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/BalanceInfo/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取充值记录列表
  getRechargeRecordsList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/BalanceDeposit/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 获取充值金额的货币单位
  getRechargeUnit(params: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/BalanceInfo/list`;
    return this.http.post(url, {}, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 充值
  recharge(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/BalanceInfo/add`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取结算规则列表
  getARulesList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/rules/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 删除规则
  delete(id: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/rules/${id}`;
    return this.http.delete(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 根据租户id获取租户下设备型号
  getDeviceType(tenantId: string) {
    const url = `glcrm-vehicle-api/v1/api/device/deviceType/${tenantId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 新增规则
  addRules(params: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/rules`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 调价
  adjustPrices(params: any): Observable<boolean> {
    const url = `glcrm-account-api/v1/api/settlement/rules/adjustment`;
    return this.http.put(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取规则消费列表
  getRulesRecordsList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/rules/consumption/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 规则消费列表设备清单下载
  exportDeviceList(params: any): Observable<any> {
    const language = window.localStorage.getItem('lang');
    const url = 'glcrm-account-api/v1/api/settlement/rules/consumption/device/export';
    return this.http.post(url, params, {
      headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Accept-Language': language }),
      responseType: 'blob',
      observe: 'response'
    });
  }

  // 获取租户设备列表
  getDeviceList(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/device/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取账户余额
  getBalance(tenantId: string) {
    const params = {
      tenantId
    };
    const url = `glcrm-account-api/v1/api/settlement/account/statistics`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 批量操作获取设备下拉数据和规则
  getDeviceTypeRules(tenantId: string) {
    const url = `glcrm-account-api/v1/api/settlement/device/model-rules/list/${tenantId}`;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 获取设备组织树
  getDeviceTree(params: any) {
    const url = `glcrm-vehicle-api/v1/api/device/organization/tree`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 批量操作
  batchRecharge(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/device/recharge`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 设备消费记录列表
  deviceRecords(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/device/recharge/page`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 撤销设备消费
  cancelDeviceConsumption(param: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/settlement/device/recharge/revert`;
    return this.http.post(url, param).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
