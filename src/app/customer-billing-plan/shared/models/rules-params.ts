import { regex } from '@app/shared/utils/regex';
import { IsNotEmpty, Matches, ValidateNested } from 'class-validator';
export class rulesParams {
  @ValidateNested({ each: true })
  paramsArray: Array<paramsArray> = [new paramsArray()];
}

export class paramsArray {
  tenantId: string;

  @IsNotEmpty({ message: '请选择设备型号' })
  // @Matches(regex.numberAndPoint, { message: '请输入正确的规则名称' })
  deviceTypeId: string;

  @IsNotEmpty({ message: '请选择服务内容' })
  serviceType: string;

  @IsNotEmpty({ message: '请输入结算周期' })
  @Matches(regex.pIntNumber, { message: '请输入结算周期' })
  servicePeriod: number;

  periodUnit: string;

  @IsNotEmpty({ message: '请输入单价' })
  @Matches(regex.numberAndPoint, { message: '请输入单价' })
  unitPrice: number;

  @IsNotEmpty({ message: '请选择货币单位' })
  monetaryUnit: string;

  @IsNotEmpty({ message: '请输入操作人姓名' })
  salesName: string;
}
