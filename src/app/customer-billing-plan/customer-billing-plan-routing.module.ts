import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RouteExtensions } from '@app/core';
import { AccountListComponent } from './partial/account-list/account-list.component';
import { RechargeRecordsComponent } from './partial/recharge-records/recharge-records.component';
import { SettlementRulesComponent } from './partial/settlement-rules/settlement-rules.component';
import { DeviceListComponent } from './partial/device-list/device-list.component';

const routes: Routes = RouteExtensions.withHost(
  {
    path: '',
    component: AccountListComponent
  },
  [
    {
      path: 'record/:id',
      component: RechargeRecordsComponent,
      data: { title: '充值记录' }
    },
    {
      path: 'rules/:id',
      component: SettlementRulesComponent,
      data: { title: '结算规则' }
    },
    {
      path: 'device/:id',
      component: DeviceListComponent,
      data: { title: '设备列表' }
    }
  ]
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomerBillingPlanRoutingModule {}
