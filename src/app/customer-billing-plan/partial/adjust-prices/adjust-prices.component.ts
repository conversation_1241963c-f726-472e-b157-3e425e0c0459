import { Component, OnInit, Input, ChangeDetectorRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { Logger } from '@app/core/logger.service';
import { LoggerFactory } from '@app/core/logger-factory.service';
import { DeviceService } from '@app/data-management/device-management/shared/device.service';
import { finalize } from 'rxjs/operators';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { regex } from '@app/shared/utils/regex';

@Component({
  selector: 'app-adjust-prices',
  templateUrl: './adjust-prices.component.html',
  styleUrls: ['./adjust-prices.component.scss']
})
export class AdjustPricesComponent implements OnInit {
  @Input() set data(value: any) {
    value.ruleId = value.id;
    value.serviceType = value.serviceType.toString();
    this.monetaryUnit = value.monetaryUnit;
    this.form.patchValue(value);
  }
  form: FormGroup;
  log: Logger;
  saving = false;
  deviceTypeList: Array<any> = [];
  monetaryUnit = '';
  periodUnit = 'Days';
  constructor(
    private formBuilder: FormBuilder,
    public activeModal: BsModalRef,
    public modalService: BsModalService,
    public cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    public translate: TranslateService,
    private deviceService: DeviceService,
    private customerBillingPlanService: CustomerBillingPlanService
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger();
  }

  ngOnInit(): void {
    this.getDeviceTypeList();
  }

  // 获取设备
  getDeviceTypeList() {
    this.deviceService
      .getDeviceType()
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.deviceTypeList = res.data;
        }
      });
  }

  submit() {
    if (this.saving) {
      return;
    }
    this.saving = true;
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    const data = this.form.value;
    // console.log('data', data)
    this.customerBillingPlanService.adjustPrices(data).subscribe(
      (res: any) => {
        if (res.success) {
          this.activeModal.hide();
          this.saving = false;
        }
      },
      (error) => {
        this.translate.get(error.error.message).subscribe((data: string) => {
          this.saving = false;
          this.log.error(data);
        });
      }
    );
  }

  close() {
    this.activeModal.hide();
  }

  buildForm() {
    this.form = this.formBuilder.group({
      ruleId: [],
      deviceTypeId: [{ value: '', disabled: true }],
      serviceType: [{ value: '', disabled: true }],
      servicePeriod: [{ value: '', disabled: true }],
      unitPrice: [null, [Validators.required, Validators.pattern(regex.numberAndPoint)]],
      salesName: [null, [Validators.required]]
    });
  }
}
