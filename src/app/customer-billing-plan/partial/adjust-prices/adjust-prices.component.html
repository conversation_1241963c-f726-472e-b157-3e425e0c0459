<div class="modal-header">
  <h5 class="modal-title">{{ '产品规则标准' | translate }}</h5>
  <button type="button" class="close" (click)="close()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzFor="deviceTypeId">
        {{ '设备型号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="deviceTypeIdErrorTpl">
        <nz-select
          formControlName="deviceTypeId"
          id="deviceTypeId"
          nzPlaceHolder="{{ '请选择设备型号' | translate }}"
          nzShowSearch
        >
          <nz-option
            *ngFor="let option of deviceTypeList"
            [nzLabel]="option.typeName"
            [nzValue]="option.id"
          ></nz-option>
        </nz-select>
        <ng-template #deviceTypeIdErrorTpl let-control>
          {{ '请选择设备型号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '服务内容' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="serviceTypeErrorTpl">
        <nz-radio-group formControlName="serviceType" id="serviceType">
          <label nz-radio nzValue="1">{{ '平台' | translate }}</label>
          <label nz-radio nzValue="2">{{ 'API' | translate }}</label>
        </nz-radio-group>
        <ng-template #serviceTypeErrorTpl let-control>
          {{ '请选择服务内容' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <!-- <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '服务内容' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="serviceTypeErrorTpl" *ngIf="data">
        <nz-radio-group formControlName="serviceType" id="serviceType">
          <label nz-radio [nzValue]="data.serviceType">{{ serviceEnum[data.serviceType] | translate }}</label>
        </nz-radio-group>
        <ng-template #serviceTypeErrorTpl let-control>
          {{ '请选择服务内容' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item> -->

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '结算周期' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="10" [nzXs]="12" [nzErrorTip]="servicePeriodErrorTpl">
        <div class="recharge-box">
          <div class="recharge-input">
            <input
              type="text"
              nz-input
              maxlength="4"
              formControlName="servicePeriod"
              id="servicePeriod"
              placeholder="{{ '请输入结算周期' | translate }}"
            />
            <ng-template #servicePeriodErrorTpl let-control>
              {{ '请输入结算周期' | translate }}
            </ng-template>
          </div>
        </div>
      </nz-form-control>
      <nz-form-control [nzSm]="10" [nzXs]="12">
        <div class="recharge-unit">
          {{ periodUnit }}
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
        {{ '单价' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="10" [nzXs]="12" [nzErrorTip]="unitPriceErrorTpl">
        <div class="recharge-box">
          <div class="recharge-input">
            <input
              type="text"
              nz-input
              maxlength="6"
              formControlName="unitPrice"
              id="unitPrice"
              placeholder="{{ '请输入单价' | translate }}"
            />
            <ng-template #unitPriceErrorTpl let-control>
              {{ '请输入单价' | translate }}
            </ng-template>
          </div>
        </div>
      </nz-form-control>
      <nz-form-control [nzSm]="10" [nzXs]="12">
        <div class="recharge-unit">
          {{ monetaryUnit }}
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
        {{ '操作人姓名' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="10" [nzXs]="12" [nzErrorTip]="salesNameErrorTpl">
        <div class="recharge-box">
          <div class="recharge-input">
            <input
              type="text"
              nz-input
              maxlength="50"
              formControlName="salesName"
              id="salesName"
              placeholder="{{ '请输入操作人姓名' | translate }}"
            />
            <ng-template #salesNameErrorTpl let-control>
              {{ '请输入操作人姓名' | translate }}
            </ng-template>
          </div>
        </div>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="close()">
    {{ '取消' | translate }}
  </button>
  <button
    type="button"
    nz-button
    nzType="primary"
    class="purple_btn"
    [disabled]="form.invalid"
    [nzLoading]="saving"
    (click)="submit()"
  >
    {{ '保存' | translate }}
  </button>
</div>
