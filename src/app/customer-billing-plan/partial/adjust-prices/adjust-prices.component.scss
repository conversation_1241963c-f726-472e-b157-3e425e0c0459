@import "../../../styles/themes/theme-value";
.modal-body {
  width: 738px;
  // max-height: calc(100vh - 120px) !important;
  max-height: 580px !important;
  padding: 25px !important;
  overflow-y: auto;
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  // width: 32%;
}

[nz-radio] {
  display: block;
  height: 32px;
  line-height: 32px;
}

.recharge-box {
  display: flex;
  justify-content: space-between;
  .recharge-input,
  .recharge-unit {
    width: 95%;
  }
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}
