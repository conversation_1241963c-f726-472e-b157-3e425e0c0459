<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'deviceNo'" label="{{ '设备号' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <input
                type="text"
                nz-input
                placeholder="{{ '请输入设备号' | translate }}"
                [(ngModel)]="rule.datas[dataIndex]"
              />
            </ng-template>
          </ngx-query-field>
          <ngx-query-field [name]="'rechargeTime'" label="{{ '充值时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
          <ngx-query-field [name]="'createdTime'" label="{{ '创建时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <div class="m-portlet">
    <div class="m-portlet__head">
      <div class="m-portlet__head-caption">
        <div class="m-portlet__head-tools">
          <ul class="m-portlet__nav">
            <li class="m-portlet__nav-item" appApplyPermission="add">
              <button nz-button nzType="primary" (click)="batchOperation()">
                {{ '批量操作' | translate }}
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="m-portlet__body p-0">
      <ngx-datatable
        appNgxDataTable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [columnMode]="'force'"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
      >
        <ngx-datatable-column
          [sortable]="false"
          [width]="260"
          name="{{ '所属机构' | translate }}"
          prop="group"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" [title]="row.orgName">
              {{ row.orgName | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="200"
          name="{{ '设备号' | translate }}"
          prop="deviceNo"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" [title]="row.deviceNo">
              {{ row.deviceNo | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '设备状态' | translate }}"
          prop="status"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ deviceStatus[row.status] | translate | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="160"
          name="{{ '到期日' | translate }}"
          prop="expireTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.expireTime | localDate : accountSetting.dateTimeFormat }}"
            >
              {{ row.expireTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="260"
          name="{{ '产品规则标准' | translate }}"
          prop="serviceType"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ serviceEnum[row.serviceType] | translate }}"
            >
              {{ serviceEnum[row.serviceType] | translate | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" title="{{ row.deviceTypeName }}">
              {{ row.deviceTypeName | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ row.servicePeriod | isNull }}{{ row.periodUnit | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }">
              {{ row.unitPrice | isNull }}{{ row.monetaryUnit | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="160"
          name="{{ '充值时间' | translate }}"
          prop="rechargeTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.rechargeTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.rechargeTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="160"
          name="{{ '创建时间' | translate }}"
          prop="createdAt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.createdAt | localDate : accountSetting.dateTimeFormat }}"
            >
              {{ row.createdAt | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="180"
          name="{{ '首次上报位置' | translate }}"
          prop="firstLocationTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.firstLocationTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.firstLocationTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.firstLocationAddress | isNull }}"
            >
              <!-- {{ row.firstLocationLng | isNull }} {{ row.firstLocationLat | isNull }} -->
              {{ row.firstLocationAddress | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="180"
          name="{{ '更新位置' | translate }}"
          prop="locationTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              [ngClass]="{ red: row.status === overdue }"
              title="{{ row.locationTime | localDate : accountSetting.dateTimeFormat | isNull }}"
            >
              {{ row.locationTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
            <div class="ellipsis" [ngClass]="{ red: row.status === overdue }" title="{{ row.address | isNull }}">
              {{ row.address | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '操作' | translate }}"
          headerClass="text-left"
          cellClass="text-left"
          [frozenRight]="true"
          [width]="100"
          [minWidth]="100"
          [maxWidth]="100"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="operators">
              <span
                class="margin_right"
                nzTooltipTitle="{{ '设备消费记录' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="deviceRecords(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/records.svg" />
                </a>
              </span>
            </div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
      <div class="return-btn">
        <button type="button" nz-button nzType="primary" class="return" [routerLink]="['./../../']">
          {{ '返回' | translate }}
          <i class="la la-angle-right icon"></i>
        </button>
      </div>
    </div>
  </div>
</div>
