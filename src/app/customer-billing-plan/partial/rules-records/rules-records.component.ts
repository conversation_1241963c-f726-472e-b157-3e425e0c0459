import { Component, OnInit, Input, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { addDays, endOfDay, startOfDay } from 'date-fns';

@Component({
  selector: 'app-rules-records',
  templateUrl: './rules-records.component.html',
  styleUrls: ['./rules-records.component.scss']
})
export class RulesRecordsComponent implements OnInit, AfterViewInit {
  @Input() ruleId: string;
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  isShowCollapse = false;
  originTemplate: any;
  billType = {
    1: '充值',
    2: '回退'
  };
  revert = 2;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'operatedTime', op: 'eq' }],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private changeDetectorRef: ChangeDetectorRef,
    private customerBillingPlanService: CustomerBillingPlanService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }
  ngOnInit(): void {}

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  loadList(event: any) {
    const page = event.page;
    const params: any = this.loadProperty(page);
    params.ruleId = this.ruleId;
    this.event = event;
    this.loading = true;
    this.footer.showTotalElements = true;
    // console.log(params);
    this.customerBillingPlanService
      .getRulesRecordsList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'operatedTime' && rule.data) {
        map['startTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['endTimeStamp'] = endOfDay(rule.data[1]).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  // 重置查询模板
  reset() {
    this.queryTemplates = this.originTemplate;
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  exportRuleRecord(billId: number) {
    const params = {
      billId
    };
    this.customerBillingPlanService.exportDeviceList(params).subscribe((response: any) => {
      if (response.ok) {
        const link = document.createElement('a');
        const blob = new Blob([response.body], { type: 'application/zip' });
        link.setAttribute('href', window.URL.createObjectURL(blob));
        this.translate.get('设备清单').subscribe((res) => {
          link.setAttribute('download', res + '-' + new Date().getTime() + '.xlsx');
        });

        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // this.refreshData();
      }
    });
  }
}
