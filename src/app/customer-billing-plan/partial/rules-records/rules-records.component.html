<div class="modal-header">
  <h5 class="modal-title">{{ '规则消费记录' | translate }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'operatedTime'" label="{{ '操作时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <!--  -->
  <div class="m-portlet">
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '账单Id' | translate }}"
          prop="billId"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.billId | translate }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '账单类型' | translate }}"
          prop="billType"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ billType[row.billType] | translate }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '操作时间' | translate }}"
          prop="createdAt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.createdAt | localDate : accountSetting.dateTimeFormat }}">
              {{ row.createdAt | localDate : accountSetting.dateTimeFormat }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '消费金额' | translate }}"
          prop="costPrice"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ (row.unitPrice * row.deviceAmount).toFixed(1) }} {{ row.monetaryUnit }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '操作人姓名' | translate }}"
          prop="accountName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.accountName }}</div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column
          name="{{ '操作' | translate }}"
          headerClass="text-left"
          cellClass="text-left"
          [frozenRight]="true"
          [width]="110"
          [minWidth]="110"
          [maxWidth]="110"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="operators" *ngIf="row.billType !== revert">
              <span
                class="margin_right"
                nzTooltipTitle="{{ '设备清单' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="exportRuleRecord(row.billId)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/device-list.svg" />
                </a>
              </span>
            </div>
            <div
              class="operators"
              *ngIf="row.billType === revert"
              title="{{ '设备号' | translate }}：{{ row.rollbackDeviceNo }}"
            >
              <!-- {{ '设备号' | translate }} ：{{ row.rollbackDeviceNo }} -->
              {{ row.rollbackDeviceNo }}
            </div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
