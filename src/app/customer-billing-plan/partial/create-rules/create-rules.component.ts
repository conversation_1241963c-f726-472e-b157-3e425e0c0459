import { Component, OnInit, Input, EventEmitter, Output, ChangeDetectorRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { TranslateService } from '@ngx-translate/core';
import { Logger } from '@app/core/logger.service';
import { LoggerFactory } from '@app/core/logger-factory.service';
import { ValidateEncapsulation } from '@app/utils/class-validator';
import { finalize } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { rulesParams, paramsArray } from '@app/customer-billing-plan/shared/models/rules-params';

@Component({
  selector: 'app-create-rules',
  templateUrl: './create-rules.component.html',
  styleUrls: ['./create-rules.component.scss']
})
export class CreateRulesComponent extends ValidateEncapsulation implements OnInit {
  @Input() tenantId = '';
  log: Logger;
  saving = false;
  rulesParams: rulesParams = new rulesParams();
  deviceTypeList: Array<any> = [];
  monetaryUnit = '';
  periodUnit = 'Days';
  monetaryUnitList: Array<any> = [];
  @Output() action: EventEmitter<any> = new EventEmitter<any>();
  constructor(
    public activeModal: BsModalRef,
    public modalService: BsModalService,
    public cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    public translate: TranslateService,
    private route: ActivatedRoute,
    private customerBillingPlanService: CustomerBillingPlanService
  ) {
    super(translate);
    this.log = this.loggerFactory.getLogger();
  }

  ngOnInit(): void {
    this.monetaryUnit = this.route.snapshot.queryParams.monetaryUnit;
    this.getDeviceTypeList(this.tenantId);
    this.getRechargeUnit();
    this.rulesParams.paramsArray.forEach((rule: paramsArray) => {
      rule.serviceType = '1';
      rule.tenantId = this.tenantId;
      rule.periodUnit = this.periodUnit;
    });
  }

  // 获取设备
  getDeviceTypeList(tenantId: string) {
    this.customerBillingPlanService
      .getDeviceType(tenantId)
      // .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.deviceTypeList = res.data;
        }
      });
  }

  getRechargeUnit() {
    const params = {
      type: 1
    };
    this.customerBillingPlanService.getRechargeUnit(params).subscribe((res: any) => {
      this.monetaryUnitList = res.data;
    });
  }
  addItem() {
    this.rulesParams.paramsArray.push(new paramsArray());
    this.rulesParams.paramsArray[this.rulesParams.paramsArray.length - 1].tenantId = this.tenantId;
    this.rulesParams.paramsArray[this.rulesParams.paramsArray.length - 1].serviceType = '1';
    this.rulesParams.paramsArray[this.rulesParams.paramsArray.length - 1].periodUnit = this.periodUnit;
    // this.setValidArr();
    this.validateFormAll(this.rulesParams, true, 'rulesParams');
  }

  deleteItem(index: number) {
    this.rulesParams.paramsArray.splice(index, 1);
    this.rulesParams.paramsArray = [...this.rulesParams.paramsArray];
    // this.setValidArr();
    this.validateFormAll(this.rulesParams, true, 'rulesParams');
  }

  // setValidArr() {
  //   this.rulesParams.paramsArray.forEach((m: any) => {
  //     m.validArr = this.rulesParams.paramsArray;
  //   });
  // }

  submit() {
    if (this.saving) {
      return;
    }
    this.saving = true;
    if (this.monetaryUnit !== '-') {
      this.rulesParams.paramsArray.forEach((rule: paramsArray) => {
        rule.monetaryUnit = this.monetaryUnit;
      });
    }
    this.validateFormAll(this.rulesParams, false, 'rulesParams').then((validateError: any) => {
      if (Object.keys(validateError).length) {
        this.saving = false;
        return;
      }
      // console.log('新建规则的参数2', this.rulesParams);
      const ruleList = this.rulesParams.paramsArray;
      this.customerBillingPlanService.addRules(ruleList).subscribe(
        (res) => {
          if (res.success) {
            this.activeModal.hide();
            this.saving = false;
          }
        },
        (error) => {
          this.translate.get(error.error.message).subscribe((data: string) => {
            this.saving = false;
            this.log.error(data);
          });
        }
      );
    });
  }

  close() {
    this.activeModal.hide();
    // this.log.clear('saveRule');
  }
}
