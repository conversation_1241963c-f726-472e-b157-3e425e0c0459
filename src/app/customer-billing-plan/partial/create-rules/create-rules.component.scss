@import "../../../styles/themes/theme-value";
.modal-body {
  width: 738px;
  // max-height: calc(100vh - 120px) !important;
  max-height: 580px !important;
  padding: 25px !important;
  overflow-y: auto;
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  // width: 32%;
}

[nz-radio] {
  display: block;
  height: 32px;
  line-height: 32px;
}

.recharge-box {
  display: flex;
  justify-content: space-between;
  .recharge-input,
  .recharge-unit {
    width: 95%;
  }
}

.group-unit {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  position: relative;
  margin-bottom: 8px;
  height: 350px;

  // &:first-child {
  //   &::before {
  //     content: " ";
  //     position: absolute;
  //     left: 64px;
  //     top: -7px;
  //     border-bottom: 10px solid #f8fafc;
  //     border-left: 10px solid transparent;
  //     border-right: 10px solid transparent;
  //   }
  // }

  &:last-child {
    margin-bottom: 0;
  }

  .left-content {
    width: 92%;
    margin-right: 8px;
    padding: 8px 16px;
    border-radius: 4px;
    background-color: #f8fafc;
  }

  .right-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 50px;
    padding: 12px;
    border-radius: 4px;
    background-color: #f8fafc;
    height: 100%;

    i {
      font-size: 18px;
      cursor: pointer;
      color: $system-color;
    }

    .del {
      cursor: pointer;
    }
  }
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}
