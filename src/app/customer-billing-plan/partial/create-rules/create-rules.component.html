<div class="modal-header">
  <h5 class="modal-title">{{ '产品规则标准' | translate }}</h5>
  <button type="button" class="close" (click)="close()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <!-- <button class="add-btn" (click)="addItem()" nz-button nzType="primary">
    <span nz-icon nzType="plus" nzTheme="outline"></span>
  </button> -->
  <div class="group-unit" *ngFor="let params of rulesParams.paramsArray; let i = index">
    <div class="left-content">
      <nz-form-item>
        <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
          {{ '设备型号' | translate }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="20"
          [nzXs]="24"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.deviceTypeId') ? 'error' : 'success'"
          [nzErrorTip]="deviceTypeIdErrorTpl"
        >
          <nz-select
            [(ngModel)]="params.deviceTypeId"
            (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
            nzPlaceHolder="{{ '请选择设备型号' | translate }}"
            nzShowSearch
          >
            <nz-option
              *ngFor="let option of deviceTypeList"
              [nzLabel]="option.typeName"
              [nzValue]="option.id"
            ></nz-option>
          </nz-select>
          <ng-template #deviceTypeIdErrorTpl let-control>
            {{ getMessage('rulesParams.paramsArray.' + i + '.deviceTypeId') }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
          {{ '服务内容' | translate }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="20"
          [nzXs]="24"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.serviceType') ? 'error' : 'success'"
          [nzErrorTip]="serviceTypeErrorTpl"
        >
          <nz-radio-group
            [(ngModel)]="params.serviceType"
            (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
          >
            <label nz-radio nzValue="1">{{ '平台' | translate }}</label>
            <!-- <label nz-radio nzValue="2">{{ 'API' | translate }}</label> -->
          </nz-radio-group>
          <ng-template #serviceTypeErrorTpl let-control>
            {{ getMessage('rulesParams.paramsArray.' + i + '.serviceType') }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
          {{ '结算周期' | translate }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="10"
          [nzXs]="12"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.servicePeriod') ? 'error' : 'success'"
          [nzErrorTip]="servicePeriodErrorTpl"
        >
          <div class="recharge-box">
            <div class="recharge-input">
              <input
                type="text"
                nz-input
                maxlength="4"
                [(ngModel)]="params.servicePeriod"
                (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
                placeholder="{{ '请输入结算周期' | translate }}"
              />
              <ng-template #servicePeriodErrorTpl let-control>
                {{ getMessage('rulesParams.paramsArray.' + i + '.servicePeriod') }}
              </ng-template>
            </div>
          </div>
        </nz-form-control>
        <nz-form-control [nzSm]="10" [nzXs]="12">
          <div class="recharge-unit">
            {{ periodUnit }}
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
          {{ '单价' | translate }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="10"
          [nzXs]="12"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.unitPrice') ? 'error' : 'success'"
          [nzErrorTip]="unitPriceErrorTpl"
        >
          <div class="recharge-box">
            <div class="recharge-input">
              <input
                type="text"
                nz-input
                maxlength="6"
                [(ngModel)]="params.unitPrice"
                (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
                placeholder="{{ '请输入单价' | translate }}"
              />
              <ng-template #unitPriceErrorTpl let-control>
                {{ getMessage('rulesParams.paramsArray.' + i + '.unitPrice') }}
              </ng-template>
            </div>
          </div>
        </nz-form-control>
        <nz-form-control
          [nzSm]="10"
          [nzXs]="12"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.monetaryUnit') ? 'error' : 'success'"
          [nzErrorTip]="monetaryUnitErrorTpl"
          *ngIf="monetaryUnit === '-'"
        >
          <div class="recharge-unit">
            <nz-select
              [(ngModel)]="params.monetaryUnit"
              (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
              nzPlaceHolder="{{ '请选择货币单位' | translate }}"
              nzShowSearch
              nzAllowClear
            >
              <nz-option
                *ngFor="let option of monetaryUnitList"
                [nzLabel]="option.name | translate"
                [nzValue]="option.name"
              ></nz-option>
            </nz-select>
            <ng-template #monetaryUnitErrorTpl let-control>
              {{ getMessage('rulesParams.paramsArray.' + i + '.monetaryUnit') }}
            </ng-template>
          </div>
        </nz-form-control>
        <nz-form-control [nzSm]="10" [nzXs]="12" *ngIf="monetaryUnit !== '-'">
          <div class="recharge-unit">
            {{ monetaryUnit }}
          </div>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
          {{ '操作人姓名' | translate }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="20"
          [nzXs]="24"
          [nzValidateStatus]="getMessage('rulesParams.paramsArray.' + i + '.salesName') ? 'error' : 'success'"
          [nzErrorTip]="salesNameErrorTpl"
        >
          <input
            type="text"
            nz-input
            maxlength="50"
            [(ngModel)]="params.salesName"
            (ngModelChange)="validateFormAll(rulesParams, true, 'rulesParams')"
            placeholder="{{ '请输入操作人姓名' | translate }}"
          />
          <ng-template #salesNameErrorTpl let-control>
            {{ getMessage('rulesParams.paramsArray.' + i + '.salesName') }}
          </ng-template>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div class="right-btn">
      <div *ngIf="i === 0">
        <i nz-icon nzType="plus-circle" nzTheme="outline" (click)="addItem()"></i>
      </div>
      <div *ngIf="i !== 0">
        <img src="/assets/media/app/img/video/del.svg" (click)="deleteItem(i)" class="del" />
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="close()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
