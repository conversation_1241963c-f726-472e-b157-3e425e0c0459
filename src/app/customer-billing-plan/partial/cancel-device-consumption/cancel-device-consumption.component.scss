@import "../../../styles/themes/theme-value";
.modal-body {
  width: 738px;
  max-height: 580px !important;
  padding: 25px !important;
  overflow-y: auto;
}

.ant-form-item-label {
  text-align: left;
}

[nz-radio] {
  display: block;
  height: 32px;
  line-height: 32px;
}

// .recharge-box {
//   display: flex;
//   justify-content: space-between;
//   .recharge-input,
//   .recharge-unit {
//     width: 95%;
//   }
// }

.count {
  position: absolute;
  right: 0;
  text-align: right;
  font-weight: 400;
  color: #575e72;
  line-height: 12px;
  font-size: 12px;
  background-color: #fff;
}

textarea {
  margin-top: 2px;
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}
