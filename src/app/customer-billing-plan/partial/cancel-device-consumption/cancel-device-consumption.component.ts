import { Component, OnInit, Input, ChangeDetectorRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { Logger } from '@app/core/logger.service';
import { LoggerFactory } from '@app/core/logger-factory.service';
import { DeviceService } from '@app/data-management/device-management/shared/device.service';
import { finalize } from 'rxjs/operators';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { regex } from '@app/shared/utils/regex';

@Component({
  selector: 'app-cancel-device-consumption',
  templateUrl: './cancel-device-consumption.component.html',
  styleUrls: ['./cancel-device-consumption.component.scss']
})
export class CancelDeviceConsumptionComponent implements OnInit {
  @Input() set data(value: any) {
    this.detail = value;
    this.form.patchValue(value);
  }
  form: FormGroup;
  log: Logger;
  saving = false;
  detail: any;
  serviceEnum: any = {
    1: '平台',
    2: 'API'
  };
  constructor(
    private formBuilder: FormBuilder,
    public activeModal: BsModalRef,
    public modalService: BsModalService,
    public cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    public translate: TranslateService,
    private deviceService: DeviceService,
    private customerBillingPlanService: CustomerBillingPlanService
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger();
  }

  ngOnInit(): void {}

  submit() {
    if (this.saving) {
      return;
    }
    this.saving = true;
    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    const data = this.form.value;
    console.log('data', data);
    this.customerBillingPlanService.cancelDeviceConsumption(data).subscribe(
      (res: any) => {
        if (res.success) {
          this.activeModal.hide();
          this.saving = false;
        }
      },
      (error) => {
        this.translate.get(error.error.message).subscribe((data: string) => {
          this.saving = false;
          this.log.error(data);
        });
      }
    );
  }

  close() {
    this.activeModal.hide();
  }

  buildForm() {
    this.form = this.formBuilder.group({
      consumeId: [],
      reason: [null, [Validators.required]],
      salesPerson: [null, [Validators.required]],
      tip: [null, [Validators.required]]
    });
  }
}
