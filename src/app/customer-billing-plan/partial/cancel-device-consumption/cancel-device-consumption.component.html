<div class="modal-header">
  <h5 class="modal-title">{{ '撤销设备消费' | translate }}</h5>
  <button type="button" class="close" (click)="close()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '设备号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24">
        {{ detail?.deviceNo }}
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '设备型号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24">
        {{ detail?.deviceTypeName }}
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24">
        {{ '服务内容' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24">
        <div>
          {{ serviceEnum[detail?.serviceType] | translate | isNull }}
        </div>
        <div>{{ detail?.servicePeriod | isNull }} {{ detail?.periodUnit | isNull }}</div>
        <div>{{ detail?.unitPrice | isNull }} {{ detail?.monetaryUnit | isNull }}</div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzFor="reason" nzRequired>
        {{ '原因' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24">
        <textarea
          rows="4"
          nz-input
          id="reason"
          formControlName="reason"
          [placeholder]="'请输入原因' | translate"
          maxlength="500"
        ></textarea>
        <div class="count">{{ form.get('reason').value ? form.get('reason').value.length : 0 }}/500</div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzFor="salesPerson" nzRequired>
        {{ '操作人姓名' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="12" [nzErrorTip]="salesNameErrorTpl">
        <div class="recharge-box">
          <div class="recharge-input">
            <input
              type="text"
              nz-input
              maxlength="50"
              formControlName="salesPerson"
              id="salesPerson"
              placeholder="{{ '请输入操作人姓名' | translate }}"
            />
            <ng-template #salesNameErrorTpl let-control>
              {{ '请输入操作人姓名' | translate }}
            </ng-template>
          </div>
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzFor="tip" nzRequired>
        {{ '提交前阅读' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="tipErrorTpl">
        <div>
          {{
            '1.提交该操作后，将清空设备在该周期内的结算规则，平台将不会展示设备上报的任何数据或者展示设备在该计费周期之前的最新一条数据。'
              | translate
          }}
        </div>
        <div>{{ '2.提交后，客户的账户余额将增加本设备对应的费用。' | translate }}</div>
        <div>{{ '3.设备开始计费7日后，不允许进行回退操作。' | translate }}</div>
        <div>
          <nz-radio-group formControlName="tip" id="tip">
            <label nz-radio nzValue="1">{{ '已阅读' | translate }}</label>
          </nz-radio-group>
        </div>
        <ng-template #tipErrorTpl let-control>
          {{ '请勾选已阅读' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="close()">
    {{ '取消' | translate }}
  </button>
  <button
    type="button"
    nz-button
    nzType="primary"
    class="purple_btn"
    [disabled]="form.invalid"
    [nzLoading]="saving"
    (click)="submit()"
  >
    {{ '保存' | translate }}
  </button>
</div>
