import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CancelDeviceConsumptionComponent } from './cancel-device-consumption.component';

describe('CancelDeviceConsumptionComponent', () => {
  let component: CancelDeviceConsumptionComponent;
  let fixture: ComponentFixture<CancelDeviceConsumptionComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CancelDeviceConsumptionComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CancelDeviceConsumptionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
