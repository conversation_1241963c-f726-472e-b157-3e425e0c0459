import { Component, Input, OnInit, ChangeDetectorRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { Logger } from '@app/core/logger.service';
import { LoggerFactory } from '@app/core/logger-factory.service';
import { finalize } from 'rxjs/operators';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzTreeSelectComponent } from 'ng-zorro-antd/tree-select';

@Component({
  selector: 'app-batch-operation',
  templateUrl: './batch-operation.component.html',
  styleUrls: ['./batch-operation.component.scss']
})
export class BatchOperationComponent implements OnInit {
  @Input() tenantId: string;
  form: FormGroup;
  log: Logger;
  saving = false;
  deviceTypeList: Array<any> = [];
  balance = 0;
  rules: any[] = [];
  serviceEnum = {
    '1': '平台',
    '2': 'API'
  };
  vehicleTree: NzTreeNodeOptions[] = [];
  deviceCount = 0;

  @ViewChild('treeSelectRef') treeSelectRef!: NzTreeSelectComponent;
  constructor(
    private formBuilder: FormBuilder,
    public activeModal: BsModalRef,
    public modalService: BsModalService,
    public cd: ChangeDetectorRef,
    private loggerFactory: LoggerFactory,
    public translate: TranslateService,
    private customerBillingPlanService: CustomerBillingPlanService
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger();
  }

  ngOnInit(): void {
    this.getDeviceTypeList();
    this.getBalance();
  }

  // 获取设备下拉数据
  getDeviceTypeList() {
    this.customerBillingPlanService
      .getDeviceTypeRules(this.tenantId)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.deviceTypeList = res.data;
        }
      });
  }

  getBalance() {
    this.customerBillingPlanService
      .getBalance(this.tenantId)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.balance = res.data.balanceMoney;
        }
      });
  }

  getRules(event: any) {
    this.rules = event.rules;
    this.getDeviceTree(event.deviceTypeId);
    this.form.get('selectedNodes').enable();
  }

  getDeviceTree(deviceTypeId: number) {
    const params = {
      deviceTypeId,
      tenantId: this.tenantId
    };
    this.customerBillingPlanService
      .getDeviceTree(params)
      .pipe(finalize(() => this.cd.markForCheck()))
      .subscribe((res) => {
        if (res.success) {
          this.vehicleTree = this.formatDeviceTree(res.data);
        }
      });
  }

  formatDeviceTree(nodes: any): NzTreeNodeOptions[] {
    if (!Array.isArray(nodes)) {
      return [];
    }

    return nodes.map((item) => {
      item.children = item.children || [];
      item.deviceList = Array.isArray(item.deviceList) ? item.deviceList : [];

      item.children = [...item.children, ...item.deviceList];
      return {
        key: item.deviceNo ? item.deviceNo : item.orgId,
        title: item.deviceNo ? item.deviceNo : item.orgName,
        // disableCheckbox: !item.deviceId,
        isLeaf: !Array.isArray(item.children) || item.children.length === 0,
        isDevice: item.deviceNo ? true : false,
        children: item.children?.length ? this.formatDeviceTree(item.children) : []
        // children: item.children?.length ? this.formatDeviceTree(item.children) : this.formatDeviceTree(item.deviceList)
      };
    });
  }

  modelChange(): void {
    const selectedNodes = this.treeSelectRef.getCheckedNodeList();
    let keys: any[] = [];
    selectedNodes.forEach((node) => {
      if (node.origin.isDevice) {
        keys.push(node.origin.key);
      }
      const children = node.origin.children;
      keys = this.getKeys(children, keys);
    });
    this.form.get('deviceNos').setValue(keys);
    this.deviceCount = keys.length;
  }

  getKeys(children: any[], keys: string[]) {
    if (children.length > 0) {
      children.forEach((item) => {
        if (item.isDevice) {
          keys.push(item.key);
        }
        if (item.children.length > 0) {
          this.getKeys(item.children, keys);
        }
      });
    }
    return keys;
  }

  submit() {
    if (this.saving) {
      return;
    }

    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }

    const data = this.form.value;
    const params = {
      deviceNos: data.deviceNos,
      ruleId: data.ruleId
    };
    // console.log('data0408', params);
    this.saving = true;
    this.customerBillingPlanService.batchRecharge(params).subscribe(
      (res: any) => {
        if (res.success) {
          this.activeModal.hide();
          // this.saving = false;
        }
      },
      (error) => {
        this.translate.get(error.error.message).subscribe((data: string) => {
          this.saving = false;
          this.log.error(data);
        });
      }
    );
  }

  close() {
    this.activeModal.hide();
  }

  buildForm() {
    this.form = this.formBuilder.group({
      deviceTypeId: [null, [Validators.required]],
      ruleId: [null, [Validators.required]],
      selectedNodes: [{ value: null, disabled: true }, [Validators.required]],
      deviceNos: []
    });
  }
}
