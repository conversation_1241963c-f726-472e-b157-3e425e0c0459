@import "../../../styles/themes/theme-value";
.modal-body {
  width: 738px;
  // max-height: calc(100vh - 120px) !important;
  max-height: 580px !important;
  padding: 25px !important;
  overflow-y: auto;
}

.balance-box {
  display: flex;
  margin-bottom: 25px;
  padding: 5px 35px;
  width: 100%;
  height: 62px;
  background: #faf2e2;
}

.balance-img {
  height: 100%;
  //   width: 100%;
}

.balance-text {
  margin-left: 22px;
  font-size: 14px;
  color: #866222;
}
.balance {
  font-size: 28px;
  color: #654610;
  line-height: 1.2;
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  // width: 32%;
}

[nz-radio] {
  display: flex;
}

.modal-footer {
  position: relative;
  padding-top: 25px !important;
}

.device-count {
  position: absolute;
  top: 0;
  right: 42px;
  display: flex;
  justify-content: flex-end;
}

.btn-box {
  display: flex;
  justify-content: flex-end;
}

.recharge-box {
  display: flex;
  justify-content: space-between;
  .recharge-input,
  .recharge-unit {
    width: 95%;
  }
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }

  .ant-select-multiple .ant-select-selection-search {
    width: 98% !important;
  }
}
