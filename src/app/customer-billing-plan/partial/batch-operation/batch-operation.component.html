<div class="modal-header">
  <h5 class="modal-title">{{ '批量操作' | translate }}</h5>
  <button type="button" class="close" (click)="close()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="balance-box">
    <img class="balance-img" src="/assets/media/app/img/customer/balance-img.png" />
    <div class="balance-text">
      <div class="balance-title">{{ '当前可用余额' | translate }}</div>
      <div class="balance">{{ balance }}</div>
    </div>
  </div>
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
        {{ '设备型号' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="deviceTypeIdErrorTpl">
        <nz-select
          formControlName="deviceTypeId"
          id="deviceTypeId"
          nzPlaceHolder="{{ '请选择设备型号' | translate }}"
          nzShowSearch
          (ngModelChange)="getRules($event)"
        >
          <nz-option
            *ngFor="let option of deviceTypeList"
            [nzLabel]="option.deviceTypeName"
            [nzValue]="option"
          ></nz-option>
        </nz-select>
        <ng-template #deviceTypeIdErrorTpl let-control>
          {{ '请选择设备型号' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <!-- <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
        {{ '服务内容' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="serviceTypeErrorTpl">
        <nz-radio-group formControlName="serviceType" id="serviceType">
          <label nz-radio nzValue="1">{{ '平台' | translate }}</label>
          <label nz-radio nzValue="2">{{ 'API' | translate }}</label>
        </nz-radio-group>
        <ng-template #serviceTypeErrorTpl let-control>
          {{ '请选择服务内容' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item> -->
    <nz-form-item>
      <nz-form-label [nzSm]="4" [nzXs]="24" nzRequired>
        {{ '产品规则标准' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="20" [nzXs]="24" [nzErrorTip]="serviceTypeErrorTpl">
        <nz-radio-group formControlName="ruleId" id="ruleId" *ngIf="rules.length">
          <label nz-radio [nzValue]="item.id" *ngFor="let item of rules">
            <div>{{ serviceEnum[item.serviceType] | translate }}</div>
            <div>{{ item.servicePeriod }} {{ item.periodUnit }}</div>
            <div>{{ item.unitPrice }} {{ item.monetaryUnit }}</div>
          </label>
        </nz-radio-group>
        <ng-template #serviceTypeErrorTpl let-control>
          {{ '请选择服务内容' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>
        {{ '设备列表' | translate }}
      </nz-form-label>
      <nz-form-control [nzSpan]="20" [nzErrorTip]="deviceIdsErrorTpl">
        <nz-tree-select
          #treeSelectRef
          class="w-full"
          [nzNodes]="vehicleTree"
          nzShowSearch
          nzCheckable
          nzDefaultExpandAll
          nzMultiple
          formControlName="selectedNodes"
          (ngModelChange)="modelChange()"
        ></nz-tree-select>
        <ng-template #deviceIdsErrorTpl let-control>
          {{ '请选择设备' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <div class="device-count">
    <div>{{ '设备数量' | translate }}：{{ deviceCount }}</div>
  </div>
  <button type="button" nz-button (click)="close()">
    {{ '取消' | translate }}
  </button>
  <button
    type="button"
    class="purple_btn"
    nz-button
    nzType="primary"
    [disabled]="form.invalid"
    [nzLoading]="saving"
    (click)="submit()"
  >
    {{ '保存' | translate }}
  </button>
</div>
