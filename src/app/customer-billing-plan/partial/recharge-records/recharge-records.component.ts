import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { addDays, endOfDay, startOfDay } from 'date-fns';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-recharge-records',
  templateUrl: './recharge-records.component.html',
  styleUrls: ['./recharge-records.component.scss']
})
export class RechargeRecordsComponent implements OnInit, AfterViewInit {
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  isShowCollapse = false;
  originTemplate: any;
  previousPage = 0; // 上一页
  nextPage = 0; // 下一页
  tenantId = '';

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'arriveDate', op: 'eq' }],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    private loggerFactory: LoggerFactory,
    private route: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private customerBillingPlanService: CustomerBillingPlanService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }
  ngOnInit(): void {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.tenantId = id;
      }
    });
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  loadList(event: any) {
    const page = event.page;
    const params: any = this.loadProperty(page);
    params.tenantId = this.tenantId;
    this.event = event;
    this.loading = true;
    this.footer.showTotalElements = true;
    this.customerBillingPlanService
      .getRechargeRecordsList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'arriveDate' && rule.data) {
        map['arriveDateStart'] = startOfDay(rule.data[0]).getTime();
        map['arriveDateEnd'] = endOfDay(rule.data[1]).getTime();
        // map['arriveDateEnd'] = addDays(startOfDay(rule.data[1]), 1).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  // 重置查询模板
  reset() {
    this.queryTemplates = this.originTemplate;
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  // download(url: string) {
  //   if (!url) {
  //     this.log.warn(this.translates['暂无数据']);
  //     return;
  //   }

  // }
}
