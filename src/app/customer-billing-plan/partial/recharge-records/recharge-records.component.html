<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'arriveDate'" label="{{ '到账时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <!--  -->
  <div class="m-portlet">
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '充值金额' | translate }}"
          prop="depositMoney"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.depositMoney | translate }}{{ row.monetaryUnit | translate }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ 'ID' | translate }}"
          prop="balanceId"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.id }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '到账时间' | translate }}"
          prop="arriveDate"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.arriveDate | localDate : accountSetting.dateFormat }}">
              {{ row.arriveDate | localDate : accountSetting.dateFormat }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '充值回执单' | translate }}"
          prop="collectionReceipt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <!-- <div class="ellipsis">{{ row.collectionReceipt }}</div> -->
            <!-- (click)="download(row.collectionReceipt)" -->
            <span nz-tooltip nzTooltipPlacement="top" nzTooltipTitle="{{ '下载' | translate }}">
              <a class="blue" target="_blank" href="{{ row.collectionReceipt }}" download="充值回执单.docx">
                {{ '下载' | translate }}
              </a>
            </span>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '业务员姓名' | translate }}"
          prop="salesName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.salesName }}">{{ row.salesName }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '创建人' | translate }}"
          prop="createdByName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.createdByName }}+{{ row.createdByAccount }}">
              {{ row.createdByName }}+{{ row.createdByAccount }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '创建时间' | translate }}"
          prop="createdAt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.createdAt | localDate : accountSetting.dateTimeFormat }}">
              {{ row.createdAt | localDate : accountSetting.dateTimeFormat }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '描述内容' | translate }}"
          prop="remark"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.remark | isNull }}">{{ row.remark | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
      <div class="return-btn">
        <button type="button" nz-button nzType="primary" class="return" [routerLink]="['./../../']">
          {{ '返回' | translate }}
          <i class="la la-angle-right icon"></i>
        </button>
      </div>
    </div>
  </div>
</div>
