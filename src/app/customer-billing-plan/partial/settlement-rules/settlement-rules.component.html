<div class="custom-table">
  <div class="m-portlet">
    <div class="m-portlet__head">
      <div class="m-portlet__head-tools">
        <ul class="m-portlet__nav">
          <li class="m-portlet__nav-item" appApplyPermission="add">
            <button nz-button nzType="primary" (click)="newRules()">
              {{ '新建规则' | translate }}
            </button>
          </li>
        </ul>
      </div>
      <!-- 写查询组件为了翻页组件生效 start-->
      <div class="page-name" [hidden]="true">
        <div class="ngx-query-container">
          <ngx-query
            [hidden]="false"
            [columnNumber]="2"
            #ngxQuery
            [queryTemplates]="queryTemplates"
            [showModeButtons]="true"
            (reset)="reset()"
            [mode]="mode"
            [showPlainCollapseToolBar]="true"
          >
            <ngx-query-field [name]="'deviceNo'" label="{{ '设备号' | translate }}" [type]="'string'">
              <ng-template
                ngx-query-value-input-template
                let-rules="rules"
                let-rule="rule"
                let-dataIndex="dataIndex"
                let-placeholder="placeholder"
              >
                <input
                  type="text"
                  nz-input
                  placeholder="{{ '请输入设备号' | translate }}"
                  [(ngModel)]="rule.datas[dataIndex]"
                />
              </ng-template>
            </ngx-query-field>
          </ngx-query>
        </div>
      </div>
      <!-- 写查询组件为了翻页组件生效 end-->
    </div>
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '规则ID' | translate }}"
          prop="id"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">
              {{ row.id }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '设备型号' | translate }}"
          prop="deviceTypeName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">
              {{ row.deviceTypeName }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '服务内容' | translate }}"
          prop="serviceType"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ serviceEnum[row.serviceType] | translate }}">
              {{ serviceEnum[row.serviceType] | translate }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '结算周期' | translate }}"
          prop="servicePeriod"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.servicePeriod }} {{ row.periodUnit }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '单价' | translate }}"
          prop="unitPrice"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.unitPrice }} {{ row.monetaryUnit | translate }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '操作时间' | translate }}"
          prop="createdAt"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.createdAt | localDate : accountSetting.dateTimeFormat }}">
              {{ row.createdAt | localDate : accountSetting.dateTimeFormat }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '操作' | translate }}"
          headerClass="text-left"
          cellClass="text-left"
          [frozenRight]="true"
          [width]="180"
          [minWidth]="180"
          [maxWidth]="180"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="operators">
              <span
                class="margin_right"
                nzTooltipTitle="{{ '调价' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="adjustPrices(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/adjust-prices.svg" />
                </a>
              </span>
              <span class="margin_right">|</span>
              <span
                class="margin_right"
                nzTooltipTitle="{{ '规则消费记录' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="rulesRecords(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/records.svg" />
                </a>
              </span>
              <span class="margin_right">|</span>
              <span
                class="margin_right"
                nzTooltipTitle="{{ '删除' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="delete(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/delete-rule.svg" />
                </a>
              </span>
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
      <div class="return-btn">
        <button type="button" nz-button nzType="primary" class="return" [routerLink]="['./../../']">
          {{ '返回' | translate }}
          <i class="la la-angle-right icon"></i>
        </button>
      </div>
    </div>
  </div>
</div>
