import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent, QueryTemplate } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger, Dialogs } from '@app/core';

import { ActivatedRoute } from '@angular/router';

import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { CreateRulesComponent } from '../create-rules/create-rules.component';
import { AdjustPricesComponent } from '../adjust-prices/adjust-prices.component';
import { RulesRecordsComponent } from '../rules-records/rules-records.component';

@Component({
  selector: 'app-settlement-rules',
  templateUrl: './settlement-rules.component.html',
  styleUrls: ['./settlement-rules.component.scss']
})
export class SettlementRulesComponent implements OnInit, AfterViewInit {
  log: Logger;
  translates = {
    结算规则: '',
    '真的要删除吗？': '',
    删除失败: '',
    删除成功: ''
  };
  accountSetting = accountSetting;
  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  originTemplate: any;
  tenantId = '';
  serviceEnum = {
    '1': '平台',
    '2': 'API'
  };

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'deviceNo', op: 'cn' }],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    private loggerFactory: LoggerFactory,
    private modalService: BsModalService,
    private route: ActivatedRoute,
    private dialogs: Dialogs,
    private changeDetectorRef: ChangeDetectorRef,
    private customerBillingPlanService: CustomerBillingPlanService,
    private translate: TranslateService
  ) {
    this.translate.get(Object.keys(this.translates)).subscribe((res) => {
      this.translates = res;
      this.log = this.loggerFactory.getLogger(this.translates['结算规则']);
    });
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }

  ngOnInit(): void {
    this.route.params.pipe(map((params) => params.id)).subscribe((id) => {
      if (id) {
        this.tenantId = id;
      }
    });
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  // 重置查询模板
  reset() {
    this.queryTemplates = this.originTemplate;
  }

  loadList(event: any) {
    const page = event.page;
    const params: any = {
      pageIndex: page.pageIndex,
      pageSize: page.pageSize,
      tenantId: this.tenantId
    };
    this.event = event;
    this.loading = true;
    this.footer.showTotalElements = true;
    // console.log('列表参数', params);
    this.customerBillingPlanService
      .getARulesList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  newRules() {
    const initialState = { tenantId: this.tenantId };
    this.modalService.show(CreateRulesComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      this.loadList(this.event);
      onHidden.unsubscribe();
    });
  }

  adjustPrices(row: any) {
    const initialState = { data: row };
    this.modalService.show(AdjustPricesComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      this.loadList(this.event);
      onHidden.unsubscribe();
    });
  }

  rulesRecords(row: any) {
    const initialState = { ruleId: row.id };
    this.modalService.show(RulesRecordsComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-lg modal-lg-custom light-modal'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      // this.loadList(this.event);
      onHidden.unsubscribe();
    });
  }

  delete(row: any) {
    this.translate.get(`真的要删除吗？`).subscribe((res) => {
      this.dialogs.confirm(res).subscribe(() => {
        this.customerBillingPlanService.delete(row.id).subscribe(
          (res) => {
            if (!res.success) {
              this.translate.get(`删除失败`).subscribe((res) => {
                this.log.error(res);
              });
              return;
            }
            this.translate.get('删除成功').subscribe((res) => {
              this.log.success(res);
            });
            this.loadList(this.event);
          },
          (err) => {
            this.translate.get(err.error.message).subscribe((res) => {
              this.log.error(res);
            });
          }
        );
      });
    });
  }
}
