<div class="modal-header">
  <h5 class="modal-title">{{ '设备消费记录' | translate }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'rechargeTime'" label="{{ '操作时间' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-range-picker
                [nzFormat]="accountSetting.dateFormat"
                class="w-full"
                [nzAllowClear]="false"
                [(ngModel)]="rule.datas[dataIndex]"
                appLocalNzDateTime
              ></nz-range-picker>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <div class="m-portlet">
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '设备消费ID' | translate }}"
          prop="consumeId"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.consumeId }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '状态' | translate }}"
          prop="status"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ deviceStatus[row.status] | translate | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '操作时间' | translate }}"
          prop="rechargeTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.rechargeTime | localDate : accountSetting.dateTimeFormat }}">
              {{ row.rechargeTime | localDate : accountSetting.dateTimeFormat | isNull }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          [width]="180"
          name="{{ '产品规则标准' | translate }}"
          prop="serviceType"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ serviceEnum[row.serviceType] | translate }}">
              {{ serviceEnum[row.serviceType] | translate | isNull }}
            </div>
            <div class="ellipsis" title="{{ row.deviceTypeName }}">
              {{ row.deviceTypeName | isNull }}
            </div>
            <div class="ellipsis">{{ row.servicePeriod | isNull }} {{ row.periodUnit | isNull }}</div>
            <div class="ellipsis">{{ row.unitPrice | isNull }} {{ row.monetaryUnit | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '操作人' | translate }}"
          prop="rechargeName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">{{ row.rechargeName | isNull }}</div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column
          name="{{ '操作' | translate }}"
          headerClass="text-left"
          cellClass="text-left"
          [frozenRight]="true"
          [width]="100"
          [minWidth]="100"
          [maxWidth]="100"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="operators" *ngIf="row.isRevert">
              <span
                class="margin_right"
                nzTooltipTitle="{{ '撤销设备消费' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="cancelConsumption(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/repeal.svg" />
                </a>
              </span>
            </div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
