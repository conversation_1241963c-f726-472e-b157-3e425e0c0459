import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';
import { finalize, map } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { endOfDay, startOfDay } from 'date-fns';
import { CancelDeviceConsumptionComponent } from '../cancel-device-consumption/cancel-device-consumption.component';

@Component({
  selector: 'app-device-consumption-records',
  templateUrl: './device-consumption-records.component.html',
  styleUrls: ['./device-consumption-records.component.scss']
})
export class DeviceConsumptionRecordsComponent implements OnInit, AfterViewInit {
  @Input() deviceNo: string;
  @Input() serviceType: number;
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  originTemplate: any;
  deviceStatus = {
    '1': '已充值',
    '2': '已回退'
  };
  serviceEnum = {
    '1': '平台',
    '2': 'API'
  };

  revert = 2;

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'rechargeTime', op: 'eq' }],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private modalService: BsModalService,
    private changeDetectorRef: ChangeDetectorRef,
    private customerBillingPlanService: CustomerBillingPlanService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger('');
    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }
  ngOnInit(): void {}

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  loadList(event: any) {
    const page = event.page;
    const params: any = this.loadProperty(page);
    params.deviceNo = this.deviceNo;
    params.serviceType = this.serviceType;
    this.event = event;
    this.loading = true;
    this.footer.showTotalElements = true;
    this.customerBillingPlanService
      .deviceRecords(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  loadProperty(page: any): any {
    const rules = page.filter.rules;
    const map: any = {};
    rules.forEach((rule: { field: any; data: any }) => {
      const key = rule.field;
      // 判断是否为时间段查询，时间段查询特殊处理
      if (rule.field === 'rechargeTime' && rule.data) {
        map['startTimeStamp'] = startOfDay(rule.data[0]).getTime();
        map['endTimeStamp'] = endOfDay(rule.data[1]).getTime();
      } else {
        map[key] = rule.data;
      }
    });
    map.pageIndex = page.pageIndex;
    map.pageSize = page.pageSize;
    return map;
  }

  // 重置查询模板
  reset() {
    this.queryTemplates = this.originTemplate;
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  cancelConsumption(row: any) {
    if (row.status === this.revert) {
      this.translate.get('设备消费已撤销').subscribe((data: string) => {
        this.log.info(data);
      });
      return;
    }
    const initialState = { data: row };
    this.modalService.show(CancelDeviceConsumptionComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      this.loadList(this.event);
      onHidden.unsubscribe();
    });
  }
}
