<div class="custom-table">
  <div class="m-portlet list_header">
    <div class="page-name">
      <div class="ngx-query-container">
        <ngx-query
          [hidden]="false"
          [columnNumber]="2"
          #ngxQuery
          [queryTemplates]="queryTemplates"
          [showModeButtons]="true"
          (reset)="reset()"
          [mode]="mode"
          [showPlainCollapseToolBar]="true"
        >
          <ngx-query-field [name]="'orgName'" label="{{ '租户名称' | translate }}" [type]="'string'">
            <ng-template
              ngx-query-value-input-template
              let-rules="rules"
              let-rule="rule"
              let-dataIndex="dataIndex"
              let-placeholder="placeholder"
            >
              <nz-select
                class="w-full"
                [(ngModel)]="rule.datas[dataIndex]"
                nzPlaceHolder="{{ '请选择租户名称' | translate }}"
                nzShowSearch
              >
                <nz-option
                  *ngFor="let item of orgNameList"
                  [nzLabel]="item.name | translate"
                  [nzValue]="item.name"
                ></nz-option>
              </nz-select>
            </ng-template>
          </ngx-query-field>
        </ngx-query>
      </div>
    </div>
  </div>

  <!--  -->
  <div class="m-portlet">
    <div class="m-portlet__body p-0">
      <ngx-datatable
        #dt
        class="material"
        [scrollbarH]="true"
        [rows]="dataList"
        [saveState]="false"
        [loadingIndicator]="loading"
        appNgxDataTable
        [ngxQuery]="ngxQuery"
        (loadValue)="loadList($event)"
        [isRetainCurrentPageQuery]="false"
        ngxNoPageFooterWatcher
        [footer]="footer"
        [count]="currentNumber"
        [selectAllRowsOnPage]="false"
        externalPaging="false"
        style="width: 100%"
        [columnMode]="'force'"
      >
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '租户名称' | translate }}"
          prop="orgName"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis" title="{{ row.orgName | translate }}">
              {{ row.orgName | translate }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '总金额' | translate }}"
          prop="totalDepositMoney"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">
              <span *ngIf="row.monetaryUnit !== '-'">{{ row.totalDepositMoney }}</span>
              {{ row.monetaryUnit | translate }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '余额' | translate }}"
          prop="balanceMoney"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="ellipsis">
              <span *ngIf="row.monetaryUnit !== '-'">{{ row.balanceMoney }}</span>
              {{ row.monetaryUnit | translate }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          [sortable]="false"
          name="{{ '更新时间' | translate }}"
          prop="newDepositTime"
          headerClass="text-left"
          cellClass="text-left"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div
              class="ellipsis"
              title="{{
                row.newDepositTime !== '-'
                  ? (row.newDepositTime | localDate : accountSetting.dateTimeFormat)
                  : row.newDepositTime
              }}"
            >
              {{
                row.newDepositTime !== '-'
                  ? (row.newDepositTime | localDate : accountSetting.dateTimeFormat)
                  : row.newDepositTime
              }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="{{ '操作' | translate }}"
          headerClass="text-left"
          cellClass="text-left"
          [frozenRight]="true"
          [width]="220"
          [minWidth]="220"
          [maxWidth]="220"
        >
          <ng-template let-row="row" ngx-datatable-cell-template>
            <div class="operators">
              <span
                class="margin_right"
                nzTooltipTitle="{{ '充值' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                (click)="charge(row)"
              >
                <a>
                  <img src="/assets/media/app/img/icons/recharge.svg" />
                </a>
              </span>
              <span class="margin_right">|</span>
              <span
                class="margin_right"
                nzTooltipTitle="{{ '充值记录' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                [routerLink]="['record', row.tenantId]"
              >
                <a>
                  <img src="/assets/media/app/img/icons/billing-records.svg" />
                </a>
              </span>
              <span class="margin_right">|</span>
              <span
                class="margin_right"
                nzTooltipTitle="{{ '结算规则' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                [routerLink]="['rules', row.tenantId]"
                [queryParams]="{ monetaryUnit: row.monetaryUnit }"
              >
                <!-- (click)="goRuleList(row)" -->
                <a>
                  <img src="/assets/media/app/img/icons/settlement-rules.svg" />
                </a>
              </span>
              <span class="margin_right">|</span>
              <span
                class="margin_right"
                nzTooltipTitle="{{ '设备清单' | translate }}"
                nzTooltipPlacement="top"
                nz-tooltip
                [routerLink]="['device', row.tenantId]"
              >
                <a>
                  <img src="/assets/media/app/img/icons/device-list.svg" />
                </a>
              </span>
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column [frozenRight]="true" [width]="10" headerClass="datatable-header-cell-acitons text-left">
          <ng-template let-column="column" ngx-datatable-header-template>
            <app-datatable-actions [datatable]="dt" [showFixed]="false" class="pull-right"></app-datatable-actions>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
      <br />
      <div class="footer-style">
        <nopage-datatable-footer
          #footer
          [currentNumber]="currentNumber"
          [totalNumber]="totalNumber"
          (getTotal)="getTotal()"
          [checkTurnPage]="turnPage.bind(this)"
        ></nopage-datatable-footer>
      </div>
    </div>
  </div>
</div>
