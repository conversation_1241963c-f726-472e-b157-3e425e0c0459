import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Router } from '@angular/router';

import { NoPageDatatableFooterComponent } from 'no-page-datatable-footer';
import { QueryMode, NgxQueryComponent } from '@zhongruigroup/ngx-query';
import { NgxDataTableDirective } from '@app/shared/directives/ngx-datatable.directive';
import { LoggerFactory, Logger } from '@app/core';

import { QueryTemplate } from '@app/shared/models/type';

import { DatatableComponent } from '@swimlane/ngx-datatable';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { RechargeComponent } from '../recharge/recharge.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';

@Component({
  selector: 'app-account-list',
  templateUrl: './account-list.component.html',
  styleUrls: ['./account-list.component.scss']
})
export class AccountListComponent implements OnInit, AfterViewInit {
  log: Logger;
  accountSetting = accountSetting;

  mode: QueryMode = QueryMode.plainCollapse;
  loading = false;

  dataList: Array<any>;
  totalNumber: number;
  currentNumber: number;
  isShowCollapse = false;
  originTemplate: any;
  previousPage = 0; // 上一页
  nextPage = 0; // 下一页
  orgNameList: Array<any> = [];

  queryTemplates: QueryTemplate[] = [
    {
      name: 'Default',
      template: {
        op: 'or',
        rules: [{ field: 'orgName', op: 'eq' }],
        groups: []
      }
    }
  ];

  @ViewChild('appNgxDataTable') ngxDataTable: NgxDataTableDirective;
  @ViewChild('ngxQuery') ngxQuery: NgxQueryComponent;
  @ViewChild('dt') table: DatatableComponent;
  @ViewChild('footer') footer: NoPageDatatableFooterComponent;

  public datatable: any;
  event: any;
  constructor(
    private loggerFactory: LoggerFactory,
    private modalService: BsModalService,
    private changeDetectorRef: ChangeDetectorRef,
    private router: Router,
    private customerBillingPlanService: CustomerBillingPlanService,
    private referenceDataService: ReferenceDataService
  ) {
    this.log = this.loggerFactory.getLogger('');

    this.originTemplate = JSON.parse(JSON.stringify(this.queryTemplates));
  }

  ngOnInit(): void {
    this.getFirstLevel();
  }

  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }

  refreshData() {
    this.loadList(this.event);
  }

  getFirstLevel() {
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      // console.log('res', res);
      this.orgNameList = res;
    });
  }

  loadList(event: any) {
    const page = event.page;
    const params: any = this.loadProperty(page);
    this.event = event;
    this.loading = true;
    this.footer.showTotalElements = true;
    // console.log(params);
    this.customerBillingPlanService
      .getAccountList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe((res) => {
        this.dataList = res.data.records;
        this.totalNumber = res.data.total;
        this.currentNumber = this.dataList ? this.dataList.length : 0;
      });
  }

  loadProperty(page: any): any {
    const { rules } = page.filter;

    const filter = rules.map((rule: { field: any; data: any }) => {
      const { field, data } = rule;
      if (data) {
        return { [field]: data };
      }
    });

    return {
      ...Object.assign({}, ...filter),
      pageIndex: page.pageIndex,
      pageSize: page.pageSize
    };
  }

  // 重置查询模板
  reset() {
    this.queryTemplates = this.originTemplate;
  }

  getTotal() {}

  turnPage() {
    return this.ngxQuery.validateQuery();
  }

  charge(row: any) {
    const initialState = { tenantId: row.tenantId, monetaryUnit: row.monetaryUnit };
    this.modalService.show(RechargeComponent, {
      initialState,
      ignoreBackdropClick: true,
      class: 'modal-display-table light-modal modal-lg-custom'
    });
    const onHidden = this.modalService.onHidden.subscribe((res: any) => {
      // console.log('弹窗保存关闭', res);
      this.ngxQuery.executeQuery();
      onHidden.unsubscribe();
    });
  }

  // goRuleList(row: any) {
  //   // sessionStorage.setItem('rules', JSON.stringify(this.event.query.query));
  //   this.router.navigate(['/customerDeviceBilling/customerBillingPlan/rules/' + row.tenantId]);
  // }
}
