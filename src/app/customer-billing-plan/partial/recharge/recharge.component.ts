import { Component, OnInit, Input } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { OssService } from '@app/shared/services/oss.service';
import { DomSanitizer } from '@angular/platform-browser';

import { LoggerFactory } from '@app/core/logger-factory.service';
import { Logger } from '@app/core/logger.service';
import { NzUploadFile } from 'ng-zorro-antd/upload';

import { BsModalRef } from 'ngx-bootstrap/modal';
import { ReferenceDataService } from '@app/shared/services/reference-data.service';
import { accountSetting } from '@app/account-settings/models/account-setting';
import { CustomerBillingPlanService } from '@app/customer-billing-plan/shared/customer-billing-plan.service';
import { regex } from '@app/shared/utils/regex';

@Component({
  selector: 'app-recharge',
  templateUrl: './recharge.component.html',
  styleUrls: ['./recharge.component.scss']
})
export class RechargeComponent implements OnInit {
  @Input() tenantId: string;
  @Input() monetaryUnit: string;
  log: Logger;
  saving = false;
  form: FormGroup;
  accountSetting = accountSetting;
  orgNameList: Array<any> = [];
  monetaryUnitList: Array<any> = [];
  loading = false;
  accept = '.png,.jpg,.pdf,.PDF';
  fileList: NzUploadFile[] = [];

  beforeUpload = (file: any) => {
    this.upload(file);
    this.fileList = [this.formatFile(file)];
    return false;
  };
  remove = () => {
    this.fileList = this.fileList.slice(0, this.fileList.length - 1);
  };

  constructor(
    public activeModal: BsModalRef,
    private formBuilder: FormBuilder,
    private referenceDataService: ReferenceDataService,
    private domSanitizer: DomSanitizer,
    private translate: TranslateService,
    private ossService: OssService,
    private loggerFactory: LoggerFactory,
    private customerBillingPlanService: CustomerBillingPlanService
  ) {
    this.buildForm();
    this.log = this.loggerFactory.getLogger('');
  }

  ngOnInit() {
    this.form.get('tenantId').patchValue(this.tenantId);
    if (this.monetaryUnit) {
      this.form.get('monetaryUnit').patchValue(this.monetaryUnit);
    }
    this.getFirstLevel();
    this.getRechargeUnit();
  }

  getFirstLevel() {
    this.referenceDataService.getFirstUserOrganizations().subscribe((res: any) => {
      this.orgNameList = res;
    });
  }

  getRechargeUnit() {
    const params = {
      type: 1
    };
    this.customerBillingPlanService.getRechargeUnit(params).subscribe((res: any) => {
      this.monetaryUnitList = res.data;
    });
  }

  /**
   * 上传文件
   */
  upload(file: any) {
    this.loading = true;
    this.ossService.uploadByOss(file, 'file').subscribe((res: any) => {
      this.form.get('collectionReceipt').patchValue(res);
      this.loading = false;
    });
  }

  submit() {
    if (this.saving) {
      return;
    }

    for (const i in this.form.controls) {
      if (this.form.controls[i]) {
        this.form.controls[i].markAsDirty();
        this.form.controls[i].updateValueAndValidity();
      }
    }
    const params = this.form.getRawValue();
    const file = params.collectionReceipt;
    if (!file) {
      this.log.info(this.translate.instant('请上传附件'));
      return;
    }
    if (this.form.invalid) {
      return;
    }
    params.arriveDate = new Date(params.arriveDate).valueOf();
    params.depositMoney = JSON.parse(params.depositMoney);
    this.saving = true;
    this.customerBillingPlanService
      .recharge(params)
      // .pipe(finalize(() => (this.saving = false)))
      .subscribe((res: any) => {
        if (!res.success) {
          this.translate.get(res.message).subscribe((res: string) => {
            this.log.error(res);
            this.saving = false;
          });
          return;
        }
        this.activeModal.hide();
        this.translate.get('新增成功').subscribe((res: string) => {
          this.log.success(res);
        });
        // this.saving = false;
      });
  }

  /**
   * 将File类型转换为NzUploadFile
   * @param file
   * @returns
   */
  formatFile(file: File): NzUploadFile {
    const fileItem: NzUploadFile = {
      uid: Math.random().toString(36).substring(2),
      name: file.name,
      status: 'success',
      percent: 0,
      size: file.size,
      type: file.type,
      originFileObj: file
    };

    fileItem.previewImgUrl = this.domSanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(file));
    return fileItem;
  }

  buildForm() {
    this.form = this.formBuilder.group({
      tenantId: [{ value: this.tenantId, disabled: true }, [Validators.required]],
      salesName: [null, [Validators.required]],
      depositMoney: [null, [Validators.required, Validators.pattern(regex.numberAndPoint)]],
      monetaryUnit: [this.monetaryUnit ? this.monetaryUnit : null, [Validators.required]],
      arriveDate: [null, [Validators.required]],
      collectionReceipt: [null, []],
      remark: [null, []]
    });
  }
}
