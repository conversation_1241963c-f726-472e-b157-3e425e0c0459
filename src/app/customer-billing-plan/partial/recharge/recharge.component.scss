.modal-body {
  width: 654px;
  max-height: calc(100vh - 120px) !important;
  padding: 25px !important;
}

.ant-form-item-label {
  text-align: left;
  // padding-left: 70px;
  // width: 32%;
}

.ant-col-sm-14 {
  width: 60.333333%;
}

.count {
  position: absolute;
  right: 0;
  text-align: right;
  font-weight: 400;
  color: #575e72;
  line-height: 12px;
  font-size: 12px;
  background-color: #fff;
}

textarea {
  margin-top: 2px;
}

.recharge-box {
  display: flex;
  justify-content: space-between;
  .recharge-input,
  .recharge-unit {
    width: 95%;
  }
}

:host ::ng-deep .avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

:host ::ng-deep {
  .ant-form-item-label {
    white-space: normal;
    overflow: visible;
  }
}
