<div class="modal-header">
  <h5 class="modal-title">{{ '充值' | translate }}</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <form nz-form [formGroup]="form" (ngSubmit)="submit()">
    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="tenantId">
        {{ '租户名称' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="tenantIdErrorTpl">
        <nz-select
          formControlName="tenantId"
          id="tenantId"
          nzPlaceHolder="{{ '请选择租户名称' | translate }}"
          nzShowSearch
          nzAllowClear
        >
          <nz-option
            *ngFor="let option of orgNameList"
            [nzLabel]="option.name | translate"
            [nzValue]="option.id"
          ></nz-option>
        </nz-select>
        <ng-template #tenantIdErrorTpl let-control>
          {{ '请选择租户名称' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="salesName">
        {{ '业务员姓名' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="salesNameErrorTpl">
        <input
          type="text"
          nz-input
          maxlength="100"
          formControlName="salesName"
          id="salesName"
          placeholder="{{ '请输入业务员姓名' | translate }}"
        />
        <ng-template #salesNameErrorTpl let-control>
          {{ '请输入业务员姓名' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="depositMoney">
        {{ '充值金额' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="9" [nzXs]="12" [nzErrorTip]="depositMoneyErrorTpl">
        <div class="recharge-box">
          <div class="recharge-input">
            <input type="text" nz-input maxlength="8" formControlName="depositMoney" id="depositMoney" />
            <ng-template #depositMoneyErrorTpl let-control>
              {{ '请输入充值金额' | translate }}
            </ng-template>
          </div>
        </div>
      </nz-form-control>
      <nz-form-control [nzSm]="9" [nzXs]="12" [nzErrorTip]="monetaryUnitErrorTpl" *ngIf="monetaryUnit === '-'">
        <div class="recharge-unit">
          <nz-select formControlName="monetaryUnit" id="monetaryUnit" nzShowSearch nzAllowClear>
            <nz-option
              *ngFor="let option of monetaryUnitList"
              [nzLabel]="option.name | translate"
              [nzValue]="option.name"
            ></nz-option>
          </nz-select>
          <ng-template #monetaryUnitErrorTpl let-control>
            {{ '请选择货币单位' | translate }}
          </ng-template>
        </div>
      </nz-form-control>
      <nz-form-control [nzSm]="9" [nzXs]="12" *ngIf="monetaryUnit !== '-'">
        <div class="recharge-unit">
          {{ monetaryUnit }}
        </div>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="arriveDate">
        {{ '到账时间' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24" [nzErrorTip]="arriveDateErrorTpl">
        <nz-date-picker
          class="w-full"
          [nzFormat]="accountSetting.dateFormat"
          nzPlaceHolder="{{ '请选择到账时间' | translate }}"
          formControlName="arriveDate"
          id="arriveDate"
          appLocalNzDateTime
        ></nz-date-picker>
        <ng-template #arriveDateErrorTpl let-control>
          {{ '请选择到账时间' | translate }}
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="collectionReceipt">
        {{ '附件' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <nz-upload
          nzListType="picture-card"
          [nzAccept]="accept"
          [nzBeforeUpload]="beforeUpload"
          [nzShowUploadList]="{ showPreviewIcon: false, showRemoveIcon: true, showDownloadIcon: false }"
          [nzRemove]="remove"
          [nzFileList]="fileList"
          [nzShowButton]="fileList.length < 1"
        >
          <span class="upload-icon" nz-icon [nzType]="loading ? 'upload' : 'plus'"></span>
        </nz-upload>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="remark">
        {{ '描述内容' | translate }}
      </nz-form-label>
      <nz-form-control [nzSm]="18" [nzXs]="24">
        <textarea
          rows="4"
          nz-input
          id="remark"
          formControlName="remark"
          [placeholder]="'请输入描述内容' | translate"
          maxlength="500"
        ></textarea>
        <div class="count">{{ form.get('remark').value ? form.get('remark').value.length : 0 }}/500</div>
      </nz-form-control>
    </nz-form-item>
  </form>
</div>
<div class="modal-footer">
  <button type="button" nz-button (click)="activeModal.hide()">
    {{ '取消' | translate }}
  </button>
  <button type="button" nz-button nzType="primary" [nzLoading]="saving" (click)="submit()">
    {{ '保存' | translate }}
  </button>
</div>
