import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../shared/shared.module';

import { LabelManagementRoutingModule } from './label-management-routing.module';
import { LabelListComponent } from './partial/label-list/label-list.component';
import { LabelAddComponent } from './partial/label-add/label-add.component';
import { LabelEditComponent } from './partial/label-edit/label-edit.component';

@NgModule({
  declarations: [LabelListComponent, LabelAddComponent, LabelEditComponent],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    LabelManagementRoutingModule
  ],
  entryComponents: [LabelAddComponent, LabelEditComponent]
})
export class LabelManagementModule {}
