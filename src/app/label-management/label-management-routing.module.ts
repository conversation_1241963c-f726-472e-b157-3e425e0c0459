import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';
import { LabelListComponent } from './partial/label-list/label-list.component';
import { LabelAddComponent } from './partial/label-add/label-add.component';
import { LabelEditComponent } from './partial/label-edit/label-edit.component';
const routes: Routes = RouteExtensions.withHost(
  { path: '', component: LabelListComponent, data: { title: '标签管理' } },
  []
);
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class LabelManagementRoutingModule {}
