import { Component, OnInit } from '@angular/core';

import { finalize } from 'rxjs/operators';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { LabelManagementService } from '@app/label-management/shared/label-management.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-label-add',
  templateUrl: './label-add.component.html',
  styleUrls: ['./label-add.component.scss']
})
export class LabelAddComponent implements OnInit {
  log: Logger;
  loading = false;

  labelName: string = undefined;
  errorMessage: string;
  constructor(
    public activeModal: BsModalRef,
    private loggerFactory: LoggerFactory,
    private labelManagementService: LabelManagementService,
    private translate: TranslateService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {}

  labelNameChange() {
    this.errorMessage = '';
  }

  saveLabel() {
    if (!this.labelName) {
      this.translate.get('标签不能为空').subscribe((res: string) => {
        this.errorMessage = res;
      });
      return;
    }
    this.labelManagementService
      .addLabelList(this.labelName)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.code === '200') {
            this.activeModal.hide();
          } else {
            if ((response.message = '标签名重复')) {
              this.translate.get('此标签名称已存在，请重新输入').subscribe((res: string) => {
                this.errorMessage = res;
              });
            }
          }
        },
        (error) => console.log('标签添加失败', error)
      );
  }
}
