import { Component, OnInit, AfterViewInit, ViewChild, ChangeDetectorRef } from '@angular/core';

import { finalize } from 'rxjs/operators';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { Logger } from '@core/logger.service';
import { Dialogs } from '@core/dialogs.service';
import { LoggerFactory } from '@core/logger-factory.service';

import { Router } from '@angular/router';
import { LabelAddComponent } from '../label-add/label-add.component';
import { LabelEditComponent } from '../label-edit/label-edit.component';
import { LabelManagementService } from '@app/label-management/shared/label-management.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-label-list',
  templateUrl: './label-list.component.html',
  styleUrls: ['./label-list.component.scss']
})
export class LabelListComponent implements OnInit {
  log: Logger;
  loading = false;

  title: string = '标签管理';
  labelName: string = '';
  labelList: Array<any> = [];

  constructor(
    private dialogs: Dialogs,
    private router: Router,
    private translate: TranslateService,
    private modalService: BsModalService,
    private loggerFactory: LoggerFactory,
    private labelManagementService: LabelManagementService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.loadLabelList('');
    this.translate.get('标签管理').subscribe((res: string) => {
      this.title = res;
    });
  }

  loadLabelList(param: string) {
    this.labelManagementService
      .getLabelList(param)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.code === '200') {
            this.labelList = response.data;
          } else {
            console.log('标签列表获取失败', response.msg);
          }
        },
        (error) => console.log('标签列表获取失败。', error)
      );
  }
  executeQuery(param: string) {
    this.loadLabelList(param);
  }
  resetQueryTemplate() {
    this.labelName = '';
    // this.labelName = undefined;
    this.executeQuery(this.labelName);
  }

  addLabel() {
    const modalRef: BsModalRef = this.modalService.show(LabelAddComponent, {
      class: 'modal-dialog-centered'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: any) => {
      this.loadLabelList('');
      onHidden.unsubscribe();
    });
  }
  editLabel(label: any) {
    // console.log('编辑标签');
    const initialState = { data: label };
    const modalRef: BsModalRef = this.modalService.show(LabelEditComponent, {
      initialState,
      // ？？？
      class: 'modal-dialog-centered'
    });
    const onHidden = this.modalService.onHidden.subscribe((val: any) => {
      this.loadLabelList('');
      onHidden.unsubscribe();
    });
  }
  deleteLabel(id: any) {
    // console.log('删除标签');
    this.translate.get('删除后使用该标签的数据将要清空标签信息，是否确定删除').subscribe((res: string) => {
      this.dialogs.confirm(res).subscribe(
        () => {
          this.labelManagementService
            .deleteLabelList(id)
            .pipe(finalize(() => (this.loading = false)))
            .subscribe(
              (response) => {
                if (response.code === '200') {
                  this.loadLabelList('');
                } else {
                  console.log('标签删除失败', response.msg);
                }
              },
              (error) => console.log('标签删除失败。', error)
            );
        },
        () => console.log(`取消删除`)
      );
    });
  }
}
