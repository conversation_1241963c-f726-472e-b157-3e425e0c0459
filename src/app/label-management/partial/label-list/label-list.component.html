<div class="m-portlet list_header">
  <div class="title">
    <h5>{{ title }}</h5>
  </div>
  <div class="query-toolbar">
    <nz-input-group [nzSuffix]="suffixIconSearch">
      <input type="text" [(ngModel)]="labelName" nz-input placeholder="{{ '请输入标签名称' | translate }}" />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <i *ngIf="labelName" class="pointer" nz-icon nzType="close" nzTheme="outline" (click)="resetQueryTemplate()"></i>
      <i
        class="pointer"
        nz-icon
        nzType="search"
        nzTheme="outline"
        class="pointer"
        (click)="executeQuery(labelName)"
      ></i>
    </ng-template>
    <ng-template #inputClearTpl></ng-template>
  </div>
</div>

<div class="m-portlet">
  <div class="label-body">
    <div class="label-card-new" (click)="addLabel()">
      <span translate>新建标签</span>
    </div>
    <div class="label-card" *ngFor="let label of labelList">
      <div class="label-card-content">
        <img src="/assets/font/label-icon.png" class="label-imgIcon" />
        <span class="label-name">{{ label.labelName }}</span>
        <img src="/assets/font/label-car.png" class="label-imgCar" />
      </div>
      <div class="label-card-button">
        <button class="button-edit" (click)="editLabel(label)">
          {{ '编辑' | translate }}
        </button>
        <button class="button-delete" (click)="deleteLabel(label.id)">
          {{ '删除' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
