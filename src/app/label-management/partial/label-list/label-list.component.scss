.list_header {
  display: grid;
  grid-template-columns: 20% 80%;
  align-items: center;
  padding: 30px;

  h5 {
    width: 64px;
    height: 14px;
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    color: #333333;
    line-height: 14px;
  }
  .query-toolbar {
    justify-self: end;
    display: flex;
    flex-direction: row;
    width: 355px;
  }
}

.label-body {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  padding: 16px;
}

.label-card-new {
  display: grid;
  height: 132px;
  width: 312px;
  margin: 16px;
  border: 1px dashed black;
  justify-items: center;
  align-items: center;
  cursor: pointer;
}

.label-card {
  display: grid;
  width: 312px;
  height: 132px;
  margin: 16px;
  background: #ffffff;
  border-radius: 4px;
  border-bottom: 1px solid rgba(96, 153, 255, 1);
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);

  .label-card-content {
    position: relative;
    width: 100%;
    height: 87px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .label-imgIcon {
      position: absolute;
      left: 19px;
    }
    .label-name {
      margin-left: 59px;
      font-size: 14px;
      font-family: PingFangSC, PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      color: #4777fd;
      line-height: 14px;
    }
    .label-imgCar {
      position: absolute;
      right: 16px;
    }
  }
  .label-card-button {
    width: 100%;

    button {
      cursor: pointer;
      height: 44px;
      width: 49%;
      background: #ffffff;
    }
    .button-edit {
      border-top: 1px solid rgba(151, 151, 151, 0.1);
      border-right: 1px solid rgba(151, 151, 151, 0.1);
      border-bottom: none;
      border-left: none;
    }
    .button-delete {
      border-top: 1px solid rgba(151, 151, 151, 0.1);
      border-left: none;
      border-bottom: none;
      border-right: none;
    }
  }
}
