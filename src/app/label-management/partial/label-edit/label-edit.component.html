<div class="modal-header">
  <h5 class="modal-title" style="color: #ffffff" translate>编辑</h5>
  <button type="button" class="close" (click)="activeModal.hide()" aria-label="Close" style="color: #ffffff">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="form-group m-form__group row">
    <label class="col-sm-2 col-form-label" translate>标签名称</label>
    <div class="col-sm-10">
      <input
        maxlength="100"
        type="text"
        placeholder="{{ '请输入1-100个字符的标签名称' | translate }}"
        class="form-control"
        [ngClass]="{ error: errorMessage }"
        [(ngModel)]="labelName"
      />
      <div>
        <span [ngStyle]="{ color: '#FF5473' }">{{ errorMessage }}</span>
      </div>
      <div class="edit-prompt">
        <img src="/assets/font/prompt.png" class="prompt-img" />
        <span class="prompt-text" translate>修改名称后，之前使用该标签的数据将随之更新</span>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button nz-button (click)="activeModal.hide()" translate>{{ '取消' | translate }}</button>
  <button nz-button nzType="primary" (click)="saveLabel()" translate>{{ '保存' | translate }}</button>
</div>
