import { Component, OnInit, Input } from '@angular/core';

import { finalize } from 'rxjs/operators';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { Logger } from '@core/logger.service';
import { LoggerFactory } from '@core/logger-factory.service';
import { LabelManagementService } from '@app/label-management/shared/label-management.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-label-edit',
  templateUrl: './label-edit.component.html',
  styleUrls: ['./label-edit.component.scss']
})
export class LabelEditComponent implements OnInit {
  @Input() data: any;
  log: Logger;
  loading = false;
  id: any;
  labelName: string = undefined;
  errorMessage: string;

  constructor(
    public activeModal: BsModalRef,
    private nzMessage: NzMessageService,
    private translate: TranslateService,
    private loggerFactory: LoggerFactory,
    private labelManagementService: LabelManagementService
  ) {
    this.log = this.loggerFactory.getLogger(``);
  }

  ngOnInit() {
    this.id = this.data.id;
    this.labelName = this.data.labelName;
  }

  saveLabel() {
    if (!this.labelName) {
      this.errorMessage = '标签不能为空';
      this.translate.get('标签不能为空').subscribe((res: string) => {
        this.errorMessage = res;
      });
      return;
    }
    this.labelManagementService
      .editLabelList(this.labelName, this.id)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (response) => {
          if (response.code === '200') {
            this.activeModal.hide();
            this.translate.get('标签编辑成功').subscribe((res: string) => {
              this.nzMessage.success(res);
            });
          } else {
            console.log('标签列表获取失败', response.msg);
          }
        },
        (error) => console.log('标签编辑失败', error)
      );
  }
}
