import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { WebApiResultResponse, PagingResponse } from '../../core/http/web-api-result-response';
import { QueryPageList, PagedList } from '@app/shared/models/type';

@Injectable({
  providedIn: 'root'
})
export class LabelManagementService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }

  // 获取标签列表
  getLabelList(labelName: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/labelList?labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 新增标签列表
  addLabelList(labelName: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/addLabel?labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 删除标签列表
  deleteLabelList(id: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/deleteLabel?id=` + id;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }

  // 编辑标签列表
  editLabelList(labelName: any, id: any): Observable<any> {
    const url = `glcrm-account-api/v1/api/label/updateLabel?id=` + id + `&labelName=` + labelName;
    return this.http.get(url).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
