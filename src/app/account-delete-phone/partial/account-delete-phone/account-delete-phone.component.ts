import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AccountDeletePhoneService } from '@app/account-delete-phone/shared/account-delete-phone.service';
import { regex } from '@app/shared/utils/regex';
import { NzMessageService } from 'ng-zorro-antd/message';
import { CustomValidators } from 'ngx-custom-validators';
@Component({
  selector: 'app-account-delete-phone',
  templateUrl: './account-delete-phone.component.html',
  styleUrls: ['./account-delete-phone.component.scss']
})
export class AccountDeletePhoneComponent implements OnInit {
  type: string = 'GetCode'; // GetCode / Verify / Delete / Success
  validateForm: FormGroup = this.fb.group({
    email: this.fb.control('', [
      Validators.required,
      CustomValidators.email,
      CustomValidators.rangeLength([6, 50]),
      Validators.pattern(regex.space)
    ])
  });
  validateForm1: FormGroup = this.fb.group({
    email: this.fb.control('', [
      Validators.required,
      CustomValidators.email,
      CustomValidators.rangeLength([6, 50]),
      Validators.pattern(regex.space)
    ]),
    code: this.fb.control('', [Validators.required])
  });
  time: number = 60;
  timing: any;
  constructor(
    private fb: FormBuilder,
    private accountDeletePhoneService: AccountDeletePhoneService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {}
  del(): void {
    this.accountDeletePhoneService
      .delAccount({
        email: this.validateForm1.get('email').value
      })
      .subscribe(
        () => {
          this.type = 'Success';
        },
        () => {
          this.message.create('error', `Operation failed!`);
        }
      );
  }
  getCode(isCurrent?: boolean): void {
    if (isCurrent) {
      this.time = 60;
    } else {
      this.validateForm1.get('email').setValue(this.validateForm.get('email').value);
    }
    this.stopTiming();
    this.accountDeletePhoneService
      .getCode({
        email: this.validateForm1.get('email').value,
        validEmail: true
      })
      .subscribe(
        () => {
          this.type = 'Verify';
          this.timing = setInterval(() => {
            this.time--;
            if (!this.time || this.time == 0) {
              this.stopTiming();
            }
          }, 1000);
        },
        () => {
          this.message.create('error', `Please confirm that you have entered the correct email!`);
        }
      );
  }
  verify(): void {
    this.accountDeletePhoneService
      .checkCode({
        email: this.validateForm1.get('email').value,
        code: this.validateForm1.get('code').value
      })
      .subscribe(
        () => {
          this.stopTiming();
          this.type = 'Delete';
        },
        () => {
          this.message.create('error', `Operation failed!`);
        }
      );
  }
  stopTiming() {
    if (this.timing) {
      clearInterval(this.timing);
      this.timing = null;
    }
  }
}
