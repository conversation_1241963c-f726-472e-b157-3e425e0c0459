<div class="phone-body">
  <div class="text-align-center">
    <img alt="" src="/assets/font/glcrm_logo.png" class="top-logo" />
  </div>

  <ng-container *ngIf="type === 'Success'; else elseTemplate">
    <div class="top-tip margin-top">Your account deletion request has been submitted.</div>
  </ng-container>
  <ng-template #elseTemplate>
    <div class="top-tip">This will delete your account.</div>
    <div class="top-desp">
      Once your account is deleted, you will no longer be able to log in with the current account. Please note that this
      action is irreversible.
    </div>
    <form nz-form [formGroup]="validateForm" (ngSubmit)="getCode()" *ngIf="type === 'GetCode'">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input correct email!">
          <input nzSize="large" formControlName="email" nz-input placeholder="Email" />
        </nz-form-control>
      </nz-form-item>

      <button nzSize="large" nz-button nzType="primary" nzBlock [disabled]="!validateForm.valid" nzShape="round">
        Confirm
      </button>
    </form>
    <form nz-form [formGroup]="validateForm1" (ngSubmit)="verify()" *ngIf="type === 'Verify'">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input correct Email!">
          <input nzSize="large" formControlName="email" nz-input placeholder="Email" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control nzErrorTip="Please input correct verification code!">
          <input nzSize="large" formControlName="code" nz-input placeholder="Verification Code" />
        </nz-form-control>
      </nz-form-item>
      <button
        class="margin-bottom-30"
        nzSize="large"
        nz-button
        nzType="primary"
        nzBlock
        [disabled]="!validateForm1.valid"
        nzShape="round"
      >
        Verify
      </button>
      <a nz-button nzType="link" nzBlock [disabled]="!!timing" (click)="getCode(true)">
        Resend Verification Code{{ time ? '(' + time + ')' : '' }}
      </a>
    </form>
    <div class="btn-del" *ngIf="type === 'Delete'">
      <button nzSize="large" (click)="del()" nz-button nzType="primary" nzBlock nzShape="round" nzDanger>Delete</button>
    </div>
  </ng-template>
</div>
