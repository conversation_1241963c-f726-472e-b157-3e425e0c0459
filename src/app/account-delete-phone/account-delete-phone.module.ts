import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountDeletePhoneRoutingModule } from './account-delete-phone-routing.module';
import { AccountDeletePhoneComponent } from './partial/account-delete-phone/account-delete-phone.component';
import { SharedModule } from '@app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [AccountDeletePhoneComponent],
  imports: [CommonModule, AccountDeletePhoneRoutingModule, SharedModule, FormsModule, ReactiveFormsModule]
})
export class AccountDeletePhoneModule {}
