import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { WebApiResultResponse } from '@app/core';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AccountDeletePhoneService extends WebApiResultResponse {
  constructor(private http: HttpClient) {
    super();
  }
  // 获取验证码
  getCode(params: any): Observable<any> {
    const url = `glcrm-app-api/v1/api/appUser/sendCode`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 检查验证码 code email
  checkCode(params: any): Observable<any> {
    const url = `glcrm-app-api/v1/api/appUser/checkCode`;
    return this.http.get(url, { params }).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
  // 注销 { email }
  delAccount(params: any): Observable<any> {
    const url = `glcrm-app-api/v1/api/appUser/deregister`;
    return this.http.post(url, params).pipe(map(super.handleSuccess), catchError(super.handleError));
  }
}
