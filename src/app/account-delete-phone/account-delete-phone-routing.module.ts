import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RouteExtensions } from '@app/core';
import { AccountDeletePhoneComponent } from './partial/account-delete-phone/account-delete-phone.component';

const routes: Routes = RouteExtensions.withHost(
  { path: 'account-delete', component: AccountDeletePhoneComponent, data: { title: '' } },
  []
);

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class AccountDeletePhoneRoutingModule {}
