server {
  listen       80;
  # listen 443;
  # ssl on;
  # ssl_certificate /etc/nginx/ssl/server.crt;
  # ssl_certificate_key /etc/nginx/ssl/server.key;

  server_name  localhost;

  #charset koi8-r;
  #access_log  /var/log/nginx/host.access.log  main;
  access_log off;

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

  #error_page  404              /404.html;

  # redirect server error pages to the static page /50x.html
  #
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}